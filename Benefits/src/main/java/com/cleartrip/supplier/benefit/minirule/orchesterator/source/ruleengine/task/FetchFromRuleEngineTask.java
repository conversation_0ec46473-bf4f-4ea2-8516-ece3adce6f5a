package com.cleartrip.supplier.benefit.minirule.orchesterator.source.ruleengine.task;

import com.cleartrip.supplier.benefit.minirule.orchesterator.source.ruleengine.dto.FareRuleDetailDTO;
import com.cleartrip.supplier.benefit.minirule.orchesterator.source.ruleengine.dto.FareRuleDetailRequest;
import com.cleartrip.supplier.benefit.minirule.orchesterator.source.ruleengine.dto.FareRuleDetailResponse;
import com.cleartrip.supplier.benefit.minirule.orchesterator.source.ruleengine.dto.RuleEngineRequestDTO;
import com.cleartrip.supplier.benefit.minirule.orchesterator.source.ruleengine.dto.RuleEngineResponseDTO;
import com.cleartrip.supplier.benefit.minirule.orchesterator.source.ruleengine.exception.MiniRuleOrchestratorException;
import com.cleartrip.supplier.benefit.minirule.repository.FareRuleRepository;
import com.cleartrip.supplier.benefit.minirule.utils.JsonUtil;
import com.cleartrip.utility.workflow.design.Task;
import org.apache.commons.lang3.StringUtils;
import org.openl.generated.beans.FareRule;
import org.openl.generated.beans.FareRuleConfigData;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class FetchFromRuleEngineTask implements Task<RuleEngineRequestDTO, FareRuleDetailResponse> {

    private final FareRuleRepository repository;

    public static final String YES = "YES";

    private static final String PATTERN = "dd/MM/yyyy";

    public FetchFromRuleEngineTask(FareRuleRepository repository) {
        this.repository = repository;
    }

    @Override
    public FareRuleDetailResponse run(RuleEngineRequestDTO request) {
        RuleEngineResponseDTO ruleEngineResponseDTO = getConfigData(request);
        FareRuleDetailRequest fareRuleDetailRequest = getFareRuleDetailRequest(ruleEngineResponseDTO);
        return getFareRuleDetailResponse(fareRuleDetailRequest);
    }

    private FareRuleDetailResponse getFareRuleDetailResponse(FareRuleDetailRequest request){
        FareRule[] fareRules = repository.getFareRuleData(String.valueOf(request.getId()), StringUtils.split(request.getComboFbc(), ",")[0]);
        if(fareRules.length == 0){
            throw new MiniRuleOrchestratorException("No fare Rules for Id => " + request.getId() + " and comboFbc => " + request.getComboFbc(), null);
        }

        List<FareRuleDetailDTO> fareRuleDetailDTOS = Arrays.stream(fareRules)
                .filter(fareRule -> YES.equalsIgnoreCase(fareRule.getEnabled()))
                .map(fareRule -> FareRuleDetailDTO.builder()
                        .Id(fareRule.getId())
                        .comboFbc(request.getComboFbc())
                        .journeyKey(request.getJourneyKey())
                        .paxType(fareRule.getPaxType())
                        .ruleType(fareRule.getRuleType())
                        .strategy(request.getStrategy())
                        .ldx(fareRule.getLdx())
                        .ldxTimeFrame(fareRule.getLdxTimeFrame())
                        .udx(fareRule.getUdx())
                        .udxTimeFrame(fareRule.getUdxTimeFrame())
                        .applicableRule(fareRule.getApplicableRule())
                        .applicableRuleAmount(fareRule.getAmount())
                        .currency(fareRule.getCurrency())
                        .permitted(fareRule.getPermitted().equalsIgnoreCase(YES))
                        .build()
                ).collect(Collectors.toList());

        return FareRuleDetailResponse.builder()
                .fareRuleList(fareRuleDetailDTOS)
                .build();
    }

    private FareRuleDetailRequest getFareRuleDetailRequest(RuleEngineResponseDTO response){
        return FareRuleDetailRequest.builder()
                .Id(response.getId())
                .strategy(response.getStrategy())
                .journeyKey(response.getJourneyKey())
                .comboFbc(response.getComoFbc())
                .build();
    }

    private RuleEngineResponseDTO getConfigData(RuleEngineRequestDTO request){
        FareRuleConfigData[] configData = repository.getFareRuleConfigData(request.getSupplier(), request.getAirline(),
                request.getFromCountry(), request.getToCountry(), request.getFromAirport(), request.getToAirport(),
                request.getFareCategory(), request.getFareSubCategory(),
                request.getFareGroupName(), request.getProductClass(),
                request.getBookingType(), request.getCountry(),
                request.getCabinClass(), request.getPaxType(), request.getPartnerId());

        if(configData.length == 0){
            throw new MiniRuleOrchestratorException("No FareRule Config Found for solutionId:comboFbc:Supplier:PaxType=>" +
                    request.getSolutionId() + ":" + request.getComoFbc() + ":" + request.getComoFbc(), null);
        }

        List<RuleEngineResponseDTO> ruleEngineResponseDTOList =  Arrays.stream(configData)
                .map(data -> RuleEngineResponseDTO.builder()
                            .journeyKey(request.getJourneyKey())
                            .comoFbc(request.getComoFbc())
                            .enabled(data.getEnabled())
                            .Id(Integer.valueOf(data.getFareRuleId()))
                            .strategy(data.getStrategy())
                            .startDate(JsonUtil.formatDate(data.getStartDate()))
                            .build()
                ).collect(Collectors.toList());

        return enabledFilter.andThen(dateFilter).apply(ruleEngineResponseDTOList);
    }

    private final Function<List<RuleEngineResponseDTO>, List<RuleEngineResponseDTO>> enabledFilter = ruleEngineResponseDTOS -> ruleEngineResponseDTOS.stream()
            .filter(ruleEngineResponseDTO -> YES.equalsIgnoreCase(ruleEngineResponseDTO.getEnabled()))
            .collect(Collectors.toList());


    private final Function<List<RuleEngineResponseDTO>, RuleEngineResponseDTO> dateFilter = ruleEngineResponseDTOS -> ruleEngineResponseDTOS.stream()
            .filter(ruleEngineResponseDTO -> (new Date()).after(ruleEngineResponseDTO.getStartDate()))
            .max(Comparator.comparing(RuleEngineResponseDTO::getStartDate))
            .orElseThrow(() -> new MiniRuleOrchestratorException("Error filtering FareRule with Highest Date from Rule Engine", null));
}
