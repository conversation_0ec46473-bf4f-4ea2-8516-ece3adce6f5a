package com.cleartrip.supplier.benefit.benefits.domain;

import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.benefit.benefits.domain.dto.BenefitType;
import com.cleartrip.supplier.benefit.benefits.domain.dto.RequestType;
import com.cleartrip.supplier.benefit.benefits.domain.dto.BenefitsDataRequest;
import com.cleartrip.supplier.benefit.benefits.domain.dto.BenefitsDomainResponse;
import com.cleartrip.supplier.benefit.benefits.domain.dto.SolutionMetaData;
import com.cleartrip.supplier.benefit.benefits.util.NewRelicUtil;
import com.cleartrip.supplier.benefit.benefits.util.StatsUtil;
import com.cleartrip.supplier.benefit.config.BenefitsConfigContainer;
import com.cleartrip.supplier.benefit.constant.SisConfigKeys;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import javax.inject.Named;
import json.response.benefit.baggage.BaggageResponse;
import json.response.benefit.fareBenefit.FareBenefitResponse;
import json.response.benefit.farerule.FareRuleResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Singleton
@Slf4j
public class BenefitsDomain {

    private final String FARERULES_API_NAME = "ONESUPPLY_FARERULES";
    private final String FAREBENEFITS_API_NAME = "ONESUPPLY_FAREBENEFITS";
    private final String BAGGAGE_API_NAME = "ONESUPPLY_BAGGAGE";
    private final String TASK_NAME = "SC_FETCH_BENEFITS";

    private FareRuleHelper fareRuleHelper;
    private FareBenefitHelper fareBenefitHelper;
    private BaggageHelper baggageHelper;
    private ExecutorService executorService;
    private Map<RequestType, BiFunction<BenefitsDataRequest, Context, BenefitsDomainResponse>> getBenfitsMap;
    private BenefitsConfigContainer properties;
    private StatsHelper statsHelper;
    private StatsUtil statsUtil;

    @Inject
    public BenefitsDomain(FareRuleHelper fareRuleHelper, FareBenefitHelper fareBenefitHelper,
        BaggageHelper baggageHelper, @Named("benefitsExecutorService") ExecutorService executorService,
        BenefitsConfigContainer properties, @Named("benefitsStatsHelper") StatsHelper statsHelper,
        StatsUtil statsUtil) {
        this.fareRuleHelper = fareRuleHelper;
        this.fareBenefitHelper = fareBenefitHelper;
        this.baggageHelper = baggageHelper;
        this.executorService = executorService;
        this.properties = properties;
        this.statsHelper = statsHelper;
        this.statsUtil = statsUtil;
        this.getBenfitsMap = initialiseMap();
    }

    private Map<RequestType, BiFunction<BenefitsDataRequest, Context, BenefitsDomainResponse>> initialiseMap() {
        Map<RequestType, BiFunction<BenefitsDataRequest, Context, BenefitsDomainResponse>> benefitMap = new HashMap<>();
        benefitMap.put(RequestType.ADAPTION, adaptedBenefits);
        benefitMap.put(RequestType.ORCHESTRATION, orchestratedBenefits);
        return benefitMap;
    }

    private byte[] getRequest(BenefitsDataRequest benefitsDataRequest, BenefitType benefitType) {
        try {
            return statsUtil.getObjectInByte(benefitsDataRequest.getRequiredBenefitMap().get(benefitType));
        } catch (Exception e) {
            return "".getBytes();
        }
    }

    private int getTimeoutInMillis(BenefitsDataRequest benefitsDataRequest) {
         return benefitsDataRequest.isIOCallAllowed() ? properties.getIntPropertyValue(SisConfigKeys.ONESUPPLY_BULK_BENEFITS_IO_CALL_TIMEOUT_IN_MILLIS, 9000)
                                                      : properties.getIntPropertyValue(SisConfigKeys.ONESUPPLY_BULK_BENEFITS_TIMEOUT_IN_MILLIS, 3000);
    }

    private <T> T fetchBenefit(Future<T> future, BenefitsDataRequest benefitsDataRequest, Context context, long startTime, BenefitType benefitType, String apiName, String taskName) {
        try {
            T response = future.get();
            Map<BenefitType, List<SolutionMetaData>> requiredBenefitMap = benefitsDataRequest.getRequiredBenefitMap();
            List<SolutionMetaData> benefits = (requiredBenefitMap != null) ? requiredBenefitMap.get(benefitType) : null;
            if (benefits == null || benefits.isEmpty()) {
                return response;
            }
            CompletableFuture.runAsync(() -> NewRelicUtil.sendEvent(benefitsDataRequest, null, startTime, response, benefitType, context));
            return response;
        } catch (Exception e) {
            log.error("Failed to fetch {}", benefitType, e);
            CompletableFuture.runAsync(() -> NewRelicUtil.sendEvent(benefitsDataRequest, e, startTime, null, benefitType, context));
            return null;
        }
    }

    private BiFunction<BenefitsDataRequest, Context, BenefitsDomainResponse> adaptedBenefits = (benefitsDataRequest, context) -> {
        final long startTime = System.currentTimeMillis();
        int TIMEOUT_IN_MILLIS = getTimeoutInMillis(benefitsDataRequest);
        List<Callable<Object>> tasks = Arrays.asList(
            () -> fareRuleHelper.adaptFareRules(benefitsDataRequest),
            () -> fareBenefitHelper.adaptFareBenefits(benefitsDataRequest),
            () -> baggageHelper.adaptBaggage(benefitsDataRequest)
        );

        List<Future<Object>> futures;
        try {
            futures = executorService.invokeAll(tasks, TIMEOUT_IN_MILLIS, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("Task execution interrupted", e);
            return BenefitsDomainResponse.builder().build();
        }

        FareRuleResponse fareRuleResponse = (FareRuleResponse) fetchBenefit(futures.get(0), benefitsDataRequest, context, startTime, BenefitType.FARE_RULE, FARERULES_API_NAME, TASK_NAME);
        FareBenefitResponse fareBenefitResponse = (FareBenefitResponse) fetchBenefit(futures.get(1), benefitsDataRequest, context, startTime, BenefitType.FARE_BENEFIT, FAREBENEFITS_API_NAME, TASK_NAME);
        BaggageResponse baggageResponse = (BaggageResponse) fetchBenefit(futures.get(2), benefitsDataRequest, context, startTime, BenefitType.BAGGAGE, BAGGAGE_API_NAME, TASK_NAME);

        return BenefitsDomainResponse.builder()
                .fareRuleResponse(fareRuleResponse)
                .fareBenefitsResponse(fareBenefitResponse)
                .baggageResponse(baggageResponse)
                .build();
    };

    private BiFunction<BenefitsDataRequest, Context, BenefitsDomainResponse> orchestratedBenefits = (benefitsDataRequest, context) -> {
        final long startTime = System.currentTimeMillis();
        int TIMEOUT_IN_MILLIS = getTimeoutInMillis(benefitsDataRequest);
        List<Callable<Object>> tasks = Arrays.asList(
            () -> fareRuleHelper.fetchFareRules(benefitsDataRequest, context),
            () -> fareBenefitHelper.fetchFareBenefits(benefitsDataRequest),
            () -> baggageHelper.fetchBaggage(benefitsDataRequest)
        );

        List<Future<Object>> futures;
        try {
            futures = executorService.invokeAll(tasks, TIMEOUT_IN_MILLIS, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.error("Task execution interrupted", e);
            return BenefitsDomainResponse.builder().build();
        }

        FareRuleResponse fareRuleResponse = (FareRuleResponse) fetchBenefit(futures.get(0), benefitsDataRequest, context, startTime, BenefitType.FARE_RULE, FARERULES_API_NAME, TASK_NAME);
        FareBenefitResponse fareBenefitResponse = (FareBenefitResponse) fetchBenefit(futures.get(1), benefitsDataRequest, context, startTime, BenefitType.FARE_BENEFIT, FAREBENEFITS_API_NAME, TASK_NAME);
        BaggageResponse baggageResponse = (BaggageResponse) fetchBenefit(futures.get(2), benefitsDataRequest, context, startTime, BenefitType.BAGGAGE, BAGGAGE_API_NAME, TASK_NAME);

        return BenefitsDomainResponse.builder()
                .fareRuleResponse(fareRuleResponse)
                .fareBenefitsResponse(fareBenefitResponse)
                .baggageResponse(baggageResponse)
                .build();
    };

    public BenefitsDomainResponse getBenefits(Map<RequestType, BenefitsDataRequest> domainRequest, Context context) {
        List<BenefitsDomainResponse> domainResponses = domainRequest.entrySet().stream()
                .map(entry -> getBenfitsMap.get(entry.getKey())
                        .apply(entry.getValue(), context))
                .collect(Collectors.toList());
        return domainResponses.stream()
                .reduce(BenefitsDomainResponse.builder()
                                .fareBenefitsResponse(FareBenefitResponse.builder()
                                        .fareBenefitDetails(new HashMap<>())
                                        .build())
                                .baggageResponse(BaggageResponse.builder()
                                        .baggageDetails(new HashMap<>())
                                        .build())
                                .fareRuleResponse(FareRuleResponse.builder()
                                        .fareRuleDetails(new HashMap<>())
                                        .build())
                                .build(),
                        (finalResponse, currResponse) -> {
                    mergeSolutionResponses(finalResponse, currResponse);
                    return finalResponse;
                });
    }

    private void mergeSolutionResponses(BenefitsDomainResponse finalResponse, BenefitsDomainResponse currResponse) {
        if(ObjectUtils.allNotNull(finalResponse, currResponse)){
            if (ObjectUtils.allNotNull(finalResponse.getFareBenefitsResponse(), currResponse.getFareBenefitsResponse())
                    && MapUtils.isNotEmpty(currResponse.getFareBenefitsResponse().getFareBenefitDetails())) {
                finalResponse.getFareBenefitsResponse().getFareBenefitDetails()
                        .putAll(currResponse.getFareBenefitsResponse().getFareBenefitDetails());
            }
            if (ObjectUtils.allNotNull(finalResponse.getBaggageResponse(), currResponse.getBaggageResponse())
                    && MapUtils.isNotEmpty(currResponse.getBaggageResponse().getBaggageDetails())) {
                finalResponse.getBaggageResponse().getBaggageDetails()
                        .putAll(currResponse.getBaggageResponse().getBaggageDetails());
            }
            if (ObjectUtils.allNotNull(finalResponse.getFareRuleResponse(), currResponse.getFareRuleResponse())
                    && MapUtils.isNotEmpty(currResponse.getFareRuleResponse().getFareRuleDetails())) {
                finalResponse.getFareRuleResponse().getFareRuleDetails()
                        .putAll(currResponse.getFareRuleResponse().getFareRuleDetails());
            }
        }

    }



}
