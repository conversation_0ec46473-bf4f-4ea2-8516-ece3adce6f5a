package com.cleartrip.supplier.benefit.benefits.domain;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.supplier.benefit.baggage.application.adapter.BaggageApplicationRequestAdapter;
import com.cleartrip.supplier.benefit.baggage.application.adapter.BaggageApplicationResponseAdapter;
import com.cleartrip.supplier.benefit.baggage.domain.model.response.BaggageDomainResponse;
import com.cleartrip.supplier.benefit.baggage.domain.service.BaggageDomainHelper;
import com.cleartrip.supplier.benefit.benefits.domain.adapters.EmptyBaggageHandler;
import com.cleartrip.supplier.benefit.benefits.domain.adapters.FlightSolutionBaggageAdapter;
import com.cleartrip.supplier.benefit.benefits.domain.dto.BenefitType;
import com.cleartrip.supplier.benefit.benefits.domain.dto.BenefitsDataRequest;
import com.cleartrip.supplier.benefit.benefits.domain.dto.SolutionMetaData;
import com.cleartrip.supplier.benefit.benefits.util.NewRelicUtil;
import com.cleartrip.supplier.benefit.benefits.util.SolutionKeyUtil;
import com.google.inject.Inject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.inject.Named;

import com.google.inject.Singleton;
import json.response.benefit.baggage.BaggageResponse;
import json.response.benefit.baggage.FlightBaggage;
import json.response.benefit.baggage.PaxBaggage;
import json.response.benefit.baggage.SegmentBaggage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class BaggageHelper {
  private final BaggageApplicationRequestAdapter baggageDomainRequestAdapter;
  private final BaggageDomainHelper baggageDomainHelper;
  private final BaggageApplicationResponseAdapter baggageDomainResponseAdapter;
  private final FlightSolutionBaggageAdapter flightSolutionBaggageAdapter;
  private final EmptyBaggageHandler emptyBaggageHandler;
  private final NewRelicHelper newRelicHelper;
  private final SolutionKeyUtil solutionKeyUtil;

  @Inject
  public BaggageHelper(BaggageApplicationRequestAdapter baggageDomainRequestAdapter,
      BaggageDomainHelper baggageDomainHelper,
      BaggageApplicationResponseAdapter baggageDomainResponseAdapter,
      FlightSolutionBaggageAdapter flightSolutionBaggageAdapter,
      EmptyBaggageHandler emptyBaggageHandler,
      @Named("baggageNewRelicHelper") NewRelicHelper newRelicHelper,
      SolutionKeyUtil solutionKeyUtil) {
    this.baggageDomainRequestAdapter = baggageDomainRequestAdapter;
    this.baggageDomainHelper = baggageDomainHelper;
    this.baggageDomainResponseAdapter = baggageDomainResponseAdapter;
    this.flightSolutionBaggageAdapter = flightSolutionBaggageAdapter;
    this.emptyBaggageHandler = emptyBaggageHandler;
    this.newRelicHelper = newRelicHelper;
    this.solutionKeyUtil = solutionKeyUtil;
  }


  private boolean isEmptyPaxBaggage(List<PaxBaggage> paxBaggageList) {
    if (paxBaggageList == null || paxBaggageList.isEmpty()) {
      return true;
    }
    return paxBaggageList.stream().allMatch(paxBaggage ->
        paxBaggage.getBaggages() == null || paxBaggage.getBaggages().isEmpty()
    );
  }

  public Map<String, List<FlightBaggage>> getEmptyBaggageEntries(BaggageDomainResponse baggageDomainResponse) {
    Map<String, List<FlightBaggage>> emptyBaggageEntries = new HashMap<>();

    baggageDomainResponse.getBaggageDetails().forEach((key, flightBaggageList) -> {
      try {
        //TODO: commented this code as we have to override data in case of config or supplier call
        boolean hasEmptyBaggage = flightBaggageList.stream()
            .anyMatch(flightBaggage -> flightBaggage.getSegBaggageInfo().stream()
                .anyMatch(segmentBaggage -> isEmptyPaxBaggage(segmentBaggage.getPaxBaggage())));

        if (hasEmptyBaggage) {
          emptyBaggageEntries.put(key, flightBaggageList);
        }
      } catch (Exception e) {
        log.error("Failed to get empty baggage entries for key: {}", key, e);
      }
    });

    return emptyBaggageEntries;
  }

  public BaggageResponse fetchBaggage(BenefitsDataRequest domainRequest) {
    try {
      BaggageDomainResponse domainResponse = flightSolutionBaggageAdapter.convert(domainRequest);

      Map<String, List<FlightBaggage>> emptyBaggageEntries = getEmptyBaggageEntries(domainResponse);
      BaggageDomainResponse emptyBaggageDomainResponse = BaggageDomainResponse.builder().baggageDetails(emptyBaggageEntries).build();
      BaggageDomainResponse updatedBaggageEntriesUsingStrategies = emptyBaggageHandler.handleEmptyBaggageEntries(domainRequest, emptyBaggageDomainResponse);
      for (Entry<String, List<FlightBaggage>> entry : updatedBaggageEntriesUsingStrategies.getBaggageDetails().entrySet()) {
        domainResponse.getBaggageDetails().put(entry.getKey(), entry.getValue());
      }

      return baggageDomainResponseAdapter.toBaggageResponse(domainResponse, domainRequest);
    } catch (Exception e) {
      log.error(e.getMessage());
      return BaggageResponse.builder().build();
    }
  }

  public BaggageResponse adaptBaggage(BenefitsDataRequest domainRequest) {
    try {
      BaggageDomainResponse domainResponse = flightSolutionBaggageAdapter.convert(domainRequest);
      return baggageDomainResponseAdapter.toBaggageResponse(domainResponse, domainRequest);
    } catch (Exception e) {
      log.error(e.getMessage());
      return BaggageResponse.builder().build();
    }
  }

}
