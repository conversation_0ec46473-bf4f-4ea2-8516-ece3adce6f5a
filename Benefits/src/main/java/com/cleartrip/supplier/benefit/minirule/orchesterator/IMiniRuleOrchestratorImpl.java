package com.cleartrip.supplier.benefit.minirule.orchesterator;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.benefit.minirule.monitoring.NewRelicUtil;
import com.cleartrip.supplier.benefit.minirule.monitoring.StatsUtil;
import com.cleartrip.supplier.benefit.minirule.orchesterator.adapter.farerules.MiniRuleWorkflowResponseAdapter;
import com.cleartrip.supplier.benefit.minirule.orchesterator.models.request.farerule.MiniRuleOrchestratorRequest;
import com.cleartrip.supplier.benefit.minirule.orchesterator.models.response.farerule.MiniRuleOrchestratorResponse;
import com.cleartrip.supplier.benefit.minirule.utils.JsonUtil;
import com.cleartrip.supplier.benefit.minirule.workflow.MiniRuleWorkflow;
import com.cleartrip.supplier.benefit.minirule.workflow.dto.response.farerules.MiniRuleWorkflowResponse;
import com.cleartrip.supplier.benefit.minirule.workflow.generator.MiniRuleWorkflowGenerator;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Slf4j
public class IMiniRuleOrchestratorImpl implements IMIniRuleOrchestrator {

    private final ExecutorService executorService;

    private final MiniRuleWorkflowGenerator workflowGenerator;

    private final MiniRuleWorkflowResponseAdapter responseAdapter;

    private final NewRelicHelper newRelicHelper;
    private final StatsHelper statsHelper;

    private final StatsUtil statsUtil;

    private final JsonUtil util;

    @Inject
    public IMiniRuleOrchestratorImpl(@Named("fareRuleOrchestratorExecutorService") ExecutorService executorService,
                                     MiniRuleWorkflowGenerator workflowGenerator,
                                     MiniRuleWorkflowResponseAdapter responseAdapter,
                                     @Named("fareRuleNewRelicHelper") NewRelicHelper newRelicHelper,
                                     @Named("benefitsStatsHelper") StatsHelper statsHelper,
                                     StatsUtil statsUtil,
                                     @Named("benefitsJsonUtil") JsonUtil util) {
        this.executorService = executorService;
        this.workflowGenerator = workflowGenerator;
        this.responseAdapter = responseAdapter;
        this.newRelicHelper = newRelicHelper;
        this.statsHelper = statsHelper;
        this.statsUtil = statsUtil;
        this.util = util;
    }

    //TODO: prevent extra monitoring calls and propagate exception for single push
    @Override
    public MiniRuleOrchestratorResponse orchestrate(MiniRuleOrchestratorRequest request) {
        long startTime = System.currentTimeMillis();
        List<MiniRuleWorkflowResponse> workflowResponses = new ArrayList<>();
        List<Callable<MiniRuleWorkflowResponse>> workflowTasks = new ArrayList<>();
        List<Future<MiniRuleWorkflowResponse>> futures;
        try{
            List<MiniRuleWorkflow> workFlowRequests = workflowGenerator.generate(request);
            workFlowRequests.forEach(miniRuleWorkflow -> workflowTasks.add(miniRuleWorkflow::process));
            futures = executorService.invokeAll(workflowTasks);
            for(int idx = 0; idx < futures.size(); idx++) {
                MiniRuleWorkflow workflow = workFlowRequests.get(idx);
                try {
                    Future<MiniRuleWorkflowResponse> future = futures.get(idx);
                    MiniRuleWorkflowResponse workflowResponse = future.get();
                    workflowResponses.add(workflowResponse);
                } catch (Exception e) {
                    log.error("Exception while executing fare rules workflow task for {} , exception {} at {}", util.writeValueAsString(request), e.getMessage(), e.getCause().getStackTrace());
                    CompletableFuture.runAsync(() -> newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleOrchestratorLayerParams(workflow.getContext(),
                            startTime,
                            request.getContext(),
                            Objects.isNull(e.getCause()) ? Optional.of(e) : Optional.of(e.getCause())))
                    );
                }
            }
        }
        catch(Exception e){
            log.error("Exception while executing fare benefit workflow tasks for {} , exception {} at {}", util.writeValueAsString(request), e.getMessage(), e.getCause().getStackTrace());
            CompletableFuture.runAsync(() -> newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleOrchestratorLayerParams(null,
                    startTime,
                    request.getContext(),
                    Optional.of(e)))
            );
        }
        return responseAdapter.convert(request, workflowResponses);
    }


    private void pushToMonitoring(long start, long end, Object response, int statusCode, Context monitoringContext, Object request) {
        try {
            statsHelper.pushToStats(start,
                    end,
                    statsUtil.getObjectInByte(response),
                    statusCode,
                    "MINI_RULES_SUPPLY_CORE",
                    monitoringContext,
                    statsUtil.getObjectInByte(request),
                    "SUPPLY_CORE_ORCHESTRATOR",
                    "MINI_RULES",
                    ApiStatsDto.HttpMethod.POST
            );
        } catch (Exception exception) {
            log.error("Exception while pushing to stats for fareRules request {} is {}", request, exception.getMessage());
        }
    }
}
