package com.cleartrip.supplier.benefit.farebenefits.orchestration.source.ruleEngine;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.IFareBenefitsOrchestrator;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.dto.FareBenefitsOrchestratorRequest;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.dto.FareBenefitsOrchestratorResponse;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.dto.SolutionDetailsOrchestratorDto;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.dto.SolutionWiseFareBenefitsOrchestratorDto;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.source.ruleEngine.workflow.FareBenefitWorkflow;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.source.ruleEngine.workflow.FareBenefitsWorkflowGenerator;
import com.cleartrip.supplier.benefit.minirule.monitoring.NewRelicParams;
import com.cleartrip.supplier.benefit.minirule.monitoring.StatsUtil;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class FareBenefitsOrchestratorImpl implements IFareBenefitsOrchestrator {

    private final FareBenefitsWorkflowGenerator fareBenefitsWorkflowGenerator;
    private final StatsHelper statsHelper;
    private final StatsUtil statsUtil;
    private final NewRelicHelper newRelicHelper;

    @Inject
    public FareBenefitsOrchestratorImpl(FareBenefitsWorkflowGenerator fareBenefitsWorkflowGenerator,
                                        @Named("benefitsStatsHelper") StatsHelper statsHelper,
                                        StatsUtil statsUtil,
                                        @Named("fareBenefitsNewRelicHelper") NewRelicHelper newRelicHelper) {
        this.fareBenefitsWorkflowGenerator = fareBenefitsWorkflowGenerator;
        this.statsHelper = statsHelper;
        this.statsUtil = statsUtil;
        this.newRelicHelper = newRelicHelper;
    }

    @Override
    public FareBenefitsOrchestratorResponse orchestrate(FareBenefitsOrchestratorRequest input) {
        long startTime = System.currentTimeMillis();
        List<SolutionWiseFareBenefitsOrchestratorDto> solutionWiseFareBenefitsOrchestratorDtoList = new ArrayList<>();
        try {
            List<FareBenefitWorkflow> generatedWorkflows = fareBenefitsWorkflowGenerator.generate(input);
            solutionWiseFareBenefitsOrchestratorDtoList = generatedWorkflows.stream()
                    .flatMap(fareBenefitWorkflow -> fareBenefitWorkflow.process()
                            .stream())
                    .collect(Collectors.toList());
            log.info("Time taken by Fare Benefits:{} for {} solution workflows", System.currentTimeMillis() - startTime, generatedWorkflows.size());
            pushToMonitoring(startTime, System.currentTimeMillis(), solutionWiseFareBenefitsOrchestratorDtoList, 200, input.getMonitoringContext(), input.getSolutionDetailsOrchestratorDtoList(),null);
        } catch (Exception ex) {
            pushToMonitoring(startTime, System.currentTimeMillis(), null, 500, input.getMonitoringContext(), input.getSolutionDetailsOrchestratorDtoList(), ex);
            log.error("Exception while executing fare benefit workflow tasks for {} , exception {} at {}", input, ex.getClass().getName(), ex.getStackTrace());
        }


        return FareBenefitsOrchestratorResponse.builder().solutionWiseFareBenefitsOrchestratorDtoList(solutionWiseFareBenefitsOrchestratorDtoList).build();
    }

    private void pushToMonitoring(long start, long end, List<SolutionWiseFareBenefitsOrchestratorDto> response, int statusCode, Context monitoringContext, List<SolutionDetailsOrchestratorDto> request, Exception exception) {
        try {
            statsHelper.pushToStats(start, end, statsUtil.getObjectInByte(Objects.nonNull(exception) ? exception : response), statusCode, "FareBenefitsAPI", monitoringContext, statsUtil.getObjectInByte(request), "FARE_BENEFITS", "FARE_BENEFITS", ApiStatsDto.HttpMethod.POST);

            Map<String, Object> newRelicData = new HashMap<>();

            newRelicData.put(NewRelicParams.TIME_TAKEN.name(), end - start);
            newRelicData.put(NewRelicParams.STATUS.name(), statusCode);
            if(Objects.nonNull(exception)) {
                newRelicData.put("ERROR", exception);
            }
            newRelicData.put(NewRelicParams.SUPPLIER.name(), request.size() > 0 ? request.get(0).getSupplier() : "UNKNOWN");
            newRelicData.put(NewRelicParams.ITINERARY_ID.name(), monitoringContext.getItineraryId());
            newRelicData.put("LAYER", "ORCHESTRATOR");

            newRelicHelper.pushToNewRelic(newRelicData);
        } catch (Exception ex) {
            log.error("Exception while pushing to stats - {}", ex.getMessage());
        }
    }
}
