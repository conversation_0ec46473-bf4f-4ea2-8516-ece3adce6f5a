package com.cleartrip.supplier.benefit.config;


import com.air.sis.rule.BaggageSourceAndConfigRuleEngine;
import com.air.sis.rule.FareBenefitRuleEngine;
import com.cleartrip.air.application.ConfigManager;
import com.cleartrip.air.application.models.BucketRegistrationCriteria;
import com.cleartrip.air.application.models.Datatype;
import com.cleartrip.air.application.models.DecoderConfig;
import com.cleartrip.air.application.models.MySQLConnectionParams;
import com.cleartrip.air.application.models.MySQLRepositoryConfig;
import com.cleartrip.air.application.models.RepositoryConfig;
import com.cleartrip.air.entity.decoders.factory.DecoderType;
import com.cleartrip.air.entity.models.BucketDataWrapper;
import com.cleartrip.air.listener.IListener;
import com.cleartrip.air.themis.client.exception.APIExecutionFailedException;
import com.cleartrip.air.themis.client.exception.ResourceNotFoundException;
import com.cleartrip.air.themis.client.service.ThemisClientService;
import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.starter.utility.BucketNameCreator;
import com.cleartrip.supplier.benefit.baggage.domain.service.BaggageDomainHelper;
import com.cleartrip.supplier.benefit.baggage.domain.service.impl.BaggageDomainHelperImpl;
import com.cleartrip.supplier.benefit.baggage.orchesterator.BaggageOrchestratorImpl;
import com.cleartrip.supplier.benefit.baggage.orchesterator.IBaggageOrchestrator;
import com.cleartrip.supplier.benefit.benefits.invoker.SearchModuleInvoker;
import com.cleartrip.supplier.benefit.benefits.util.NewRelicUtil;
import com.cleartrip.supplier.benefit.config.listener.PropertiesListener;
import com.cleartrip.supplier.benefit.config.models.MonitoringDetails;
import com.cleartrip.supplier.benefit.config.models.MySqlConf;
import com.cleartrip.supplier.benefit.farebenefits.domain.IFareBenefitsDomainService;
import com.cleartrip.supplier.benefit.farebenefits.domain.impl.FareBenefitsDomainServiceImpl;
import com.cleartrip.supplier.benefit.farebenefits.enricher.IFareBenefitEnricher;
import com.cleartrip.supplier.benefit.farebenefits.enricher.impl.FareBenefitDomainEnricher;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.IFareBenefitsOrchestrator;
import com.cleartrip.supplier.benefit.farebenefits.orchestration.source.ruleEngine.FareBenefitsOrchestratorImpl;
import com.cleartrip.supplier.infrastructure.factory.RetrofitClientFactoryImpl;
import com.cleartrip.supplier.infrastructure.factory.RetrofitConverterFactoryGeneratorImpl;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.ConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JSONConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JacksonConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JsonSubTypeConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.dto.ClientConfig;
import com.cleartrip.supplier.infrastructure.factory.dto.PoolConfiguration;
import com.cleartrip.supplier.infrastructure.factory.dto.RetroClientBuilderRequest;
import com.cleartrip.supplier.infrastructure.factory.dto.TimeoutConfig;
import com.cleartrip.supplier.infrastructure.factory.dto.iodata.jsondata.JacksonJsonDataInfo;
import com.cleartrip.supplier.benefit.config.models.RetrofitClientParam;
import com.cleartrip.supplier.benefit.minirule.domain.service.IMiniRuleService;
import com.cleartrip.supplier.benefit.minirule.domain.service.MiniRuleDomainHelper;
import com.cleartrip.supplier.benefit.minirule.domain.service.impl.MiniRuleDomainHelperImpl;
import com.cleartrip.supplier.benefit.minirule.domain.service.impl.MiniRuleServiceImpl;
import com.cleartrip.supplier.benefit.minirule.orchesterator.IMIniRuleOrchestrator;
import com.cleartrip.supplier.benefit.minirule.orchesterator.IMiniRuleOrchestratorImpl;
import com.cleartrip.supplier.benefit.minirule.repository.IFetchMiniRuleFromSupplierRepository;
import com.cleartrip.supplier.benefit.minirule.repository.IMiniRulesCacheRepository;
import com.cleartrip.supplier.benefit.minirule.repository.caching.CachingService;
import com.cleartrip.supplier.benefit.minirule.repository.caching.impl.redis.RedisCachingServiceTemplate;
import com.cleartrip.supplier.benefit.minirule.repository.caching.impl.redis.ZstdRedisSerializer;
import com.cleartrip.supplier.benefit.minirule.repository.impl.FetchMiniRuleFromSupplierRepositoryImpl;
import com.cleartrip.supplier.benefit.minirule.repository.impl.MiniRuleCacheRepositoryImpl;
import com.cleartrip.supplier.benefit.minirule.repository.invoker.SMSInvoker;
import com.cleartrip.supplier.benefit.minirule.timelinegenerator.Charge;
import com.cleartrip.supplier.benefit.minirule.timelinegenerator.TimelineGenerator;
import com.cleartrip.supplier.benefit.minirule.utils.JsonUtil;
import com.cleartrip.supplier.benefit.minirule.workflow.keyGenerator.KeyGenerator;
import com.cleartrip.supplier.benefit.minirule.workflow.keyGenerator.MiniRuleCacheKeyGenerator;
import com.cleartrip.supplier.rule.FareRuleConfigRuleEngine;
import com.cleartrip.supplier.benefit.ruleEngine.RuleEngineResourceObserver;
import com.cleartrip.supplier.benefit.ruleEngine.RuleEngineWrapper;
import com.cleartrip.supply.core.api.proto.CachedMiniRules;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import common.monitoring.bean.MonitoringBeanContext;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.resource.ClientResources;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStaticMasterReplicaConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import retrofit2.Retrofit;
import ru.vyarus.dropwizard.guice.module.support.DropwizardAwareModule;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


public class BenefitsModule extends DropwizardAwareModule<BenefitsConfiguration> {

    private static final String BAGGAGE_TABLE_NAME = "Baggage";
    private static final String FARE_BENEFIT_TABLE_NAME = "fareBenefits";
    private static final String THEMIS_PROPERTY_NAME = "ct.sos.rule.fareRules.v2";

    @Override
    protected void configure() {
        bind(IMiniRuleService.class).to(MiniRuleServiceImpl.class).in(Singleton.class);
        bind(IFetchMiniRuleFromSupplierRepository.class).to(FetchMiniRuleFromSupplierRepositoryImpl.class).in(Singleton.class);
        bind(IMiniRulesCacheRepository.class).to(MiniRuleCacheRepositoryImpl.class).in(Singleton.class);
        bind(KeyGenerator.class).to(MiniRuleCacheKeyGenerator.class).in(Singleton.class);
        bind(MiniRuleDomainHelper.class).to(MiniRuleDomainHelperImpl.class).in(Singleton.class);
        bind(IMIniRuleOrchestrator.class).to(IMiniRuleOrchestratorImpl.class).in(Singleton.class);
        bind(IFareBenefitsOrchestrator.class).to(FareBenefitsOrchestratorImpl.class).in(Singleton.class);
        bind(IFareBenefitEnricher.class).to(FareBenefitDomainEnricher.class).in(Singleton.class);
        bind(IFareBenefitsDomainService.class).to(FareBenefitsDomainServiceImpl.class).in(Singleton.class);
        bind(BaggageDomainHelper.class).to(BaggageDomainHelperImpl.class).in(Singleton.class);
        bind(IBaggageOrchestrator.class).to(BaggageOrchestratorImpl.class).in(Singleton.class);
    }

    @Named("benefitsRedisConnectionFactory")
    @Singleton
    @Provides
    public RedisConnectionFactory redisConnectionFactory(@Named("benefitsRedisConfiguration") RedisConfiguration redisConfiguration,
                                                         ClientOptions options, ClientResources dcr) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(80);
        genericObjectPoolConfig.setMinIdle(50);
        genericObjectPoolConfig.setMaxTotal(600);
        LettucePoolingClientConfiguration lettucePoolConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(options)
                .clientResources(dcr)
                .build();
        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(redisConfiguration, lettucePoolConfig);
        lettuceConnectionFactory.afterPropertiesSet();
        return lettuceConnectionFactory;
    }


    @Singleton
    @Provides
    @Named("benefitsRetrofitClientFactory")
    public com.cleartrip.supplier.infrastructure.factory.RetroClientFactory getRetrofitClientFactory() {
        List<JsonSubTypeConverterFactoryHandler> jsonSubTypeConverterFactoryHandlers = Collections.singletonList(new JacksonConverterFactoryHandler());
        JSONConverterFactoryHandler jsonConverterFactoryHandler = new JSONConverterFactoryHandler(jsonSubTypeConverterFactoryHandlers);
        List<ConverterFactoryHandler> converterFactoryHandlerList = Collections.singletonList(jsonConverterFactoryHandler);
        final RetrofitConverterFactoryGeneratorImpl retrofitConverterFactoryGenerator = new RetrofitConverterFactoryGeneratorImpl(converterFactoryHandlerList);
        return new RetrofitClientFactoryImpl(retrofitConverterFactoryGenerator);
    }

    @Singleton
    @Provides
    @Named("searchModuleInvoker")
    public SearchModuleInvoker getSearchModuleInvoker(
            @Named("benefitsRetrofitClientFactory") com.cleartrip.supplier.infrastructure.factory.RetroClientFactory retroClientFactory,
            @Named("benefitsObjectMapper") ObjectMapper objectMapper,
            BenefitsConfiguration benefitsConfiguration
    ) {
        final RetrofitClientParam retrofitClientParam = benefitsConfiguration.getRetrofitClientParam();
        final RetrofitClientParam.PoolConfiguration poolConfiguration = retrofitClientParam.getPoolConfiguration();
        final RetrofitClientParam.TimeoutConfiguration timeoutConfiguration = retrofitClientParam.getTimeoutConfiguration();
        Retrofit retro = retroClientFactory.createRetrofitClient(RetroClientBuilderRequest.builder()
                .clientConfig(ClientConfig.builder()
                        .baseURL(benefitsConfiguration.getSosDns())
                        .poolConfiguration(PoolConfiguration.builder()
                                .keepAliveTimeInMillis(poolConfiguration.getKeepAliveTimeInMillis())
                                .maxIdleConnections(poolConfiguration.getMaxIdleConnections())
                                .maxRequests(poolConfiguration.getMaxRequests())
                                .maxRequestsPerHost(poolConfiguration.getMaxRequestsPerHost())
                                .build())
                        .timeoutConfig(TimeoutConfig.builder()
                                .connectTimeoutInMillis(timeoutConfiguration.getConnectTimeoutInMillis())
                                .overallCallTimeoutInMillis(timeoutConfiguration.getOverallCallTimeoutInMillis())
                                .readTimeoutInMillis(timeoutConfiguration.getReadTimeoutInMillis())
                                .writeTimeoutInMillis(timeoutConfiguration.getWriteTimeoutInMillis())
                                .build())
                        .build())
                .ioDataInfo(JacksonJsonDataInfo.newInstanceWithObjectMapper(objectMapper))
                .build());
        return retro.create(SearchModuleInvoker.class);
    }

    @Singleton
    @Provides
    @Named("benefitsSmsInvoker")
    public SMSInvoker getSmsInvoker(
            @Named("benefitsRetrofitClientFactory") com.cleartrip.supplier.infrastructure.factory.RetroClientFactory retroClientFactory,
            @Named("benefitsObjectMapper") ObjectMapper objectMapper,
            BenefitsConfiguration benefitsConfiguration
    ) {
        final RetrofitClientParam retrofitClientParam = benefitsConfiguration.getRetrofitClientParam();
        final RetrofitClientParam.PoolConfiguration poolConfiguration = retrofitClientParam.getPoolConfiguration();
        final RetrofitClientParam.TimeoutConfiguration timeoutConfiguration = retrofitClientParam.getTimeoutConfiguration();
        Retrofit retro = retroClientFactory.createRetrofitClient(RetroClientBuilderRequest.builder()
                .clientConfig(ClientConfig.builder()
                        .baseURL(benefitsConfiguration.getSmsDns())
                        .poolConfiguration(PoolConfiguration.builder()
                                .keepAliveTimeInMillis(poolConfiguration.getKeepAliveTimeInMillis())
                                .maxIdleConnections(poolConfiguration.getMaxIdleConnections())
                                .maxRequests(poolConfiguration.getMaxRequests())
                                .maxRequestsPerHost(poolConfiguration.getMaxRequestsPerHost())
                                .build())
                        .timeoutConfig(TimeoutConfig.builder()
                                .connectTimeoutInMillis(timeoutConfiguration.getConnectTimeoutInMillis())
                                .overallCallTimeoutInMillis(timeoutConfiguration.getOverallCallTimeoutInMillis())
                                .readTimeoutInMillis(timeoutConfiguration.getReadTimeoutInMillis())
                                .writeTimeoutInMillis(timeoutConfiguration.getWriteTimeoutInMillis())
                                .build())
                        .build())
                .ioDataInfo(JacksonJsonDataInfo.newInstanceWithObjectMapper(objectMapper))
                .build());
        return retro.create(SMSInvoker.class);
    }

    @Singleton
    @Provides
    @Named("benefitsJsonUtil")
    public JsonUtil getJsonUtil(@Named("benefitsObjectMapper") ObjectMapper mapper){
        return new JsonUtil(mapper);
    }

    @Singleton
    @Provides
    @Named("benefitsObjectMapper")
    public ObjectMapper getObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new GuavaModule());
        mapper.registerModule(new Jdk8Module());
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
//        mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        mapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        mapper.setVisibility(mapper
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .getSerializationConfig()
                .getDefaultVisibilityChecker()
                .withFieldVisibility(JsonAutoDetect.Visibility.ANY)
                .withGetterVisibility(JsonAutoDetect.Visibility.NONE)
                .withSetterVisibility(JsonAutoDetect.Visibility.NONE)
                .withCreatorVisibility(JsonAutoDetect.Visibility.ANY));
        return mapper;
    }

    @Singleton
    @Provides
    @Named("benefitsCacheService")
    CachingService<CachedMiniRules.CachedMiniRuleResponse> miniRuleCacheDataCachingService(@Named("benefitsRedisTemplate") RedisTemplate<String, CachedMiniRules.CachedMiniRuleResponse> redisTemplate) {
        return new RedisCachingServiceTemplate<>(redisTemplate);
    }

    @Singleton
    @Provides
    @Named("prepareTimeLineExecutorServiceBenefits")
    ExecutorService prepareTimeLineExecutorService() {
        return new ThreadPoolExecutor(10, 100, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(4));
    }

    @Singleton
    @Provides
    @Named("benefitsRedisTemplate")
    public RedisTemplate<String, CachedMiniRules.CachedMiniRuleResponse> redisMiniRulesTemplate(@Named("benefitsRedisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, CachedMiniRules.CachedMiniRuleResponse> miniRuleCacheDataRedisTemplate = new RedisTemplate<>();
        miniRuleCacheDataRedisTemplate.setConnectionFactory(cf);
        miniRuleCacheDataRedisTemplate.setKeySerializer(new StringRedisSerializer());
        miniRuleCacheDataRedisTemplate.setValueSerializer(new ZstdRedisSerializer<>());
        miniRuleCacheDataRedisTemplate.afterPropertiesSet();
        return miniRuleCacheDataRedisTemplate;
    }

    @Singleton
    @Provides
    @Named("benefitsRedisClient")
    public RedisClient redisClient(BenefitsConfiguration properties) {
        RedisURI redisURI = RedisURI.builder()
                .withHost(properties.getRedisConnectionDetails().getHost())
                .withPort(properties.getRedisConnectionDetails().getPort())
                .build();
        RedisClient client = RedisClient.create(redisURI);
        return client;
    }

    @Singleton
    @Provides
    @Named("benefitsRedisConfiguration")
    public RedisConfiguration redisStandaloneConfiguration(BenefitsConfiguration properties) {
        return new RedisStaticMasterReplicaConfiguration(properties.getRedisConnectionDetails().getHost(), properties.getRedisConnectionDetails().getPort());
    }

    @Singleton
    @Provides
    @Named("fareBenefitsNewRelicHelper")
    public NewRelicHelper getNewRelicHelper(MonitoringBeanContext monitoringBeanContext) {
        return new NewRelicHelper(monitoringBeanContext.getCommonPublisher(), FARE_BENEFIT_TABLE_NAME);
    }

    @Singleton
    @Provides
    @Named("baggageNewRelicHelper")
    public NewRelicHelper getBaggageNewRelicHelper(MonitoringBeanContext monitoringBeanContext) {
        return new NewRelicHelper(monitoringBeanContext.getCommonPublisher(), BAGGAGE_TABLE_NAME);
    }

    @Singleton
    @Provides
    @Named("flightPreviewRepoExecutorService")
    ExecutorService prepareFlightPreviewRepoExecutorService() {
        return new ThreadPoolExecutor(10, 50, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1));
    }

    @Singleton
    @Provides
    @Named("benefitsExecutorService")
    ExecutorService prepareBenefitsExecutorService() {
        return new ThreadPoolExecutor(100, 600, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1));
    }

    @Singleton
    @Provides
    @Named("fareRuleTimeLineGenerator")
    TimelineGenerator<Charge> prepareTimeLineGenerator() {
        return new TimelineGenerator<>();
    }

    @Singleton
    @Provides
    @Named("fareRuleNewRelicHelper")
    public NewRelicHelper getNewRelicHelper(Provider<BenefitsConfiguration> benefitsConfiguration, MonitoringBeanContext monitoringBeanContext) {
        final MonitoringDetails monitoringDetails = benefitsConfiguration.get().getMonitoringDetails();
        final String tableName = monitoringDetails.getNewRelic().getTableName();
        return new NewRelicHelper(monitoringBeanContext.getCommonPublisher(), tableName);
    }

    @Singleton
    @Provides
    @Named("benefitsStatsHelper")
    public StatsHelper getStatsHelper(Provider<BenefitsConfiguration> benefitsConfiguration, MonitoringBeanContext monitoringBeanContext) {
        final MonitoringDetails monitoringDetails = benefitsConfiguration.get().getMonitoringDetails();
        final String topicName = monitoringDetails.getStats().getTopicName();
        return new StatsHelper(monitoringBeanContext.getCommonPublisher(), topicName);
    }


    @Named("benefitsThemisClientService")
    @Singleton
    @Provides
    public ThemisClientService themisClientService(BenefitsConfigContainer properties) {
        String themisUrl = properties.getPropertyValue("ct.sos.themis.url", "https://qa-themis.cleartripcorp.com");
        String themisBucket = properties.getPropertyValue("ct.sos.themis.gcs.bucket", "qa-themis-resource-data");
        return new ThemisClientService(themisUrl, themisBucket, 5);
    }

    @Named("benefitsFareRuleConfigRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FareRuleConfigRuleEngine> getFareRulesRepo(@Named("benefitsThemisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FareRuleConfigRuleEngine> wrapper = new RuleEngineWrapper<>(FareRuleConfigRuleEngine.class);
        wrapper.refreshResource(new File("Benefits/src/main/resources/openl/qa/fareRule_V5.xlsx"));
//        themisClientService.registerResource(THEMIS_PROPERTY_NAME, new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }


    @Singleton
    @Provides
    @Named("fareRuleOrchestratorExecutorService")
    ExecutorService prepareMiniRuleOrchestratorExecutorServiceV2() {
        return new ThreadPoolExecutor(10, 50, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(4));
    }

    @Singleton
    @Provides
    public BenefitsConfigContainer getIProperties(Provider<BenefitsConfiguration> searchConfigurationProvider,
                                                ObjectMapper objectMapper,
                                                ConfigManager configManager) {
        BucketRegistrationCriteria<Map<String, Object>> bucketRegistrationCriteria = getMapBucketRegistrationCriteria(searchConfigurationProvider);
        configManager.registerBucket(bucketRegistrationCriteria);

        String bucketName = BucketNameCreator.getBucketName("search");
        BucketDataWrapper<Map<String, String>> initialData = configManager.getBucketData(bucketName);

        BenefitsConfigContainer benefitsConfigContainer = new BenefitsConfigContainer(objectMapper);
        Map<String, String> currMap = initialData.getData().get();
        benefitsConfigContainer.update(currMap);
        IListener<Map<String, String>> listener = new PropertiesListener(benefitsConfigContainer, objectMapper);
        configManager.addListener(listener, BucketNameCreator.getBucketName("search"));

        return benefitsConfigContainer;
    }

    private BucketRegistrationCriteria<Map<String, Object>> getMapBucketRegistrationCriteria(Provider<BenefitsConfiguration> searchConfigurationProvider) {
        MySqlConf mySqlConf = searchConfigurationProvider.get().getMySqlConf();
        RepositoryConfig repositoryConfig = new MySQLRepositoryConfig(new MySQLConnectionParams(mySqlConf.getUrl(),
                mySqlConf.getUserName(),
                mySqlConf.getPassword()));
        DecoderConfig<Map<String, Object>> decoderConfig = new DecoderConfig<>(DecoderType.OBJECT_MAPPER, new Datatype<>(new TypeReference<>() {
        }));
        String bucketName = BucketNameCreator.getBucketName("search");
        return new BucketRegistrationCriteria<>(bucketName, repositoryConfig, decoderConfig);
    }

    @Named("fareBenefitsRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FareBenefitRuleEngine> getFareBenefitRuleEngine(@Named("benefitsThemisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FareBenefitRuleEngine> wrapper = new RuleEngineWrapper<>(FareBenefitRuleEngine.class);
        wrapper.refreshResource(new File("Benefits/src/main/resources/openl/qa/fareBenefitRule_V3.xlsx"));
//        themisClientService.registerResource("ct.sos.rule.fareBenefitsRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Singleton
    @Provides
    @Named("benefitsBaggageSourceAndConfigRuleEngine")
    public RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> getBaggageSourceAndConfigRuleEngine(@Named("benefitsThemisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> wrapper = new RuleEngineWrapper<>(BaggageSourceAndConfigRuleEngine.class);
//        wrapper.refreshResource(new File("Benefits/src/main/resources/openl/qa/baggageSourceAndConfigRules-prod_c4d565ca859c6d8ccd9f43f290852720.xlsx"));
        themisClientService.registerResource("ct.sos.rule.baggage.rules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Singleton
    @Provides
    @Named("newRelicUtil")
    public NewRelicUtil getNewRelicUtil(@Named("baggageNewRelicHelper") NewRelicHelper newRelicHelper) {
        return new NewRelicUtil(newRelicHelper);
    }

}
