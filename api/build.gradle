plugins {
    id 'java'
    id "com.google.protobuf" version "0.9.2"
    id "com.jfrog.artifactory" version "4.31.4"
}

apply plugin: 'maven-publish'
apply plugin: 'com.jfrog.artifactory'

group 'com.cleartrip.supplier'
version '2.2.0-RELEASE'

repositories {
    mavenCentral()
}

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'

    implementation group: 'org.apache.commons', name: 'commons-collections4', version: '4.1'
    implementation group: 'com.google.protobuf', name: 'protobuf-java', version: '3.22.2'
    implementation group: 'com.google.protobuf', name: 'protobuf-java-util', version: '3.19.4'
    implementation 'io.grpc:grpc-protobuf:1.49.0'
    implementation(group: 'io.grpc', name: 'grpc-stub', version: '1.49.0') {
        exclude group: 'com.google.guava'
    }
}

test {
    useJUnitPlatform()
}
sourceSets {
    main {
        proto{
            srcDirs 'src/main/resources/proto/'
        }
    }
}

sourceCompatibility = 11
targetCompatibility = 11

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.17.3'
    }
    plugins {
        grpc {
            artifact = 'io.grpc:protoc-gen-grpc-java:1.43.0'
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
    }
}

artifactory {
    contextUrl = "${ARTIFACTORY_URL}"
    publish {
        repository {
            repoKey = "${ARTIFACTORY_REPO}"
            username = "${ARTIFACTORY_USERNAME}"
            password = "${ARTIFACTORY_PASSWORD}"
            //maven = true
        }
        defaults {
            publications('mavenJava')
        }
    }
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
}