syntax = "proto3";

package com.cleartrip.supplier.inventory.protos.v1;
import  "v1/entity/baggage_request.proto";
import  "v1/entity/baggage_response.proto";
import  "v1/entity/flight_solution.proto";
option java_outer_classname = "BaggageServiceOuter";
option java_multiple_files = true;

//Service to get free baggage data in read-through-cache mode.
service BaggageService {
  rpc BaggageSearchRpc (BaggageRequest) returns (BaggageResponse) {};
}

//Actual free baggage request.
message BaggageRequest {
  string request_id = 1;
  BaggageCriteria free_baggage_info_criteria = 2;//It holds the mandatory baggage details of a journey.
  //multiple in case of normal RT and normal MC
  //Info about number of flights, segments and solution fares etc.
  repeated SolutionInfo solutions = 3;
  string itinerary_id = 4;
}

//Final baggage response correspond to a free baggage request.
message BaggageResponse{
  string request_id = 1;
  //multiple in case of normal RT and normal MC
  repeated SolutionBaggageResponse solution_baggage_response = 2;
}