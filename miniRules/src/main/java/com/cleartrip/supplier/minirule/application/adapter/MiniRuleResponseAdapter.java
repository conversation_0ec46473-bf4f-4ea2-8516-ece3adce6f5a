package com.cleartrip.supplier.minirule.application.adapter;

import com.cleartrip.supplier.minirule.application.models.request.PaxInfo;
import com.cleartrip.supplier.minirule.application.models.request.SolutionDetail;
import com.cleartrip.supplier.minirule.application.models.responseV2.FareRuleApplicationDTO;
import com.cleartrip.supplier.minirule.application.models.responseV2.FareRuleDetailApplicationDTO;
import com.cleartrip.supplier.minirule.application.models.responseV2.MiniRuleApplicationResponse;
import com.cleartrip.supplier.minirule.domain.models.response.FareRuleDomainDTO;
import com.cleartrip.supplier.minirule.domain.models.response.MiniRuleDomainResponse;
import com.cleartrip.supply.core.api.proto.FareRuleDetailOuterClass;
import com.cleartrip.supply.core.api.proto.FareRuleListOuterClass;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class MiniRuleResponseAdapter {

    private final FareRuleProtoAdapter fareRuleProtoAdapter;

    public MiniRuleResponseAdapter() {
        fareRuleProtoAdapter = new FareRuleProtoAdapter();
    }

    public List<FareRuleDetailOuterClass.FareRuleDetail> toMiniRuleResponse(MiniRuleDomainResponse domainResponse, List<PaxInfo> paxDetails) {
        List<FareRuleDetailOuterClass.FareRuleDetail> fareRuleDetails = new ArrayList<>();
        domainResponse.getFareRuleDetails().forEach(fareRuleDetail -> {
//            updatePaxPrice(fareRuleDetail.getFareRules().values().stream().flatMap(List::stream).collect(Collectors.toList()), paxDetails);

            FareRuleDetailOuterClass.FareRuleDetail fareRuleDetail1 = FareRuleDetailOuterClass.FareRuleDetail.newBuilder()
                    .putAllJourneyWiseFareRules(toFareRulesMap(fareRuleDetail.getFareRules(), paxDetails))
                    .setComboFbc(fareRuleDetail.getComboFbd())
                    .setSolutionId(fareRuleDetail.getSolutionId())
                    .setCtBrandName(Optional.ofNullable(fareRuleDetail.getCtBrandName()).orElse("Regular"))
                    .build();
            fareRuleDetails.add(fareRuleDetail1);
        });
        return fareRuleDetails;

    }

    public Map<String, FareRuleListOuterClass.FareRuleList> toFareRulesMap(Map<String, List<FareRuleDomainDTO>> fareRulesMap, List<PaxInfo> paxDetails) {
        Map<String, FareRuleListOuterClass.FareRuleList> fareRuleListMap = new HashMap<>();
        fareRulesMap.forEach((key, value) -> {
            fareRuleListMap.put(key, FareRuleListOuterClass.FareRuleList.newBuilder()
                    .addAllFareRules(fareRuleProtoAdapter.getFareRules(value, paxDetails))
                    .build());
        });
        return fareRuleListMap;
    }

    public MiniRuleApplicationResponse protoToMiniRuleResponse(List<FareRuleDetailOuterClass.FareRuleDetail> fareRuleResponse, List<PaxInfo> paxDetails, Map<String, SolutionDetail> solutionDetails) {
        List<FareRuleDetailApplicationDTO> fareRuleDetailApplicationList = new ArrayList<>();
        for(FareRuleDetailOuterClass.FareRuleDetail fareRuleDetail : fareRuleResponse) {
            fareRuleDetailApplicationList.add(FareRuleDetailApplicationDTO.builder()
//                            .comboFbd(fareRuleDetail.getComboFbc())
                            .comboFbd(fetchComboFbc(solutionDetails))
                            .comboFbc(fetchComboFbc(solutionDetails))
                            .ctBrandName(Optional.of(fareRuleDetail.getCtBrandName()).orElse("Regular"))
                            .solutionId(fareRuleDetail.getSolutionId())
                            .fareRules(fromFareRulesMap(fareRuleDetail.getJourneyWiseFareRulesMap()))
                    .build());
        }
        return new MiniRuleApplicationResponse(fareRuleDetailApplicationList);
    }

    private String fetchComboFbc(Map<String, SolutionDetail> fareRuleDetail) {
        SolutionDetail solutionDetail = fareRuleDetail.values().iterator().next();
        String comboFbc = solutionDetail.getComboFbc().get(0);
        return Optional.ofNullable(comboFbc).orElse(StringUtils.EMPTY);
    }

    private Map<String, List<FareRuleApplicationDTO>> fromFareRulesMap(Map<String, FareRuleListOuterClass.FareRuleList> journeyWiseFareRulesMap) {
        Map<String, List<FareRuleApplicationDTO>> fareRulesMap = new HashMap<>();
        journeyWiseFareRulesMap.forEach((key, value) -> {
            fareRulesMap.put(key, fareRuleProtoAdapter.getFareRulesDomainDTO(value.getFareRulesList(), new ArrayList<>()));
        });
        return fareRulesMap;
    }

}
