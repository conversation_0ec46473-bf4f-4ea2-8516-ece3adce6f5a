package com.cleartrip.supplier.minirule.domain.models.request;

import com.google.common.collect.Maps;

import java.util.Map;

public enum ConnectorEnum {
    AIE,
    TBO,
    TF,
    AMADEUS,
    AIR_ASIA,
    TRUJET,
    FLY_DUBAI,
    AIR_ARABIA,
    SKY_PICKER,
    SABRE,
    INDIGO,
    SPICEJET,
    MYSTI_FLY,
    GALILEO,
    EZEEGO,
    RADIXX,
    JAZEERA,
    FLYNAS,
    TRIPJACK,
    RIYA,
    AKASA_AIR,
    ALLIANCE_AIR,
    STAR_AIR,
    FLYADEAL,
    AMADEUS_NDC;

    protected static final Map<String, ConnectorEnum> supplierMap = Maps.newHashMap();

    private ConnectorEnum() {
    }

    public static ConnectorEnum resolveSupplier(String name) {
        ConnectorEnum connectorEnum = supplierMap.get(name);
        return null != connectorEnum ? connectorEnum : valueOf(name);
    }

    static {
        supplierMap.put("GALILEO_AI", GALILEO);
        supplierMap.put("GALILEO_IC", GALILEO);
        supplierMap.put("GALILEO_RT", GALILEO);
        supplierMap.put("GALILEO_LFS_INTERNATIONAL", GALILEO);
        supplierMap.put("GALILEO_INTERNATIONAL_RT", GALILEO);
        supplierMap.put("AMADEUS_AI", AMADEUS);
        supplierMap.put("AMADEUS_IC", AMADEUS);
        supplierMap.put("AMADEUS_RT", AMADEUS);
        supplierMap.put("AMADEUS_INTERNATIONAL", AMADEUS);
        supplierMap.put("AMADEUS_INTERNATIONAL_RT", AMADEUS);
        supplierMap.put("TF_RT", TF);
        supplierMap.put("AIR_ARABIA_RT", AIR_ARABIA);
        supplierMap.put("AIR_ARABIA_MOROCCO", AIR_ARABIA);
        supplierMap.put("AIR_ARABIA_EGYPT", AIR_ARABIA);
        supplierMap.put("SABRE_RT", SABRE);
    }
}
