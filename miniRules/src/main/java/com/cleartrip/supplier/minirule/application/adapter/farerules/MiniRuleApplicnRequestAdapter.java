package com.cleartrip.supplier.minirule.application.adapter.farerules;

import com.cleartrip.air.sms.api.constants.enums.AirSupplier;
import com.cleartrip.air.sms.api.constants.enums.ConnectorEnum;
import com.cleartrip.supplier.inventory.protos.v1.PaxTypeOuterClass;
import com.cleartrip.supplier.minirule.application.models.request.*;
import com.cleartrip.supplier.minirule.application.models.request.PaxInfo;
import com.cleartrip.supplier.minirule.domain.models.request.farerules.*;
import com.cleartrip.supplier.minirule.workflow.keyGenerator.FetchSearchCriteria;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.models.DTO.*;
import com.cleartrip.supplier.search.models.FlightSegmentIdDTO;
import com.cleartrip.supplier.search.models.application.*;
import com.cleartrip.supplier.search.services.cachingService.keyGenerator.SolutionIdFieldsV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class MiniRuleApplicnRequestAdapter {

    private static final String SEPARATOR = "_";
    public static final String BASE_FARE = "BASE_FARE";
    public static final String FLY_DUBAI_AIRLINE = "FZ";
    public static final String AKASA_AIR_AIRLINE = "QP";

    public MiniRuleDomainRequest convert(MiniRuleRequest request, FetchFlightSolutionRes fetchFlightSolutionRes) {
        if(Objects.isNull(fetchFlightSolutionRes.getSolutionDTOList()) || fetchFlightSolutionRes.getSolutionDTOList().isEmpty()){
            throw new RuntimeException("No Data found from the search cache for solutionIds " + request.getSolutionDetails().values().stream().map(SolutionDetail::getSolutionId).collect(Collectors.joining(",")), null);
        }
        List<FlightSolutionDTO> flightSolutionList = new ArrayList<>(fetchFlightSolutionRes.getSolutionDTOList().values());
        Map<String, FlightSolutionDomainDTO> flightSolutionDTOMap = new HashMap<>();
        request.getSolutionDetails().values().forEach(solutionDetails -> {
            String solutionId = solutionDetails.getSolutionId();
            List<String> comboFbcList = solutionDetails.getComboFbc();
            LinkedHashMap<String, FlightDetailsDomainDTO> flightDetailsMap = new LinkedHashMap<>();
            Map<String, String> oldToNewJourneyKeyMap = new HashMap<>();
            List<FareDetailsDomainDTO> fareDetailsDtoList = new ArrayList<>();
            Optional<String> supplier = Optional.empty();
            String credentialId = flightSolutionList.get(0).getSolutionMetaInfoDTO().getSupplierInfoDTO().getCredentialKey();

            for (FlightSolutionDTO flightSolution : flightSolutionList) {
                if (CollectionUtils.isNotEmpty(flightSolution.getFareFamilyDTO()) && CollectionUtils.isNotEmpty(flightSolution.getFareFamilyDTO()) && Objects.nonNull(flightSolution.getSolutionMetaInfoDTO())) {
                    List<FareFamilyDTO> fareDetails = flightSolution.getFareFamilyDTO().stream().filter(fareDetail -> fareDetail.getFareSolutionId().equalsIgnoreCase(solutionId) && comboFbcList.contains(fareDetail.getComboFbc())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(fareDetails)) {
                        if (supplier.isEmpty()) {
                            supplier = Optional.ofNullable(getSupplier(flightSolution));
                        }
                        if (flightDetailsMap.isEmpty()) {
                            LinkedHashMap<String, FlightDetailsDomainDTO> flightDetailsDomainDTOMap = prepareFlightDetails(flightSolution.getFlightDTO());
                            oldToNewJourneyKeyMap = prepareOldToNewJourneyKeyMap(solutionId, flightDetailsDomainDTOMap);
                            flightDetailsMap.putAll(flightDetailsDomainDTOMap);
                        }
                        fareDetailsDtoList.addAll(prepareFareDetails(fareDetails, supplier.get(), oldToNewJourneyKeyMap));
                    }
                }
            }
            if (!flightDetailsMap.isEmpty() && CollectionUtils.isNotEmpty(fareDetailsDtoList)) {
                if(solutionDetails.getSolutionId().contains(FLY_DUBAI_AIRLINE)||solutionDetails.getSolutionId().contains(AKASA_AIR_AIRLINE)) {
                    flightSolutionDTOMap.put(flightSolutionList.get(0).getSolutionMetaInfoDTO().getPromiseId(), FlightSolutionDomainDTO.builder()
                            .flightDetailsMap(flightDetailsMap)
                            .fareDetailsList(fareDetailsDtoList)
                            .country(request.getCountry())
                            .isInternational(solutionDetails.isInternational())
                            .supplier(supplier.get())
                            .credentialId(credentialId)
                            .build()
                    );
                } else {
                    flightSolutionDTOMap.put(solutionId, FlightSolutionDomainDTO.builder()
                            .flightDetailsMap(flightDetailsMap)
                            .fareDetailsList(fareDetailsDtoList)
                            .country(request.getCountry())
                            .isInternational(solutionDetails.isInternational())
                            .supplier(supplier.get())
                            .credentialId(credentialId)
                            .build()
                    );
                }

            }

        });
        return MiniRuleDomainRequest.builder()
                .flightSolutionMap(flightSolutionDTOMap)
                .paxDetails(getPaxDetails(request.getPaxDetails()))
                .context(request.getContext())
                .build();
    }

    private Map<String, String> prepareOldToNewJourneyKeyMap(String solutionId, LinkedHashMap<String, FlightDetailsDomainDTO> flightDetailsDomainDTOMap){
        Map<String, String> oldToNewJourneyKeyMap = new HashMap<>();
        List<SolutionIdFieldsV2.SearchCriteriaFields> searchCriteria = FetchSearchCriteria.getSearchCriteria(solutionId);
        SolutionIdFieldsV2.SearchCriteriaFields onwardCriteria = searchCriteria.get(0);
        String onwardOldJourneyKey = onwardCriteria.getJourneyKey();

        List<String> keys = new ArrayList<>(flightDetailsDomainDTOMap.keySet());
        String onwardNewJourneyKey = keys.get(0);
        oldToNewJourneyKeyMap.put(onwardOldJourneyKey, onwardNewJourneyKey);

        if(keys.size() > 1){
            SolutionIdFieldsV2.SearchCriteriaFields returnCriteria = searchCriteria.get(1);
            String returnOldJourneyKey = returnCriteria.getJourneyKey();
            String returnNewJourneyKey = keys.get(1);
            oldToNewJourneyKeyMap.put(returnOldJourneyKey, returnNewJourneyKey);
        }
        return oldToNewJourneyKeyMap;
    }

    private String getSupplier(FlightSolutionDTO flightSolution){
        String supplier = flightSolution.getSolutionMetaInfoDTO().getSupplierInfoDTO().getSupplier();
        if(Objects.isNull(supplier)) return null;
        return ConnectorEnum.resolveSupplier(supplier).name();
    }


    private List<PaxInfoDomainDTO> getPaxDetails(List<PaxInfo> paxInfos){
        return paxInfos.stream()
                .map(paxInfo -> PaxInfoDomainDTO.builder()
                        .paxType(paxInfo.getPaxType().getType())
                        .count(paxInfo.getCount())
                        .build())
                .collect(Collectors.toList());
    }

    private List<FareDetailsDomainDTO> prepareFareDetails(List<FareFamilyDTO> fareDetailsList, String supplier, Map<String, String> oldToNewJourneyKeyMap) {
        List<FareDetailsDomainDTO> fareDetailsDtoList = new ArrayList<>();
        fareDetailsList.forEach(fareDetails -> {
            String comboFbc = fareDetails.getComboFbc();
            String ctBrandName = fareDetails.getCtBrandName();
            Map<String, JourneyFareSummaryDomainDTO> journeyWiseFareDetailsMap = new HashMap<>();

            fareDetails.getJourneyFareSummarymap().forEach((journeyKey, journeyValue) -> {
                Map<String, SegmentFareSummaryDomainDTO> segmentWiseFareDetailMap = new HashMap<>();
                journeyValue.getSegmentFareMap().forEach((segmentKey, segmentValue) -> segmentWiseFareDetailMap.put(segmentKey, SegmentFareSummaryDomainDTO.builder()
                                .fareBasisCode(getFareBasisCode(segmentValue))
                                .productClass(getProductClass(segmentValue, supplier))
                                .cabinType(segmentValue.getSegmentFareCodesDTO().getCabinType())
                                .build()
                        ));

                journeyWiseFareDetailsMap.put(oldToNewJourneyKeyMap.get(journeyKey),
                        JourneyFareSummaryDomainDTO.builder()
                                .paxFareSummaries(getPaxFareSummaryMap(journeyValue.getPaxFareInfoDTOList()))
                                .segmentFareMap(segmentWiseFareDetailMap)
                                .build()
                );
            });
            fareDetailsDtoList.add(FareDetailsDomainDTO.builder()
                            .comboFbc(comboFbc)
                            .ctBrandName(ctBrandName)
                            .fareCategory(getFareCategory(fareDetails))
                            .fareSubCategory(fareDetails.getFareGroupInfo().toString())
                            .fareGroupName(fareDetails.getGroupDisplayName())
                            .validatingCarrier(fareDetails.getFareMetaInfoDTO().getValidatingCarrier())
                            .journeyFareSummaryMap(journeyWiseFareDetailsMap)
                            .build()
                    );
        });
        return fareDetailsDtoList;
    }

    private String getFareCategory(FareFamilyDTO fareDetails){
        if(Objects.isNull(fareDetails.getFareMetaInfoDTO().getFareCategoryType().toString())) return null;
        return fareDetails.getFareMetaInfoDTO().getFareCategoryType().toString();
    }

    private Map<String, PaxFareSummaryDomainDTO> getPaxFareSummaryMap(List<PaxFareInfoDTO> paxFareSummaries) {
        Map<String, PaxFareSummaryDomainDTO> paxFareSummaryMap = new HashMap<>();
        paxFareSummaries.forEach(paxFareSummary -> paxFareSummaryMap.put(getPaxType(paxFareSummary.getPaxType()), getPaxFareSummary(paxFareSummary)));
        return paxFareSummaryMap;
    }

    private String getPaxType(PaxTypeOuterClass.PaxType paxType){
        if(paxType.toString().equalsIgnoreCase("ADULT")) return "ADT";
        if(paxType.toString().equalsIgnoreCase("CHILD")) return "CHD";
        return "INF";
    }


    private PaxFareSummaryDomainDTO getPaxFareSummary(PaxFareInfoDTO paxFareSummary) {
        return PaxFareSummaryDomainDTO.builder()
                .paxCount(paxFareSummary.getPaxCount())
                .priceComponents(getPriceComponents(paxFareSummary.getPriceComponentDTOS()))
                .build();
    }

    private List<PriceComponentDomainDTO> getPriceComponents(List<PriceComponentDTO> priceComponents) {
        return priceComponents.stream()
                .map(pc -> PriceComponentDomainDTO.builder()
                    .code(getCode(pc.getCategory(), pc.getCode()))
                    .amount(pc.getAmount())
                    .currency(pc.getCurrency())
                    .build()
                 ).collect(Collectors.toList());
    }

    private String getCode(String category, String code){
        if(BASE_FARE.equalsIgnoreCase(category)) return category;
        return code;
    }

    private String getFareBasisCode(SegmentFareSummaryDTO segmentValue) {
        return segmentValue.getSegmentFareCodesDTO().getFareBasisCode();
    }

    private String getProductClass(SegmentFareSummaryDTO segmentValue, String supplier) {
        if (supplier.contains(AirSupplier.GALILEO.getDisplayName())) {
            return segmentValue.getSegmentFareCodesDTO().getBookingClass();
        }

        return segmentValue.getSegmentFareCodesDTO().getProductClass();
    }

    private LinkedHashMap<String, FlightDetailsDomainDTO> prepareFlightDetails(List<FlightDTO> flightDetails) {
        LinkedHashMap<String, FlightDetailsDomainDTO> flightDetailsDtoMap = new LinkedHashMap<>();
        flightDetails = flightDetails.stream().sorted(Comparator.comparing(flightDetail -> {
            List<FlightSegmentIdDTO> segmentDetails = flightDetail.getFlightSegmentDTOList().stream().sorted(Comparator.comparing(segmentDetail -> segmentDetail.getDepartFromStop().getTime())).collect(Collectors.toList());
            return segmentDetails.get(0).getDepartFromStop().getTime();
        })).collect(Collectors.toList());

    flightDetails.forEach(flightDetail -> {
            List<FlightSegmentIdDTO> segmentDetails = flightDetail.getFlightSegmentDTOList().stream().sorted(Comparator.comparing(segmentDetail -> segmentDetail.getDepartFromStop().getTime())).collect(Collectors.toList());
            String departureCode = segmentDetails.get(0).getDepartFromStop().getAirport();
            String arrivalCode = segmentDetails.get(segmentDetails.size() - 1).getArrivalToStop().getAirport();
            flightDetailsDtoMap.put(departureCode + SEPARATOR + arrivalCode, prepareJourneyDetailDto(segmentDetails));
        });
        return flightDetailsDtoMap;
    }

    private FlightDetailsDomainDTO prepareJourneyDetailDto(List<FlightSegmentIdDTO> segmentDetails) {
        LinkedHashMap<String, SegmentDetailsDomainDTO> segmentDetailsDtoMap = new LinkedHashMap<>();
        segmentDetails.forEach(segmentDetail -> {
            String departureAirportCode = segmentDetail.getDepartFromStop().getAirport();
            String arrivalAirportCode = segmentDetail.getArrivalToStop().getAirport();
            segmentDetailsDtoMap.put(departureAirportCode + SEPARATOR + arrivalAirportCode, getSegmentDetail(segmentDetail));
        });
        return FlightDetailsDomainDTO.builder().segmentDetails(segmentDetailsDtoMap).build();
    }

    private SegmentDetailsDomainDTO getSegmentDetail(FlightSegmentIdDTO segmentDetails){
        return SegmentDetailsDomainDTO.builder()
                .arrivalAirportCode(segmentDetails.getArrivalToStop().getAirport())
                .departureAirportCode(segmentDetails.getDepartFromStop().getAirport())
                .airlineCode(segmentDetails.getSegmentFlightDetailsDTO().getAirline())
                .flightNumber(segmentDetails.getSegmentFlightDetailsDTO().getFlightNumber())
                .departureTime(segmentDetails.getDepartFromStop().getTime())
                .zoneId(segmentDetails.getDepartFromStop().getZoneId())
                .build();
    }
}
