package com.cleartrip.supplier.minirule.utils;

import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.MiniRuleWorkflowRequest;
import com.cleartrip.supplier.minirule.workflow.strategy.MiniRuleWorkflowStrategy;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
public class StrategyUtil {

    public static final String STRATEGY_JSON = "{\\\"SUPPLIER_FARE_RULE\\\": [\\\"GAL\\\",\\\"FLY_DUBAI\\\",\\\"AKASA_AIR\\\"],\\\"GAL_DOMESTIC\\\": [],\\\"SEARCH_FARE_RULE\\\": [\\\"VERTEIL\\\"],\\\"RULE_ENGINE\\\": [],\\\"CACHE_ONLY\\\": [\\\"AIR_ARABIA\\\"]}";
    public static final String GAL = "GAL";
    public static final String UTF_8 = "UTF-8";
    public static final String TARGET = "\\\"";
    public static final String REPLACEMENT = "\"";
    private final SearchConfigContainer commonCachedProperties;
    private final ObjectMapper objectMapper;
    private final TypeReference<Map<MiniRuleWorkflowStrategy, List<String>>> typeReference = new TypeReference<Map<MiniRuleWorkflowStrategy, List<String>>>() {};

    public StrategyUtil(SearchConfigContainer commonCachedProperties, ObjectMapper objectMapper) {
        this.commonCachedProperties = commonCachedProperties;
        this.objectMapper = objectMapper;
    }

    public MiniRuleWorkflowStrategy getStrategyFromConfig(String config, MiniRuleWorkflowRequest request){
        String strategyJson = commonCachedProperties.getPropertyValue(config, STRATEGY_JSON);

        Map<MiniRuleWorkflowStrategy, List<String>> map;
        try {
            String strategyMap = URLDecoder.decode(strategyJson, UTF_8);
            map = objectMapper.readValue(strategyMap.replace(TARGET, REPLACEMENT), typeReference);
        } catch (Exception e) {
            log.error("Error in parsing map from Json of property {} ", config, e);
            return MiniRuleWorkflowStrategy.RULE_ENGINE;
        }

        MiniRuleWorkflowStrategy strategy = map.entrySet().stream()
                .filter(entry -> entry.getValue().contains(request.getSupplier()))
                .map(Map.Entry::getKey)
                .findFirst()
                .orElse(null);

        MiniRuleWorkflowStrategy miniRuleWorkflowStrategy = Arrays.stream(MiniRuleWorkflowStrategy.values())
                .filter(s -> s.equals(strategy))
                .findFirst()
                .orElse(MiniRuleWorkflowStrategy.RULE_ENGINE);

        return getFinalStrategy(miniRuleWorkflowStrategy, request.isInternational(), request.getSupplier());

    }

    private MiniRuleWorkflowStrategy getFinalStrategy(MiniRuleWorkflowStrategy miniRuleWorkflowStrategy, boolean international, String supplier) {
        if(MiniRuleWorkflowStrategy.SUPPLIER_FARE_RULE.equals(miniRuleWorkflowStrategy) && !international && GAL.equals(supplier)) return MiniRuleWorkflowStrategy.GAL_DOMESTIC;

        return miniRuleWorkflowStrategy;
    }
}
