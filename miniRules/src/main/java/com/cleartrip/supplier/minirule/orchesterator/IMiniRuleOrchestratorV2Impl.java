package com.cleartrip.supplier.minirule.orchesterator;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.minirule.monitoring.NewRelicUtil;
import com.cleartrip.supplier.minirule.monitoring.StatsUtil;
import com.cleartrip.supplier.minirule.orchesterator.adapter.farerules.MiniRuleWorkflowResponseAdapter;
import com.cleartrip.supplier.minirule.orchesterator.models.request.farerule.MiniRuleOrchestratorRequest;
import com.cleartrip.supplier.minirule.orchesterator.models.response.farerule.MiniRuleOrchestratorResponse;
import com.cleartrip.supplier.minirule.utils.JsonUtil;
import com.cleartrip.supplier.minirule.workflow.MiniRuleWorkflow;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.MiniRuleWorkflowResponse;
import com.cleartrip.supplier.minirule.workflow.generator.MiniRuleWorkflowGenerator;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Slf4j
public class IMiniRuleOrchestratorV2Impl implements IMIniRuleOrchestratorV2{

    private final ExecutorService executorService;

    private final MiniRuleWorkflowGenerator workflowGenerator;

    private final MiniRuleWorkflowResponseAdapter responseAdapter;

    private final NewRelicHelper newRelicHelper;
    private final StatsHelper statsHelper;

    private final StatsUtil statsUtil;

    private final JsonUtil util;

    @Inject
    public IMiniRuleOrchestratorV2Impl(@Named("miniRuleOrchestratorExecutorServiceV2") ExecutorService executorService,
                                       MiniRuleWorkflowGenerator workflowGenerator,
                                       MiniRuleWorkflowResponseAdapter responseAdapter,
                                       @Named("miniRuleNewRelicHelper") NewRelicHelper newRelicHelper,
                                       @Named("miniRuleStatsHelper") StatsHelper statsHelper,
                                       StatsUtil statsUtil,
                                       @Named("miniRuleJsonUtil") JsonUtil util) {
        this.executorService = executorService;
        this.workflowGenerator = workflowGenerator;
        this.responseAdapter = responseAdapter;
        this.newRelicHelper = newRelicHelper;
        this.statsHelper = statsHelper;
        this.statsUtil = statsUtil;
        this.util = util;
    }

    @Override
    public MiniRuleOrchestratorResponse orchestrate(MiniRuleOrchestratorRequest request) {
        long startTime = System.currentTimeMillis();
        List<MiniRuleWorkflowResponse> workflowResponses = new ArrayList<>();
        List<Callable<MiniRuleWorkflowResponse>> workflowTasks = new ArrayList<>();
        List<Future<MiniRuleWorkflowResponse>> futures;
        try{
            List<MiniRuleWorkflow> workFlowRequests = workflowGenerator.generate(request);
            workFlowRequests.forEach(miniRuleWorkflow -> workflowTasks.add(miniRuleWorkflow::process));
            futures = executorService.invokeAll(workflowTasks);
            for(int idx = 0; idx < futures.size(); idx++) {
                MiniRuleWorkflow workflow = workFlowRequests.get(idx);
                try {
                    Future<MiniRuleWorkflowResponse> future = futures.get(idx);
                    MiniRuleWorkflowResponse workflowResponse = future.get();
                    workflowResponses.add(workflowResponse);
                    CompletableFuture.runAsync(() -> pushToMonitoring(startTime, System.currentTimeMillis(), workflowResponse, 200, request.getContext(), request));
                } catch (Exception e) {
                    log.error("Exception while executing fare rules workflow task for {} , exception {} at {}", util.writeValueAsString(request), e.getMessage(), e.getCause().getStackTrace());
                    CompletableFuture.runAsync(() -> pushToMonitoring(startTime, System.currentTimeMillis(), e.getCause(), 500, request.getContext(), request));
                    CompletableFuture.runAsync(() -> newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleOrchestratorLayerParams(workflow.getContext(),
                            startTime,
                            request.getContext(),
                            Objects.isNull(e.getCause()) ? Optional.of(e) : Optional.of(e.getCause())))
                    );
                }
            }
        }
        catch(Exception e){
            log.error("Exception while executing fare benefit workflow tasks for {} , exception {} at {}", util.writeValueAsString(request), e.getMessage(), e.getCause().getStackTrace());
            CompletableFuture.runAsync(() -> pushToMonitoring(startTime, System.currentTimeMillis(), e.getCause(), 500, request.getContext(), request));
            CompletableFuture.runAsync(() -> newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleOrchestratorLayerParams(null,
                    startTime,
                    request.getContext(),
                    Optional.of(e)))
            );
        }
        return responseAdapter.convert(request, workflowResponses);
    }


    private void pushToMonitoring(long start, long end, Object response, int statusCode, Context monitoringContext, Object request) {
        try {
            statsHelper.pushToStats(start,
                    end,
                    statsUtil.getObjectInByte(response),
                    statusCode,
                    "MINI_RULES_SUPPLY_CORE",
                    monitoringContext,
                    statsUtil.getObjectInByte(request),
                    "SUPPLY_CORE_ORCHESTRATOR",
                    "MINI_RULES",
                    ApiStatsDto.HttpMethod.POST
            );
        } catch (Exception exception) {
            log.error("Exception while pushing to stats for fareRules request {} is {}", request, exception);
        }
    }
}
