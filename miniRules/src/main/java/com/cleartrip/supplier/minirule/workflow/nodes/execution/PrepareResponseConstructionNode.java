package com.cleartrip.supplier.minirule.workflow.nodes.execution;

import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.FlightSolutionWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.FareRuleDetailWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.ResponseConstructionTaskRequestDTO;
import com.cleartrip.supplier.minirule.workflow.dto.MiniRuleWorkflowContext;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.MiniRuleWorkflowResponse;
import com.cleartrip.supplier.minirule.workflow.tasks.execution.PrepareResponseConstructionTask;
import com.cleartrip.utility.workflow.impl.SequentialNodeImpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PrepareResponseConstructionNode extends SequentialNodeImpl<ResponseConstructionTaskRequestDTO, MiniRuleWorkflowResponse, MiniRuleWorkflowContext, PrepareResponseConstructionTask> {

    private final String FINAL_CONSTRUCTION_NODE = "PrepareResponseConstructionNode";
    @Override
    public ResponseConstructionTaskRequestDTO prepareTaskInput(MiniRuleWorkflowContext miniRuleWorkflowContext) {


        List<FareRuleDetailWorkflowDTO> finalFareRules = new ArrayList<>() {{
            addAll(miniRuleWorkflowContext.getFareRulesCache());
            addAll(miniRuleWorkflowContext.getFareRulesSupplier());
            addAll(miniRuleWorkflowContext.getFareRulesRuleEngine());
        }};

        return ResponseConstructionTaskRequestDTO.builder()
                .fareRules(finalFareRules)
                .flightSolutionMap(getMap(miniRuleWorkflowContext))
                .successFlag(miniRuleWorkflowContext.getSuccessFlag())
                .build();
    }

    private Map<String, FlightSolutionWorkflowDTO> getMap(MiniRuleWorkflowContext miniRuleWorkflowContext){
        Map<String, FlightSolutionWorkflowDTO> flightSolutionMap = new HashMap<>();
        flightSolutionMap.put(miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getSolutionId(), miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getFlightSolution());
        return flightSolutionMap;
    }

    @Override
    public void handleTaskOutput(MiniRuleWorkflowResponse response, MiniRuleWorkflowContext miniRuleWorkflowContext) {
        miniRuleWorkflowContext.setWorkflowResponse(response);
    }

    @Override
    public void process(MiniRuleWorkflowContext miniRuleWorkflowContext) {
        ResponseConstructionTaskRequestDTO domainResponseConstructionTaskRequestDTO = prepareTaskInput(miniRuleWorkflowContext);
        MiniRuleWorkflowResponse response = this.getTasks().get(0).run(domainResponseConstructionTaskRequestDTO);
        handleTaskOutput(response, miniRuleWorkflowContext);
    }

    @Override
    public String getNodeName() {
        return FINAL_CONSTRUCTION_NODE;
    }
}
