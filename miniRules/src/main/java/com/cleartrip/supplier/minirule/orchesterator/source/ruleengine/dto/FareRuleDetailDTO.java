package com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.stream.Collectors;

@Builder
@Getter
@Setter
public class FareRuleDetailDTO {
    private String Id;
    private String comboFbc;
    private String journeyKey;
    private String paxType;
    private String ruleType;
    private String strategy;
    private String ldx;
    private String ldxTimeFrame;
    private String udx;
    private String udxTimeFrame;
    private String applicableRule;
    private String applicableRuleAmount;
    private Double finalAmount;
    private String currency;
    private Boolean permitted;
}
