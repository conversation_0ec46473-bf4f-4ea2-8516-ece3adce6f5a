package com.cleartrip.supplier.minirule.workflow.nodes.execution;

import com.cleartrip.air.sms.api.constants.enums.AirSupplier;
import com.cleartrip.supplier.minirule.repository.model.response.FareRuleDetail;
import com.cleartrip.supplier.minirule.workflow.adapters.MiniRuleRepoRequestAdapter;
import com.cleartrip.supplier.minirule.workflow.adapters.MiniRuleRepoResponseAdapter;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.FareRuleAggregatorRequest;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.FareRuleDetailWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.MiniRuleWorkflowContext;
import com.cleartrip.supplier.minirule.workflow.tasks.execution.FareRuleAggregatorTask;
import com.cleartrip.utility.workflow.impl.SequentialNodeImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FareRuleAggregatorNode extends SequentialNodeImpl<FareRuleAggregatorRequest, List<FareRuleDetail>, MiniRuleWorkflowContext, FareRuleAggregatorTask> {

    private static final String UNDERSCORE = "_";

    private final MiniRuleRepoRequestAdapter repoRequestAdapter;

    private final MiniRuleRepoResponseAdapter repoResponseAdapter;

    public FareRuleAggregatorNode(){
        this.repoRequestAdapter = new MiniRuleRepoRequestAdapter();
        this.repoResponseAdapter = new MiniRuleRepoResponseAdapter();
    }

    @Override
    public FareRuleAggregatorRequest prepareTaskInput(MiniRuleWorkflowContext miniRuleWorkflowContext) {
        if(AirSupplier.GALILEO.getDisplayName().equalsIgnoreCase(getSupplier(miniRuleWorkflowContext)) ||
                AirSupplier.FLY_DUBAI.getDisplayName().equalsIgnoreCase(getSupplier(miniRuleWorkflowContext)) ||
                AirSupplier.AKASA_AIR.getDisplayName().equalsIgnoreCase(getSupplier(miniRuleWorkflowContext))) {
            return FareRuleAggregatorRequest.builder()
                    .fareRuleDetailList(repoRequestAdapter.convert(miniRuleWorkflowContext.getFareRulesSupplier()))
                    .supplier(getSupplier(miniRuleWorkflowContext))
                    .isSpecialRT(isSplRT(miniRuleWorkflowContext))
                    .RTKey(getRTkey(miniRuleWorkflowContext))
                    .isInternational(miniRuleWorkflowContext.getMiniRuleWorkflowRequest().isInternational())
                    .build();
        }
        return FareRuleAggregatorRequest.builder()
                .fareRuleDetailList(repoRequestAdapter.convert(miniRuleWorkflowContext.getFareRulesRuleEngine()))
                .supplier(getSupplier(miniRuleWorkflowContext))
                .isSpecialRT(isSplRT(miniRuleWorkflowContext))
                .RTKey(getRTkey(miniRuleWorkflowContext))
                .isInternational(miniRuleWorkflowContext.getMiniRuleWorkflowRequest().isInternational())
                .build();
    }

    @Override
    public void process(MiniRuleWorkflowContext miniRuleWorkflowContext) {
        log.error(getNodeName());
        List<FareRuleDetail> fareRuleDetails = this.getTasks().get(0).run(prepareTaskInput(miniRuleWorkflowContext));
        handleTaskOutput(fareRuleDetails, miniRuleWorkflowContext);
    }

    @Override
    public void handleTaskOutput(List<FareRuleDetail> fareRuleDetails, MiniRuleWorkflowContext miniRuleWorkflowContext) {
        List<FareRuleDetailWorkflowDTO> fareRuleDetailWorkflowDTOS = repoResponseAdapter.convert(fareRuleDetails);
        if(AirSupplier.GALILEO.getDisplayName().equalsIgnoreCase(getSupplier(miniRuleWorkflowContext)) ||
                AirSupplier.FLY_DUBAI.getDisplayName().equalsIgnoreCase(getSupplier(miniRuleWorkflowContext)) ||
                AirSupplier.AKASA_AIR.getDisplayName().equalsIgnoreCase(getSupplier(miniRuleWorkflowContext))){
            miniRuleWorkflowContext.setFareRulesSupplier(fareRuleDetailWorkflowDTOS);
        }
        else{
            miniRuleWorkflowContext.setFareRulesRuleEngine(fareRuleDetailWorkflowDTOS);
        }

    }

    private String getSupplier(MiniRuleWorkflowContext miniRuleWorkflowContext){
        return miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getSupplier();
    }

    private boolean isSplRT(MiniRuleWorkflowContext context){
        return context.getMiniRuleWorkflowRequest().getFlightSolution().getFlightDetailsMap().size() > 1;
    }

    private String getRTkey(MiniRuleWorkflowContext context){
        List<String> journeyKeys = new ArrayList<>(context.getMiniRuleWorkflowRequest().getFlightSolution().getFlightDetailsMap().keySet());
        String origin = journeyKeys.get(0).split(UNDERSCORE)[0];
        return origin + UNDERSCORE + origin;
    }

    @Override
    public String getNodeName() {
        return null;
    }
}
