package com.cleartrip.supplier.minirule.contoller;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.farebenefits.application.IFareBenefitsApplicationService;
import com.cleartrip.supplier.farebenefits.application.adapter.FareBenefitApplicationRequestAdapter;
import com.cleartrip.supplier.farebenefits.application.adapter.FareBenefitsProtoResponseAdapter;
import com.cleartrip.supplier.farebenefits.application.dto.request.FareBenefitsRequestDto;
import com.cleartrip.supplier.farebenefits.application.dto.response.FareBenefitsResponseDto;
import com.google.gson.Gson;
import com.google.protobuf.util.JsonFormat;
import com.cleartrip.supplier.minirule.application.IMiniRuleApplicationService;
import com.cleartrip.supplier.minirule.application.models.request.MiniRuleRequest;
import com.cleartrip.supply.core.api.proto.FareBenefitsRequestOuterClass;
import com.cleartrip.supply.core.api.proto.FareBenefitsResponseOuterClass;
import com.cleartrip.supplier.minirule.application.models.requestV2.ApiContext;
import com.cleartrip.supplier.minirule.application.models.requestV2.MiniRuleApplicationRequest;
import com.cleartrip.supplier.minirule.application.models.responseV2.MiniRuleApplicationResponse;
import com.cleartrip.supply.core.api.proto.MiniRuleRequestListOuterClass;
import com.cleartrip.supply.core.api.proto.MiniRuleResponseListOuterClass;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
@Singleton
@Path("")
@Produces("application/x-protobuf")
@Consumes("application/x-protobuf")
public class MiniRuleResource {

    private final IMiniRuleApplicationService miniRuleApplicationService;
    private final IFareBenefitsApplicationService fareBenefitsApplicationService;
    private final FareBenefitApplicationRequestAdapter fareBenefitApplicationRequestAdapter;
    private final Function<FareBenefitsResponseDto, FareBenefitsResponseOuterClass.FareBenefitsResponse> fareBenefitsResponseAdapter;


    @Inject
    public MiniRuleResource(IMiniRuleApplicationService miniRuleApplicationService,
                            IFareBenefitsApplicationService fareBenefitsApplicationService) {
        this.miniRuleApplicationService = miniRuleApplicationService;
        this.fareBenefitsApplicationService = fareBenefitsApplicationService;
        this.fareBenefitApplicationRequestAdapter = new FareBenefitApplicationRequestAdapter();
        this.fareBenefitsResponseAdapter = new FareBenefitsProtoResponseAdapter();
    }

    @POST
    @Path("/fare-rule/{itineraryId}")
    public MiniRuleResponseListOuterClass.MiniRuleResponseList getHoldResponse(MiniRuleRequestListOuterClass.MiniRuleRequestList request, @PathParam("itineraryId") String itineraryId) {
        try {
            log.error("---> fare-rule: {}", (new Gson()).toJson(request));

        } catch (Exception e) {
            log.error("Exception while ---> fare-rule/: {}", e.getMessage());
        }

        final Context context = getContext(itineraryId);
        log.error("Received Request for /fare-rule/{itin-id} {}", request);
        return miniRuleApplicationService.fetchMiniRulesV2(request, context);
    }

    @POST
    @Path("/fare-rule/test/{itineraryId}")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public MiniRuleResponseListOuterClass.MiniRuleResponseList getHoldResponseTest(String request, @PathParam("itineraryId") String itineraryId) {
        try {
            final Context context = getContext(itineraryId);
            MiniRuleRequestListOuterClass.MiniRuleRequestList.Builder builder = MiniRuleRequestListOuterClass.MiniRuleRequestList.newBuilder();
            JsonFormat.parser().merge(request, builder);
            MiniRuleRequestListOuterClass.MiniRuleRequestList miniRuleRequestList1 = builder.build();
            return miniRuleApplicationService.fetchMiniRulesV2(miniRuleRequestList1, context);
        }catch (Exception ex){
            ex.printStackTrace();
            return null;
        }
    }

    @POST
    @Path("/fare-benefits/{itineraryId}")
    public FareBenefitsResponseOuterClass.FareBenefitsResponse getFareBenefits(FareBenefitsRequestOuterClass.FareBenefitsRequest fareBenefitsRequest, @PathParam("itineraryId") String itineraryId) {
        try {
            log.error("---> fare-benefits/", (new Gson()).toJson(fareBenefitsRequest));

        } catch (Exception e) {
            log.error("Exception while ---> fare-benefits/: {}", e.getMessage());
        }
        final Context context = getContext(itineraryId);
        FareBenefitsRequestDto applicationRequest = fareBenefitApplicationRequestAdapter.apply(fareBenefitsRequest, context);
        FareBenefitsResponseDto response = fareBenefitsApplicationService.fetchFareBenefits(applicationRequest);
        return fareBenefitsResponseAdapter.apply(response);
    }

    @POST
    @Path("/fare-ruleV2")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public MiniRuleApplicationResponse getFareRules(MiniRuleApplicationRequest request) {
        try {
            log.error("---> /fare-ruleV2 ", (new Gson()).toJson(request));

        } catch (Exception e) {
            log.error("Exception while ---> /fare-ruleV2: {}", e.getMessage());
        }
        final Context context = getContextV2(request.getApiContext());
        return miniRuleApplicationService.fetchMiniRulesV3(request, context);
    }

    @POST
    @Path("/fare-ruleV3")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public MiniRuleApplicationResponse getFareRulesV3(MiniRuleRequest request) {
        final Context context = getContextV3(request.getContext());
        return miniRuleApplicationService.fetchMiniRulesV4(request, context);
    }

    private Context getContextV3(Context context) {
        if(Objects.isNull(context) || Objects.isNull(context.getItineraryId())){
            return Context.builder()
                    .build();
        }
        return Context.builder()
                .itineraryId(context.getItineraryId())
                .build();
    }

    private Context getContextV2(ApiContext apiContext){
        if(Objects.isNull(apiContext) || Objects.isNull(apiContext.getItineraryId())){
            return Context.builder()
                    .build();
        }
        return Context.builder()
                .itineraryId(apiContext.getItineraryId())
                .build();
    }

    private Context getContext(String itineraryId) {
        return Context.builder()
                .itineraryId(itineraryId)
                .build();
    }
}
