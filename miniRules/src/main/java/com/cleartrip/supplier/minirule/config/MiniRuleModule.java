package com.cleartrip.supplier.minirule.config;


import com.cleartrip.air.themis.client.exception.APIExecutionFailedException;
import com.cleartrip.air.themis.client.exception.ResourceNotFoundException;
import com.cleartrip.air.themis.client.service.ThemisClientService;
import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.farebenefits.application.IFareBenefitsApplicationService;
import com.cleartrip.supplier.farebenefits.application.impl.FareBenefitsApplicationServiceImpl;
import com.cleartrip.supplier.farebenefits.domain.IFareBenefitsDomainService;
import com.cleartrip.supplier.farebenefits.domain.impl.FareBenefitsDomainServiceImpl;
import com.cleartrip.supplier.farebenefits.enricher.IFareBenefitEnricher;
import com.cleartrip.supplier.farebenefits.enricher.impl.FareBenefitDomainEnricher;
import com.cleartrip.supplier.farebenefits.orchestration.IFareBenefitsOrchestrator;
import com.cleartrip.supplier.farebenefits.orchestration.source.ruleEngine.FareBenefitsOrchestratorImpl;
import com.cleartrip.supplier.infrastructure.factory.RetrofitClientFactoryImpl;
import com.cleartrip.supplier.infrastructure.factory.RetrofitConverterFactoryGeneratorImpl;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.ConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JSONConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JacksonConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JsonSubTypeConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.dto.ClientConfig;
import com.cleartrip.supplier.infrastructure.factory.dto.PoolConfiguration;
import com.cleartrip.supplier.infrastructure.factory.dto.RetroClientBuilderRequest;
import com.cleartrip.supplier.infrastructure.factory.dto.TimeoutConfig;
import com.cleartrip.supplier.infrastructure.factory.dto.iodata.jsondata.JacksonJsonDataInfo;
import com.cleartrip.supplier.minirule.application.IMiniRuleApplicationService;
import com.cleartrip.supplier.minirule.application.impl.MiniRuleApplicationServiceImpl;
import com.cleartrip.supplier.minirule.application.strategy.FareFamilyMiniRuleFetchingStrategy;
import com.cleartrip.supplier.minirule.application.strategy.MiniRuleFetchingStrategy;
import com.cleartrip.supplier.minirule.config.models.MonitoringDetails;
import com.cleartrip.supplier.minirule.config.models.RetrofitClientParam;
import com.cleartrip.supplier.minirule.domain.service.IMiniRuleService;
import com.cleartrip.supplier.minirule.domain.service.MiniRuleDomainService;
import com.cleartrip.supplier.minirule.domain.service.impl.MiniRuleDomainServiceImpl;
import com.cleartrip.supplier.minirule.domain.service.impl.MiniRuleServiceImpl;
import com.cleartrip.supplier.minirule.orchesterator.IMIniRuleOrchestratorV2;
import com.cleartrip.supplier.minirule.orchesterator.IMiniRuleOrchestratorV2Impl;
import com.cleartrip.supplier.minirule.repository.IFetchMiniRuleFromSupplierRepository;
import com.cleartrip.supplier.minirule.repository.IFetchSearchSolutionRepository;
import com.cleartrip.supplier.minirule.repository.IMiniRulesCacheRepository;
import com.cleartrip.supplier.minirule.repository.caching.CachingService;
import com.cleartrip.supplier.minirule.repository.caching.impl.redis.RedisCachingServiceTemplate;
import com.cleartrip.supplier.minirule.repository.caching.impl.redis.ZstdRedisSerializer;
import com.cleartrip.supplier.minirule.repository.impl.FetchMiniRuleFromSupplierRepositoryImpl;
import com.cleartrip.supplier.minirule.repository.impl.FetchSearchSolutionRepositoryImpl;
import com.cleartrip.supplier.minirule.repository.impl.MiniRuleCacheRepositoryImpl;
import com.cleartrip.supplier.minirule.repository.invoker.SMSInvoker;
import com.cleartrip.supplier.minirule.timelinegenerator.Charge;
import com.cleartrip.supplier.minirule.timelinegenerator.TimelineGenerator;
import com.cleartrip.supplier.minirule.utils.JsonUtil;
import com.cleartrip.supplier.minirule.utils.StrategyUtil;
import com.cleartrip.supplier.minirule.workflow.keyGenerator.KeyGenerator;
import com.cleartrip.supplier.minirule.workflow.keyGenerator.MiniRuleCacheKeyGenerator;
import com.cleartrip.supplier.rule.FareRuleConfigRuleEngine;
import com.cleartrip.supplier.search.AvailabilitySolutionService;
import com.cleartrip.supplier.search.AvailabilitySolutionServiceImpl;
import com.cleartrip.supplier.search.application.FlightAvailApplication;
import com.cleartrip.supplier.search.application.FlightAvailApplicationImpl;
import com.cleartrip.supplier.search.ruleEngine.RuleEngineResourceObserver;
import com.cleartrip.supplier.search.ruleEngine.RuleEngineWrapper;
import com.cleartrip.supply.core.api.proto.CachedMiniRules;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.google.inject.name.Names;
import common.monitoring.bean.MonitoringBeanContext;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.support.ConnectionPoolSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStaticMasterReplicaConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import retrofit2.Retrofit;
import ru.vyarus.dropwizard.guice.module.support.DropwizardAwareModule;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MiniRuleModule extends DropwizardAwareModule<MiniRuleConfiguration> {

    private static final String FARE_BENEFIT_TABLE_NAME = "fareBenefits";

    @Override
    protected void configure() {
        bind(IMiniRuleApplicationService.class).to(MiniRuleApplicationServiceImpl.class).in(Singleton.class);
        bind(IMiniRuleService.class).to(MiniRuleServiceImpl.class).in(Singleton.class);
        bind(IFetchMiniRuleFromSupplierRepository.class).to(FetchMiniRuleFromSupplierRepositoryImpl.class).in(Singleton.class);
        bind(IMiniRulesCacheRepository.class).to(MiniRuleCacheRepositoryImpl.class).in(Singleton.class);
        bind(KeyGenerator.class).to(MiniRuleCacheKeyGenerator.class).in(Singleton.class);
        bind(IFetchSearchSolutionRepository.class).to(FetchSearchSolutionRepositoryImpl.class).in(Singleton.class);
        bind(MiniRuleDomainService.class).to(MiniRuleDomainServiceImpl.class).in(Singleton.class);
        bind(IMIniRuleOrchestratorV2.class).to(IMiniRuleOrchestratorV2Impl.class).in(Singleton.class);
        bind(IFareBenefitsOrchestrator.class).to(FareBenefitsOrchestratorImpl.class).in(Singleton.class);
        bind(IFareBenefitEnricher.class).to(FareBenefitDomainEnricher.class).in(Singleton.class);
        bind(IFareBenefitsApplicationService.class).to(FareBenefitsApplicationServiceImpl.class).in(Singleton.class);
        bind(IFareBenefitsDomainService.class).to(FareBenefitsDomainServiceImpl.class).in(Singleton.class);
        bind(FlightAvailApplication.class).annotatedWith(Names.named("FlightAvailApplicationImpl"))
                .to(FlightAvailApplicationImpl.class).in(Singleton.class);
        bind(MiniRuleFetchingStrategy.class).annotatedWith(Names.named("FareFamilyMiniRuleFetchingStrategy"))
                .to(FareFamilyMiniRuleFetchingStrategy.class).in(Singleton.class);
        bind(AvailabilitySolutionService.class).to(AvailabilitySolutionServiceImpl.class).in(Singleton.class);
    }

    @Named("miniRuleRedisConnectionFactory")
    @Singleton
    @Provides
    public RedisConnectionFactory redisConnectionFactory(@Named("miniRuleRedisConfiguration") RedisConfiguration redisConfiguration,
                                                         ClientOptions options, ClientResources dcr) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(80);
        genericObjectPoolConfig.setMinIdle(50);
        genericObjectPoolConfig.setMaxTotal(600);
        LettucePoolingClientConfiguration lettucePoolConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(options)
                .clientResources(dcr)
                .build();
        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(redisConfiguration, lettucePoolConfig);
        lettuceConnectionFactory.afterPropertiesSet();
        return lettuceConnectionFactory;
    }


    @Singleton
    @Provides
    @Named("miniRuleRetrofitClientFactory")
    public com.cleartrip.supplier.infrastructure.factory.RetroClientFactory getRetrofitClientFactory() {
        List<JsonSubTypeConverterFactoryHandler> jsonSubTypeConverterFactoryHandlers = Collections.singletonList(new JacksonConverterFactoryHandler());
        JSONConverterFactoryHandler jsonConverterFactoryHandler = new JSONConverterFactoryHandler(jsonSubTypeConverterFactoryHandlers);
        List<ConverterFactoryHandler> converterFactoryHandlerList = Collections.singletonList(jsonConverterFactoryHandler);
        final RetrofitConverterFactoryGeneratorImpl retrofitConverterFactoryGenerator = new RetrofitConverterFactoryGeneratorImpl(converterFactoryHandlerList);
        return new RetrofitClientFactoryImpl(retrofitConverterFactoryGenerator);
    }


    @Singleton
    @Provides
    @Named("miniRuleSmsInvoker")
    public SMSInvoker getSmsInvoker(
            @Named("miniRuleRetrofitClientFactory") com.cleartrip.supplier.infrastructure.factory.RetroClientFactory retroClientFactory,
            @Named("miniRuleObjectMapper") ObjectMapper objectMapper,
            MiniRuleConfiguration miniRuleConfiguration
    ) {
        final RetrofitClientParam retrofitClientParam = miniRuleConfiguration.getRetrofitClientParam();
        final RetrofitClientParam.PoolConfiguration poolConfiguration = retrofitClientParam.getPoolConfiguration();
        final RetrofitClientParam.TimeoutConfiguration timeoutConfiguration = retrofitClientParam.getTimeoutConfiguration();
        Retrofit retro = retroClientFactory.createRetrofitClient(RetroClientBuilderRequest.builder()
                .clientConfig(ClientConfig.builder()
                        .baseURL(miniRuleConfiguration.getSmsDns())
                        .poolConfiguration(PoolConfiguration.builder()
                                .keepAliveTimeInMillis(poolConfiguration.getKeepAliveTimeInMillis())
                                .maxIdleConnections(poolConfiguration.getMaxIdleConnections())
                                .maxRequests(poolConfiguration.getMaxRequests())
                                .maxRequestsPerHost(poolConfiguration.getMaxRequestsPerHost())
                                .build())
                        .timeoutConfig(TimeoutConfig.builder()
                                .connectTimeoutInMillis(timeoutConfiguration.getConnectTimeoutInMillis())
                                .overallCallTimeoutInMillis(timeoutConfiguration.getOverallCallTimeoutInMillis())
                                .readTimeoutInMillis(timeoutConfiguration.getReadTimeoutInMillis())
                                .writeTimeoutInMillis(timeoutConfiguration.getWriteTimeoutInMillis())
                                .build())
                        .build())
                .ioDataInfo(JacksonJsonDataInfo.newInstanceWithObjectMapper(objectMapper))
                .build());
        return retro.create(SMSInvoker.class);
    }

    @Singleton
    @Provides
    @Named("miniRuleJsonUtil")
    public JsonUtil getJsonUtil(@Named("miniRuleObjectMapper") ObjectMapper mapper){
        return new JsonUtil(mapper);
    }

    @Singleton
    @Provides
    @Named("miniRuleStrategyUtil")
    public StrategyUtil getStrategyUtil(SearchConfigContainer configContainer, ObjectMapper mapper){
        return new StrategyUtil(configContainer, mapper);
    }

    @Singleton
    @Provides
    @Named("miniRuleObjectMapper")
    public ObjectMapper getObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new GuavaModule());
        mapper.registerModule(new Jdk8Module());
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
//        mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        mapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        mapper.setVisibility(mapper
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .getSerializationConfig()
                .getDefaultVisibilityChecker()
                .withFieldVisibility(JsonAutoDetect.Visibility.ANY)
                .withGetterVisibility(JsonAutoDetect.Visibility.NONE)
                .withSetterVisibility(JsonAutoDetect.Visibility.NONE)
                .withCreatorVisibility(JsonAutoDetect.Visibility.ANY));
        return mapper;
    }

    @Singleton
    @Provides
    @Named("miniRuleCacheService")
    CachingService<CachedMiniRules.CachedMiniRuleResponse> miniRuleCacheDataCachingService(@Named("miniRuleRedisTemplate") RedisTemplate<String, CachedMiniRules.CachedMiniRuleResponse> redisTemplate) {
        return new RedisCachingServiceTemplate<>(redisTemplate);
    }

    @Singleton
    @Provides
    @Named("prepareTimeLineExecutorService")
    ExecutorService prepareTimeLineExecutorService() {
        return new ThreadPoolExecutor(10, 100, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(4));
    }

    @Singleton
    @Provides
    @Named("miniRuleOrchestratorExecutorService")
    ExecutorService prepareMiniRuleOrchestratorExecutorService() {
        return new ThreadPoolExecutor(10, 50, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(4));
    }

    @Singleton
    @Provides
    @Named("miniRuleRedisTemplate")
    public RedisTemplate<String, CachedMiniRules.CachedMiniRuleResponse> redisMiniRulesTemplate(@Named("miniRuleRedisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, CachedMiniRules.CachedMiniRuleResponse> miniRuleCacheDataRedisTemplate = new RedisTemplate<>();
        miniRuleCacheDataRedisTemplate.setConnectionFactory(cf);
        miniRuleCacheDataRedisTemplate.setKeySerializer(new StringRedisSerializer());
        miniRuleCacheDataRedisTemplate.setValueSerializer(new ZstdRedisSerializer<>());
        miniRuleCacheDataRedisTemplate.afterPropertiesSet();
        return miniRuleCacheDataRedisTemplate;
    }

    @Singleton
    @Provides
    @Named("miniRuleRedisClient")
    public RedisClient redisClient(MiniRuleConfiguration properties) {
        RedisURI redisURI = RedisURI.builder()
                .withHost(properties.getRedisConnectionDetails().getHost())
                .withPort(properties.getRedisConnectionDetails().getPort())
                .build();
        RedisClient client = RedisClient.create(redisURI);
        return client;
    }

    @Singleton
    @Provides
    @Named("miniRuleRedisConfiguration")
    public RedisConfiguration redisStandaloneConfiguration(MiniRuleConfiguration properties) {
        //return new RedisStandaloneConfiguration(env.getProperty("redis.host"), Integer.parseInt(env.getProperty("redis.port")));
        return new RedisStaticMasterReplicaConfiguration(properties.getRedisConnectionDetails().getHost(), properties.getRedisConnectionDetails().getPort());
    }

    @Singleton
    @Provides
    @Named("GenericObjectPool")
    public GenericObjectPool<StatefulRedisConnection<String, String>> statefulRedisConnection(@Named("miniRuleRedisClient") RedisClient redisClient,
                                                                                              MiniRuleConfiguration properties) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(properties.getRedisConnectionDetails().getRedisPoolMaxIdleConnection());
        genericObjectPoolConfig.setMinIdle(properties.getRedisConnectionDetails().getRedisPoolMinIdleConnection());
        genericObjectPoolConfig.setMaxTotal(properties.getRedisConnectionDetails().getRedisPoolMaxTotalConnection());
        return ConnectionPoolSupport
                .createGenericObjectPool(() -> redisClient.connect(), genericObjectPoolConfig);
    }

    @Singleton
    @Provides
    @Named("fareBenefitNewRelicHelper")
    public NewRelicHelper getNewRelicHelper(MonitoringBeanContext monitoringBeanContext) {
        return new NewRelicHelper(monitoringBeanContext.getCommonPublisher(), FARE_BENEFIT_TABLE_NAME);
    }

    @Singleton
    @Provides
    @Named("fareBenefitsOrchestratorExecutorService")
    ExecutorService prepareFareBenefitsOrchestratorExecutorService() {
        return new ThreadPoolExecutor(10, 50, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(1));
    }

    @Singleton
    @Provides
    @Named("fareBenefitsWorkflowExecutorService")
    ExecutorService preparefareBenefitsWorkflowExecutorService() {
        return new ThreadPoolExecutor(10, 100, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(2));
    }

    @Singleton
    @Provides
    @Named("timeLineGenerator")
    TimelineGenerator<Charge> prepareTimeLineGenerator() {
        return new TimelineGenerator<>();
    }

    @Singleton
    @Provides
    @Named("miniRuleNewRelicHelper")
    public NewRelicHelper getNewRelicHelper(Provider<MiniRuleConfiguration> miniRuleConfiguration, MonitoringBeanContext monitoringBeanContext) {
        final MonitoringDetails monitoringDetails = miniRuleConfiguration.get().getMonitoringDetails();
        final String tableName = monitoringDetails.getNewRelic().getTableName();
        return new NewRelicHelper(monitoringBeanContext.getCommonPublisher(), tableName);
    }

    @Singleton
    @Provides
    @Named("miniRuleStatsHelper")
    public StatsHelper getStatsHelper(Provider<MiniRuleConfiguration> miniRuleConfiguration, MonitoringBeanContext monitoringBeanContext) {
        final MonitoringDetails monitoringDetails = miniRuleConfiguration.get().getMonitoringDetails();
        final String topicName = monitoringDetails.getStats().getTopicName();
        return new StatsHelper(monitoringBeanContext.getCommonPublisher(), topicName);
    }


    @Named("themisClientServiceMiniRule")
    @Singleton
    @Provides
    public ThemisClientService themisClientService(SearchConfigContainer properties) {
        String themisUrl = properties.getPropertyValue("ct.sos.themis.url", "https://qa-themis.cleartripcorp.com");
        String themisBucket = properties.getPropertyValue("ct.sos.themis.gcs.bucket", "qa-themis-resource-data");
        return new ThemisClientService(themisUrl, themisBucket, 5);
    }

    @Named("fareRuleConfigRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FareRuleConfigRuleEngine> getFareRulesRepo(@Named("themisClientServiceMiniRule") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FareRuleConfigRuleEngine> wrapper = new RuleEngineWrapper<>(FareRuleConfigRuleEngine.class);
//        wrapper.refreshResource(new File("miniRules/src/main/resources/openl/qa/fareRule_8607027e78cebea8c3d82c91de486b87.xlsx"));
                wrapper.refreshResource(new File("miniRules/src/main/resources/openl/qa/fareRule_V5.xlsx"));
//        themisClientService.registerResource("ct.sos.rule.fareRules.v2", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }


    @Singleton
    @Provides
    @Named("miniRuleOrchestratorExecutorServiceV2")
    ExecutorService prepareMiniRuleOrchestratorExecutorServiceV2() {
        return new ThreadPoolExecutor(10, 50, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(4));
    }

    @Singleton
    @Provides
    @Named("fetchMiniRuleFromRuleEngineExecutor")
    ExecutorService prepareFetchMiniRuleFromExecutorService() {
        return new ThreadPoolExecutor(50, 500, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(4));
    }


}
