package com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.adapters;

import com.cleartrip.supplier.minirule.application.models.response.*;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.RuleEvaluator;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.dto.FareRuleDetailDTO;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.dto.FareRuleDetailResponse;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.exception.MiniRuleOrchestratorException;
import com.cleartrip.supplier.minirule.workflow.dto.MiniRuleWorkflowContext;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.FareDetailsWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.*;

import java.util.*;
import java.util.stream.Collectors;

public class ResponseAdapter {

    public static final String DOUBLE_DASH = "__";
    public static final String DAY = "D";
    public static final String HOUR = "H";
    public static final String MINUTE = "M";
    public static final String PERIOD = "P";
    public static final String TIME = "T";
    public static final String DEFAULT_CURRENCY = "";

    private static final String AMEND_SAME_FARE = "AMEND_SAME_FARE";

    private static final String AMEND_HIGHER_FARE = "AMEND_HIGHER_FARE";

    private final String NON_CHANGEABLE = "NON_CHANGEABLE";

    private final String NON_REFUNDABLE = "NON_REFUNDABLE";
    private final String CHANGEABLE = "CHANGEABLE";
    private final String REFUNDABLE = "REFUNDABLE";

//    public List<FareRuleDetailWorkflowDTO> adapt(List<FareRuleDetailResponse> list, MiniRuleWorkflowContext context){
//        return context.getMiniRuleWorkflowRequest().getFlightSolution().getFareDetailsList().stream()
//                .map(FareDetailsWorkflowDTO::getComboFbc)
//                .map(comboFbc -> {
//                    String solutionId = context.getMiniRuleWorkflowRequest().getSolutionId();
//                    List<FareRuleDetailDTO> fareRuleDetailDTOS = list.stream()
//                                                                .flatMap(fareRuleDetailResponse -> fareRuleDetailResponse.getFareRuleList().stream())
//                                                                .filter(fareRuleDetailDTO -> comboFbc.equalsIgnoreCase(fareRuleDetailDTO.getComboFbc()))
//                                                                .collect(Collectors.toList());
//                    return FareRuleDetailWorkflowDTO.builder()
//                            .solutionId(solutionId)
//                            .comboFbd(comboFbc)
//                            .fareRuleId(solutionId + DOUBLE_DASH + comboFbc)
//                            .fareRules(getJourneyWiseFareRules(fareRuleDetailDTOS, context))
//                            .build();
//                }).collect(Collectors.toList());
//    }
    public List<FareRuleDetailWorkflowDTO> adapt(List<FareRuleDetailResponse> list, MiniRuleWorkflowContext context) {
        return context.getMiniRuleWorkflowRequest().getFlightSolution().getFareDetailsList().stream()
                .map(fareDetailWorkflowDTO -> {
                    String comboFbc = fareDetailWorkflowDTO.getComboFbc();
                    String solutionId = context.getMiniRuleWorkflowRequest().getSolutionId();
                    String ctBrandName = fareDetailWorkflowDTO.getCtBrandName();

                    if(list.isEmpty()){
                        return FareRuleDetailWorkflowDTO.builder()
                                .solutionId(solutionId)
                                .comboFbd(comboFbc)
                                .ctBrandName(ctBrandName)
                                .fareRuleId(solutionId + DOUBLE_DASH + comboFbc)
                                .fareRules(Collections.emptyMap())
                                .build();
                    }
                    List<FareRuleDetailDTO> fareRuleDetailDTOS = list.stream()
                            .flatMap(fareRuleDetailResponse -> fareRuleDetailResponse.getFareRuleList().stream())
                            .filter(fareRuleDetailDTO -> comboFbc.equalsIgnoreCase(fareRuleDetailDTO.getComboFbc()))
                            .collect(Collectors.toList());

                    return FareRuleDetailWorkflowDTO.builder()
                            .solutionId(solutionId)
                            .comboFbd(comboFbc)
                            .ctBrandName(ctBrandName)
                            .fareRuleId(solutionId + DOUBLE_DASH + comboFbc)
                            .fareRules(getJourneyWiseFareRules(fareRuleDetailDTOS, context))
                            .build();
                })
                .collect(Collectors.toList());
    }



    private Map<String, List<FareRuleWorkflowDTO>> getJourneyWiseFareRules(List<FareRuleDetailDTO> fareRules, MiniRuleWorkflowContext context) {
        Map<String, List<FareRuleWorkflowDTO>> journeyWiseFareRuleMap = new HashMap<>();
        List<String> journeyKeys = new ArrayList<>(context.getMiniRuleWorkflowRequest().getFlightSolution().getFlightDetailsMap().keySet());
        for (String jk : journeyKeys) {
            List<FareRuleDetailDTO> journeyWiseFareRules = fareRules.stream()
                    .filter(fareRuleDetailDTO -> jk.equalsIgnoreCase(fareRuleDetailDTO.getJourneyKey()))
                    .collect(Collectors.toList());

            journeyWiseFareRuleMap.put(jk, getFareRules(journeyWiseFareRules));
        }
        return journeyWiseFareRuleMap;
    }

    private List<FareRuleWorkflowDTO> getFareRules(List<FareRuleDetailDTO> fareRules){
        Map<String, FareRuleWorkflowDTO> fareRuleTypeMap = fareRules.stream()
                        .collect(Collectors.groupingBy(FareRuleDetailDTO::getRuleType,
                                Collectors.collectingAndThen(Collectors.toList(), list -> FareRuleWorkflowDTO.builder()
                                                                                                            .fareRuleType(getFareRuleType(list.get(0).getRuleType()))
                                                                                                            .permitted(true)
                                                                                                            .fareRuleInfoText(null)
                                                                                                            .charges(convertToFareRuleCharge(list))
                                                                                                            .build()
                                )));
        return new ArrayList<>(fareRuleTypeMap.values());

    }

    private FareRuleType getFareRuleType(String value){
        Optional<FareRuleType> fareRuleType = FareRuleType.filterValue(value.toUpperCase());
        return fareRuleType.orElseThrow(() -> new MiniRuleOrchestratorException("No fare of type " + value, null));
    }

    private List<FareRuleChargeWorkflowDTO> convertToFareRuleCharge(List<FareRuleDetailDTO> fareRules){
        return fareRules.stream()
                .map(this::getFareRuleCharge)
                .collect(Collectors.toList());
    }

    private FareRuleChargeWorkflowDTO getFareRuleCharge(FareRuleDetailDTO fareRule){
        return FareRuleChargeWorkflowDTO.builder()
                .timeLine(getTimeline(fareRule))
                .passengerFareRuleCharges(getPassengerFareRuleCharge(fareRule))
                .permitted(fareRule.getPermitted())
                .chargeInfoText(getChargeInfoText(fareRule))
                .departureType(null)
                .build();
    }

    private String getChargeInfoText(FareRuleDetailDTO fareRule){
        if(!fareRule.getPermitted()){
            if(Arrays.asList(AMEND_SAME_FARE, AMEND_HIGHER_FARE).contains(fareRule.getRuleType())){
                return NON_CHANGEABLE;
            }
            return NON_REFUNDABLE;
        }
        else{
            if(Arrays.asList(AMEND_SAME_FARE, AMEND_HIGHER_FARE).contains(fareRule.getRuleType())){
                return CHANGEABLE;
            }
            return REFUNDABLE;
        }
    }

    private TimelineWorkflowDTO getTimeline(FareRuleDetailDTO fareRule){
        String ldxUnit = getTimeLineUnit(fareRule.getLdxTimeFrame());
        String udxUnit = getTimeLineUnit(fareRule.getUdxTimeFrame());
        return TimelineWorkflowDTO.builder()
                .from(getPrefix(ldxUnit) + fareRule.getLdx() + ldxUnit)
                .to(getPrefix(udxUnit) + fareRule.getUdx() + udxUnit)
                .build();
    }

    private String getPrefix(String timeLineUnit){
        if(timeLineUnit.equalsIgnoreCase(DAY)) return PERIOD;
        return PERIOD + TIME;
    }

    private Map<TravellerType, AmountWorkflowDTO> getPassengerFareRuleCharge(FareRuleDetailDTO fareRule){
        Map<TravellerType, AmountWorkflowDTO> map = new HashMap<>();
        map.put(getTravellerType(fareRule), getAmount(fareRule));
        return map;
    }

    private TravellerType getTravellerType(FareRuleDetailDTO fareRule){
        if("ADT".equalsIgnoreCase(fareRule.getPaxType())){
            return TravellerType.ADULT;
        }
        if("CHD".equalsIgnoreCase(fareRule.getPaxType())){
            return TravellerType.CHILD;
        }
        if("INF".equalsIgnoreCase(fareRule.getPaxType())){
            return TravellerType.INFANT;
        }
        return null;
    }

    private AmountWorkflowDTO getAmount(FareRuleDetailDTO fareRule){
        return new AmountWorkflowDTO(fareRule.getFinalAmount(), Optional.ofNullable(fareRule.getCurrency()).orElse(DEFAULT_CURRENCY));
    }

    private String getTimeLineUnit(String timeFrame){
        if(timeFrame.toLowerCase().contains("day")){
            return DAY;
        }
        if(timeFrame.toLowerCase().contains("hour")){
            return HOUR;
        }
        return MINUTE;
    }
}
