package com.cleartrip.supplier.minirule.workflow.utils;

import com.cleartrip.supplier.inventory.protos.v1.PaxTypeOuterClass;
import com.cleartrip.supplier.minirule.application.models.response.FareRuleType;
import com.cleartrip.supplier.minirule.application.models.response.TravellerType;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.AmountWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.FareRuleChargeWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.FareRuleWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.TimelineWorkflowDTO;
import com.cleartrip.supplier.search.models.DTO.PaxFareInfoDTO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AirArabiaRegexUtil {

    private static final String MODIFICATION_REGEX = "(?:Modification\\s*:\\s*(?:One|\\d+)\\s*modification(?:s)?, up to\\s*(\\d+)h)|(?:\\b(\\d+)\\s*Free modifications, up to\\s*(\\d+)h)";
    private static final String CANCELLATION_REGEX = "Cancellation up to\\s*(\\d+)h(?:.*?Min\\s*(\\d+)\\s*([A-Z]{3}))?";
    private static final String DEPT_TIME = "PT0H";
    private static final String CURRENCY = "AED";
    private static final String ALLOWED_MODIFICATIONS = "Allowed Modifications: ";
    public static final String H = "H";
    public static final String PT = "PT";
    public static final String PER_PAX_PER_SECTOR = "%.2f %s per Pax per Sector";
    public static final String UNDERSCORE = "_";
    public static final String PIPE = "\\|";

    private AirArabiaRegexUtil() {
        throw new IllegalStateException("Utility class");
    }

    /*
     * CANCEL
     * */


    public static FareRuleWorkflowDTO createCancelDTO(String description, PaxFareInfoDTO paxFareInfoDTO) {
        List<FareRuleChargeWorkflowDTO> fareRuleChargeWorkflowDTO = createFareRuleCancelWorkflowDTO(paxFareInfoDTO, description);

        return FareRuleWorkflowDTO.builder()
                .fareRuleType(FareRuleType.CANCEL)
                .permitted(ListUtils.isNullOrEmpty(fareRuleChargeWorkflowDTO))
                .fareRuleInfoText(Optional.of(fareRuleChargeWorkflowDTO)
                        .flatMap(list -> list.stream().findFirst())
                        .map(FareRuleChargeWorkflowDTO::getChargeInfoText)
                        .orElse(""))
                .charges(fareRuleChargeWorkflowDTO)
                .build();
    }

    private static List<FareRuleChargeWorkflowDTO> createFareRuleCancelWorkflowDTO(PaxFareInfoDTO pax, String description) {
        List<FareRuleChargeWorkflowDTO> fareRuleCancelChargeWorkflow = new ArrayList<>();
        Matcher matcher = getMatcher(description, CANCELLATION_REGEX);

        if (matcher.find()) {
            String modTime = PT + matcher.group(1) + H;
            double fee = matcher.group(2) != null ? Double.parseDouble(matcher.group(2)) : 0.0;
            String currency = matcher.group(3) != null ? matcher.group(3) : CURRENCY;

                fareRuleCancelChargeWorkflow.add(
                        FareRuleChargeWorkflowDTO.builder()
                                .timeLine(createTimeLine(modTime))
                                .passengerFareRuleCharges(Map.of(convertPaxType(pax.getPaxType()), new AmountWorkflowDTO(fee, currency)))
                                .permitted(true)
                                .chargeInfoText(chargeText(fee, currency))
                                .build()
                );

        }

        return fareRuleCancelChargeWorkflow;
    }

    private static String chargeText(double fee, String currency) {
        return String.format(PER_PAX_PER_SECTOR, fee, currency);
    }


    /*
    * AMENDMENT
    * */

    public static FareRuleWorkflowDTO createAmendDTO(String description, PaxFareInfoDTO paxFareInfoDTO) {

        List<FareRuleChargeWorkflowDTO> fareRuleChargeWorkflowDTO = createFareRuleAmendWorkflowDTO(paxFareInfoDTO, description);

        return FareRuleWorkflowDTO.builder()
                .fareRuleType(FareRuleType.AMENDMENT)
                .permitted(ListUtils.isNullOrEmpty(fareRuleChargeWorkflowDTO))
                .fareRuleInfoText(Optional.of(fareRuleChargeWorkflowDTO)
                .flatMap(list -> list.stream().findFirst())
                .map(FareRuleChargeWorkflowDTO::getChargeInfoText)
                .orElse(""))
                .charges(fareRuleChargeWorkflowDTO)
                .build();
    }

    private static List<FareRuleChargeWorkflowDTO> createFareRuleAmendWorkflowDTO(PaxFareInfoDTO pax, String description) {

        List<FareRuleChargeWorkflowDTO> fareRuleAmendChargeWorkflow = new ArrayList<>();
        Matcher matcher = getMatcher(description, MODIFICATION_REGEX);

        if (matcher.find()) {
            String modCount = matcher.group(2) != null ? matcher.group(2) : "1";
            String modTime = PT + (matcher.group(1) != null ? matcher.group(1) : matcher.group(3)) + H;
            String chargeInfoText = ALLOWED_MODIFICATIONS.concat(modCount);

                fareRuleAmendChargeWorkflow.add(
                        FareRuleChargeWorkflowDTO.builder()
                                .timeLine(createTimeLine(modTime))
                                .passengerFareRuleCharges(Map.of(convertPaxType(pax.getPaxType()), new AmountWorkflowDTO(0.0, CURRENCY)))
                                .permitted(true)
                                .chargeInfoText(chargeInfoText)
                               .build()
                );

        }

        return fareRuleAmendChargeWorkflow;

    }

    /*
    * Utils
    * */

    private static TravellerType convertPaxType(PaxTypeOuterClass.PaxType paxType) {
        return TravellerType.valueOf(paxType.toString());
    }

    private static TimelineWorkflowDTO createTimeLine(String time) {
        return TimelineWorkflowDTO.builder()
                .from(DEPT_TIME)
                .to(time)
                .build();
    }

    private static Matcher getMatcher(String description, String regex) {
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(description);
    }

    public static String getKey(String solutionId) {
        return Arrays.asList(solutionId.split(PIPE)).get(0) + UNDERSCORE + Arrays.asList(solutionId.split(PIPE)).get(0);
    }


}
