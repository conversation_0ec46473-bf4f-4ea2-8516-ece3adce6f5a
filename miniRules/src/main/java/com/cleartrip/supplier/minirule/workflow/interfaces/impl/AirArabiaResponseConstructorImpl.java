package com.cleartrip.supplier.minirule.workflow.interfaces.impl;

import com.cleartrip.supplier.minirule.workflow.dto.Error;
import com.cleartrip.supplier.minirule.workflow.dto.MiniRuleWorkflowContext;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.FlightSolutionWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.MiniRuleWorkflowRequest;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.Exception;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.FareRuleDetailWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.FareRuleWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.response.farerules.MiniRuleWorkflowResponse;
import com.cleartrip.supplier.minirule.workflow.interfaces.ResponseConstructor;
import com.cleartrip.supplier.minirule.workflow.utils.AirArabiaRegexUtil;
import com.cleartrip.supplier.search.models.DTO.BundleFareDetails;
import com.cleartrip.supplier.search.models.DTO.FareFamilyDTO;
import com.cleartrip.supplier.search.models.DTO.FareMetaInfoDTO;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.DTO.JourneyFareSummaryDTO;
import com.cleartrip.supplier.search.models.DTO.PaxFareInfoDTO;
import org.openl.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class AirArabiaResponseConstructorImpl implements ResponseConstructor {
    @Override
    public MiniRuleWorkflowResponse prepareMiniRuleResponse(MiniRuleWorkflowContext context) {
        List<Error> errors = context.getErrors();

        if(Objects.nonNull(errors)) {
            return MiniRuleWorkflowResponse.builder()
                    .flightSolutionMap(null)
                    .fareRuleDetails(null)
                    .exception(createException(errors))
                    .build();
        }

        return MiniRuleWorkflowResponse.builder()
                .flightSolutionMap(createFlightSolutionMap(context.getMiniRuleWorkflowRequest()))
                .fareRuleDetails(createFareRuleDetails(context.getFlightSolutionDTO()))
                .exception(null)
                .build();
    }

    private List<FareRuleDetailWorkflowDTO> createFareRuleDetails(FlightSolutionDTO flightSolutionDTO) {

        List<FareRuleDetailWorkflowDTO> fareRuleDetailWorkflowDTOList = new ArrayList<>();

        flightSolutionDTO.getFareFamilyDTO().get(0).getJourneyFareSummarymap().forEach((key, value) -> {
            for(PaxFareInfoDTO paxFareInfoDTO:flightSolutionDTO.getFareFamilyDTO().get(0).getPaxFareInfoDTO()){
                fareRuleDetailWorkflowDTOList.add(FareRuleDetailWorkflowDTO.builder()
                        .solutionId(flightSolutionDTO.getSolutionId())
                        .comboFbd(flightSolutionDTO.getFareFamilyDTO().get(0).getComboFbc())
                        .ctBrandName(flightSolutionDTO.getFareFamilyDTO().get(0).getCtBrandName())
                        .fareRuleId(createFareRuleId(flightSolutionDTO.getFareFamilyDTO().get(0).getFareMetaInfoDTO()))
                        .fareRules(generateFareRules(flightSolutionDTO.getFareFamilyDTO(), paxFareInfoDTO, flightSolutionDTO.getSolutionId()))
                        .build());
            }
        });
        return fareRuleDetailWorkflowDTOList;

    }

    private String createFareRuleId(FareMetaInfoDTO fareMetaInfoDTO) {
        return StringUtils.join(new String[]{fareMetaInfoDTO.getPromiseId(), fareMetaInfoDTO.getFareId()}, "__");
    }

    private Map<String, List<FareRuleWorkflowDTO>> generateFareRules(List<FareFamilyDTO> fareFamilyDTO, PaxFareInfoDTO paxFareInfoDTO, String solutionId) {

        Map<String, List<FareRuleWorkflowDTO>> fareRulesMap = new HashMap<>();

        FareFamilyDTO fareFamily = fareFamilyDTO.get(0);

        fareFamily.getJourneyFareSummarymap().forEach((key, value) -> {
            List<FareRuleWorkflowDTO> fareRuleList = createFareRuleList(value, paxFareInfoDTO);
            fareRulesMap.put(key, fareRuleList);
        });

        enrichFareRulesMap(fareRulesMap, solutionId);

        return fareRulesMap;
    }

    private void enrichFareRulesMap(Map<String, List<FareRuleWorkflowDTO>> fareRulesMap, String solutionId) {
        if(fareRulesMap.size() > 1){
            fareRulesMap.values().stream().findFirst().ifPresent(firstValue -> {
                ArrayList<FareRuleWorkflowDTO> fareRuleWorkflowDTOS = new ArrayList<>(firstValue);
                fareRulesMap.put(AirArabiaRegexUtil.getKey(solutionId), fareRuleWorkflowDTOS);
            });
        }
    }

    private List<FareRuleWorkflowDTO> createFareRuleList(JourneyFareSummaryDTO journeyFareSummaryDTO, PaxFareInfoDTO paxFareInfoDTO) {
        List<FareRuleWorkflowDTO> chargesList = new ArrayList<>();
        String description = journeyFareSummaryDTO.getBundleFareDetails()
                .map(BundleFareDetails::getDescriptions)
                .orElse("");
        chargesList.add(AirArabiaRegexUtil.createAmendDTO(description, paxFareInfoDTO));
        chargesList.add(AirArabiaRegexUtil.createCancelDTO(description, paxFareInfoDTO));

        return chargesList;
    }

    private Map<String, FlightSolutionWorkflowDTO> createFlightSolutionMap(MiniRuleWorkflowRequest miniRuleWorkflowRequest) {
        Map<String, FlightSolutionWorkflowDTO> flightSolutionMap = new HashMap<>();
        flightSolutionMap.put(miniRuleWorkflowRequest.getSolutionId(), miniRuleWorkflowRequest.getFlightSolution());
        return flightSolutionMap;
    }

    private com.cleartrip.supplier.minirule.workflow.dto.response.farerules.Exception createException(List<Error> errors) {
        Error error = errors.get(0);
        return Exception.builder()
                .description(error.getCode() + StringUtils.SPACE + error.getMessage())
                .message(error.getMessage())
                .throwable(new Throwable(error.getMessage()))
                .build();
    }

}
