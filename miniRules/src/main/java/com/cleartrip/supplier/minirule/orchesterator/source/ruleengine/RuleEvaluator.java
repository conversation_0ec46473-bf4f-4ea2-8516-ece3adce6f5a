package com.cleartrip.supplier.minirule.orchesterator.source.ruleengine;

import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.dto.FareRuleDetailDTO;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.exception.MiniRuleOrchestratorException;
import com.cleartrip.supplier.minirule.workflow.dto.MiniRuleWorkflowContext;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.FareDetailsWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.JourneyFareSummaryWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.PriceComponentWorkflowDTO;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class RuleEvaluator {

    public static final String FLAT_DEDUCTION = "Flat Deduction";
    public static final String PERCENTAGE_OF_BF = "% of BF";
    public static final String PERCENTAGE_OF_BF_AND_YQ = "% of BF and YQ";
    public static final String PERCENTAGE_OF_BF_AND_YQ_AND_CUTE_AND_RCF = "% of BF and YQ and CUTE and RCF";
    public static final String BASE_FARE = "BASE_FARE";
    public static final String YQ = "YQ";
    public static final String CUTE = "CUTE";
    public static final String RCF = "RCF";
    public static final String AMOUNT_ONLY_REFUNDED = "Amount Only Refunded";

    public Double evaluate(FareRuleDetailDTO fareRule, MiniRuleWorkflowContext context){
        FareDetailsWorkflowDTO fareDetailsOfFbc = context.getMiniRuleWorkflowRequest().getFlightSolution().getFareDetailsList().stream()
                .filter(fareDetailsWorkflowDTO -> fareDetailsWorkflowDTO.getComboFbc().equalsIgnoreCase(fareRule.getComboFbc()))
                .findFirst().orElseThrow(() -> new MiniRuleOrchestratorException("No FareDetails for fbc " + fareRule.getComboFbc() , null));

        if(!fareDetailsOfFbc.getJourneyFareSummaryMap().containsKey(fareRule.getJourneyKey())) throw new MiniRuleOrchestratorException("Journekey: " + fareRule.getJourneyKey() + " not present in FareDetails of fbc " + fareRule.getComboFbc() , null);
        JourneyFareSummaryWorkflowDTO journeyFareSummary = fareDetailsOfFbc.getJourneyFareSummaryMap().get(fareRule.getJourneyKey());
        if(!journeyFareSummary.getPaxFareSummaries().containsKey(fareRule.getPaxType())) throw new MiniRuleOrchestratorException("PaxType: " + fareRule.getPaxType() + " not present in FareDetails of fbc " + fareRule.getComboFbc() , null);

        return evaluate(journeyFareSummary.getPaxFareSummaries().get(fareRule.getPaxType()).getPriceComponents(), fareRule);
    }

    private double evaluate(List<PriceComponentWorkflowDTO> priceComponentList, FareRuleDetailDTO fareRule){
        if(FLAT_DEDUCTION.equalsIgnoreCase(fareRule.getApplicableRule())){
            return Double.parseDouble(fareRule.getApplicableRuleAmount());
        }
        if(AMOUNT_ONLY_REFUNDED.equalsIgnoreCase(fareRule.getApplicableRule())){
            return priceComponentList.stream().map(PriceComponentWorkflowDTO::getAmount).reduce((double) 0, Double::sum) - Double.parseDouble(fareRule.getApplicableRuleAmount());
        }
        if(PERCENTAGE_OF_BF.equalsIgnoreCase(fareRule.getApplicableRule())){
            double baseFare = getAmount(priceComponentList, BASE_FARE);
            return getPercentage(Double.parseDouble(fareRule.getApplicableRuleAmount()), baseFare);
        }
        if(PERCENTAGE_OF_BF_AND_YQ.equalsIgnoreCase(fareRule.getApplicableRule())){
            double baseFare = getAmount(priceComponentList, BASE_FARE);
            double yq = getAmount(priceComponentList, YQ);
            return getPercentage(Double.parseDouble(fareRule.getApplicableRuleAmount()), baseFare) + yq;
        }
        if(PERCENTAGE_OF_BF_AND_YQ_AND_CUTE_AND_RCF.equalsIgnoreCase(fareRule.getApplicableRule())){
            double baseFare = getAmount(priceComponentList, BASE_FARE);
            double yq = getAmount(priceComponentList, YQ);
            double cute = getAmount(priceComponentList, CUTE);
            double rcf = getAmount(priceComponentList, RCF);
            return getPercentage(Double.parseDouble(fareRule.getApplicableRuleAmount()), baseFare) + yq + cute + rcf;
        }
        throw new MiniRuleOrchestratorException("No ApplicableRuleType Strategy present for this ApplicableRuleType " + fareRule.getApplicableRule() , null);
    }

    private double getAmount(List<PriceComponentWorkflowDTO> priceComponents, String code){
        Optional<PriceComponentWorkflowDTO> component = priceComponents.stream()
                .filter(priceComponent -> code.equalsIgnoreCase(priceComponent.getCode()))
                .findFirst();

        return component.map(PriceComponentWorkflowDTO::getAmount).orElse(0.0);
    }

    private double getPercentage(double percent, double value){
        return (value * percent ) / 100;
    }
}
