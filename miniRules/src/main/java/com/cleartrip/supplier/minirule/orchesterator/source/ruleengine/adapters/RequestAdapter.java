package com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.adapters;

import com.cleartrip.config.management.api.resources.airport.AirportInfoBean;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.dto.RuleEngineRequestDTO;
import com.cleartrip.supplier.minirule.workflow.dto.MiniRuleWorkflowContext;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.*;

import java.util.ArrayList;
import java.util.List;

public class RequestAdapter {

    public static final String INTERNATIONAL = "International";
    public static final String DOMESTIC = "Domestic";
    private final AirportInfoBean airportInfo;

    public RequestAdapter(AirportInfoBean resource) {
        this.airportInfo = resource;
    }

    public List<RuleEngineRequestDTO> adapt(MiniRuleWorkflowContext context){
        List<RuleEngineRequestDTO> ruleEngineRequestDTOS = new ArrayList<>();
        MiniRuleWorkflowRequest workflowRequest = context.getMiniRuleWorkflowRequest();
        FlightSolutionWorkflowDTO flightSolution = workflowRequest.getFlightSolution();
        List<FareDetailsWorkflowDTO> fareDetailsList = workflowRequest.getFlightSolution().getFareDetailsList();
        for(FareDetailsWorkflowDTO fareDetails: fareDetailsList){
            for (PaxInfoWorkflowDTO paxInfo : workflowRequest.getPaxDetails()) {
                for (String jk : flightSolution.getFlightDetailsMap().keySet()) {
                    for (String sk : flightSolution.getFlightDetailsMap().get(jk).getSegmentDetails().keySet()) {
                        SegmentFareSummaryWorkflowDTO segmentFareSummaryWorkflowDTO = fareDetails.getJourneyFareSummaryMap().get(jk).getSegmentFareMap().get(sk);
                        SegmentDetailsWorkflowDTO segmentDetailsWorkflowDTO = flightSolution.getFlightDetailsMap().get(jk).getSegmentDetails().get(sk);
                            ruleEngineRequestDTOS.add(RuleEngineRequestDTO.builder()
                                    .solutionId(workflowRequest.getSolutionId())
                                    .comoFbc(fareDetails.getComboFbc())
                                    .journeyKey(jk)
                                    .airline(segmentDetailsWorkflowDTO.getAirlineCode())
                                    .supplier(workflowRequest.getSupplier())
                                    .fromCountry(getCountryCode(segmentDetailsWorkflowDTO.getDepartureAirportCode()))
                                    .toCountry(getCountryCode(segmentDetailsWorkflowDTO.getArrivalAirportCode()))
                                    .fromAirport(segmentDetailsWorkflowDTO.getDepartureAirportCode())
                                    .toAirport(segmentDetailsWorkflowDTO.getArrivalAirportCode())
                                    .fareCategory(fareDetails.getFareCategory())
                                    .fareSubCategory(fareDetails.getFareSubCategory())
                                    .fareGroupName(fareDetails.getFareGroupName())
                                    .productClass(segmentFareSummaryWorkflowDTO.getProductClass())
                                    .bookingType(getBookingType(workflowRequest.isInternational()))
                                    .country(workflowRequest.getCountry())
                                    .paxType(paxInfo.getPaxType())
                                    .build()
                                );
                    }
                }
            }
        }
        return ruleEngineRequestDTOS;
    }

    private String getCountryCode(String airportCode) {
        return airportInfo.getAirportInfoMap().get(airportCode).getCountryCode();
    }

    private void enrichRuleEngineRequest(List<RuleEngineRequestDTO> ruleEngineRequestDTOS) {
        for (RuleEngineRequestDTO ruleEngineRequestDTO : ruleEngineRequestDTOS) {
        }
    }

    private String getBookingType(boolean isInternational){
        if(isInternational) return INTERNATIONAL;
        return DOMESTIC;
    }
}
