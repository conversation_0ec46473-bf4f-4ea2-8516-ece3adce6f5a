package com.cleartrip.supplier.minirule.application.impl;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.minirule.application.IMiniRuleApplicationService;
import com.cleartrip.supplier.minirule.application.adapter.farerules.MiniRuleApplicationRequestAdapterV2;
import com.cleartrip.supplier.minirule.application.adapter.farerules.MiniRuleApplicnRequestAdapter;
import com.cleartrip.supplier.minirule.application.adapter.MiniRuleRequestAdapter;
import com.cleartrip.supplier.minirule.application.models.request.MiniRuleRequest;
import com.cleartrip.supplier.minirule.application.models.request.SolutionDetail;
import com.cleartrip.supplier.minirule.application.strategy.MiniRuleFetchingStrategy;
import com.cleartrip.supplier.minirule.application.adapter.MiniRuleResponseAdapter;
import com.cleartrip.supplier.minirule.application.adapter.farerules.MiniRuleDomainResponseAdapter;
import com.cleartrip.supplier.minirule.application.models.requestV2.MiniRuleApplicationRequest;
import com.cleartrip.supplier.minirule.application.models.responseV2.MiniRuleApplicationResponse;
import com.cleartrip.supplier.minirule.config.MiniRuleConfiguration;
import com.cleartrip.supplier.minirule.domain.models.request.farerules.MiniRuleDomainRequest;
import com.cleartrip.supplier.minirule.domain.models.response.MiniRuleDomainResponse;
import com.cleartrip.supplier.minirule.domain.service.IMiniRuleService;
import com.cleartrip.supplier.minirule.domain.service.MiniRuleDomainService;
import com.cleartrip.supplier.minirule.monitoring.NewRelicUtil;
import com.cleartrip.supplier.minirule.monitoring.StatsUtil;
import com.cleartrip.supplier.search.AvailabilitySolutionService;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.models.application.FetchFlightSolutionReq;
import com.cleartrip.supplier.search.models.application.FetchFlightSolutionRes;
import com.cleartrip.supplier.search.models.application.SelectedSolutionDetails;
import com.cleartrip.supply.core.api.proto.FareRuleDetailOuterClass;
import com.cleartrip.supply.core.api.proto.MiniRuleRequestListOuterClass;
import com.cleartrip.supply.core.api.proto.MiniRuleResponseListOuterClass;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class MiniRuleApplicationServiceImpl implements IMiniRuleApplicationService {

    private static final String ASTERISK = "\\*";
    private final IMiniRuleService iMiniRuleDomainService;
    private final MiniRuleRequestAdapter miniRuleRequestAdapter;
    private final MiniRuleResponseAdapter miniRuleResponseAdapter;
    private final MiniRuleConfiguration miniRuleConfiguration;
    private final StatsUtil statsUtil;
    private final NewRelicHelper newRelicHelper;
    private final StatsHelper statsHelper;
    private final ExecutorService executorService;
    private final AvailabilitySolutionService availabilitySolutionService;
    private final MiniRuleFetchingStrategy fareFamilyStrategy;

    private final MiniRuleApplicnRequestAdapter miniRuleApplicnRequestAdapter;

    private final MiniRuleApplicationRequestAdapterV2 miniRuleApplicnRequestAdapterV2;

    private final MiniRuleDomainResponseAdapter miniRuleDomainResponseAdapter;

    private final MiniRuleDomainService miniRuleDomainServiceV2;

    private static final String MINI_RULE_URL = "v1/miniRule";
    private static final String SC_MINI_RULE = "SC_MINI_RULE";
    private static final String MINI_RULE = "MINI_RULE";
    private static final String FARE_RULE_V3_URL = "v1/fare-ruleV3";
    private static final String SC_FARE_RULE_V3 = "SC_FARE_RULE_V3";
    private static final String FARE_RULE_V3 = "FARE_RULE_V3";

    private static final String GET_FLIGHT_SOLUTION_V1 = "getFlightSolutionV1";

    private static final String SINGLE_SOL_SEARCH = "SINGLE_SOLUTION_SEARCH";


    @Inject
    public MiniRuleApplicationServiceImpl(final MiniRuleDomainService miniRuleDomainServiceV2,
                                          final IMiniRuleService iMiniRuleDomainService,
                                          final MiniRuleConfiguration miniRuleConfiguration,
                                          final StatsUtil statsUtil,
                                          @Named("miniRuleNewRelicHelper") final NewRelicHelper newRelicHelper,
                                          @Named("miniRuleStatsHelper") final StatsHelper statsHelper,
                                          @Named("miniRuleOrchestratorExecutorService") ExecutorService executorService,
                                          @Named("AvailabilitySolutionServiceImpl") AvailabilitySolutionService availabilitySolutionService,
                                          @Named("FareFamilyMiniRuleFetchingStrategy")MiniRuleFetchingStrategy fareFamilyStrategy) {

        this.miniRuleApplicnRequestAdapter = new MiniRuleApplicnRequestAdapter();
        this.miniRuleApplicnRequestAdapterV2 = new MiniRuleApplicationRequestAdapterV2();
        this.miniRuleDomainResponseAdapter = new MiniRuleDomainResponseAdapter();
        this.miniRuleDomainServiceV2 = miniRuleDomainServiceV2;
        this.iMiniRuleDomainService = iMiniRuleDomainService;
        miniRuleResponseAdapter = new MiniRuleResponseAdapter();
        miniRuleRequestAdapter = new MiniRuleRequestAdapter();
        this.miniRuleConfiguration = miniRuleConfiguration;
        this.statsUtil = statsUtil;
        this.newRelicHelper = newRelicHelper;
        this.statsHelper = statsHelper;
        this.executorService = executorService;
        this.availabilitySolutionService = availabilitySolutionService;
        this.fareFamilyStrategy = fareFamilyStrategy;
    }

    @Override
    public MiniRuleApplicationResponse fetchMiniRulesV3(MiniRuleApplicationRequest request, Context context) {
        final long startTime = System.currentTimeMillis();
        MiniRuleDomainRequest domainRequest = miniRuleApplicnRequestAdapterV2.convert(request, context);
        MiniRuleDomainResponse domainResponse = miniRuleDomainServiceV2.getDomainResponse(domainRequest);
        MiniRuleApplicationResponse response = miniRuleDomainResponseAdapter.adapt(domainResponse);
        newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleApplicationLayerParams(request, response, startTime, context));
        statsHelper.pushToStats(startTime, System.currentTimeMillis(), statsUtil.getObjectInByte(response), 200,
                MINI_RULE_URL, context, statsUtil.getObjectInByte(request), SC_MINI_RULE, MINI_RULE, ApiStatsDto.HttpMethod.POST);

        return response;
    }

    @Override
    public MiniRuleApplicationResponse fetchMiniRulesV4(MiniRuleRequest miniRuleRequest, Context context) {
        final long startTime = System.currentTimeMillis();
        List<FareRuleDetailOuterClass.FareRuleDetail> fareRuleDetails = fetchMiniRuleV2(new MiniRuleRequest(miniRuleRequest.getSolutionDetails(), miniRuleRequest.getPaxDetails(), context));
        MiniRuleApplicationResponse miniRuleApplicationResponse = miniRuleResponseAdapter.protoToMiniRuleResponse(fareRuleDetails, miniRuleRequest.getPaxDetails(), miniRuleRequest.getSolutionDetails());
        statsHelper.pushToStats(startTime,System.currentTimeMillis(),statsUtil.getObjectInByte(miniRuleApplicationResponse),miniRuleApplicationResponse == null ? 500 :200 ,FARE_RULE_V3_URL,context,statsUtil.getObjectInByte(miniRuleRequest),SC_FARE_RULE_V3,FARE_RULE_V3,ApiStatsDto.HttpMethod.POST);
        return miniRuleApplicationResponse;
    }
    private List<FareRuleDetailOuterClass.FareRuleDetail> getUniqueFareRules(List<FareRuleDetailOuterClass.FareRuleDetail> fareRuleDetails) {
        return new ArrayList<>(
                fareRuleDetails.stream()
                        .collect(Collectors.toMap(
                                FareRuleDetailOuterClass.FareRuleDetail::getComboFbc,
                                Function.identity(),
                                (existing, replacement) -> existing,
                                LinkedHashMap::new
                        )).values()
        );
    }

    private String extractPaxType(FareRuleDetailOuterClass.FareRuleDetail fareRuleDetail) {
        return fareRuleDetail.getJourneyWiseFareRulesMap().values().stream()
                .flatMap(fareRuleDetails -> fareRuleDetails.getFareRulesList().stream())
                .flatMap(fareRule -> fareRule.getChargesList().stream())
                .flatMap(fareRuleCharge -> fareRuleCharge.getPassengerFareRuleChargeMap().keySet().stream())
                .findFirst().orElse("");
    }
    @Override
    public MiniRuleResponseListOuterClass.MiniRuleResponseList fetchMiniRulesV2(MiniRuleRequestListOuterClass.MiniRuleRequestList request, Context context) {
        final long startTime = System.currentTimeMillis();
        final MiniRuleRequest miniRuleRequest = miniRuleRequestAdapter.toMiniRuleRequest(request.getSolutionDetailList(), request.getPaxDetailList(), context);
        List<FareRuleDetailOuterClass.FareRuleDetail> fareRuleDetails = fetchMiniRuleV2(miniRuleRequest);
        MiniRuleResponseListOuterClass.MiniRuleResponseList miniRuleResponseList = MiniRuleResponseListOuterClass.MiniRuleResponseList
                .newBuilder()
                .addAllMiniRules(fareRuleDetails)
                .build();

        try{
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleParams(miniRuleRequest, fareRuleDetails, startTime, context));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), statsUtil.getObjectInByte(miniRuleResponseList.toString()), 200,
                    MINI_RULE_URL, context, statsUtil.getObjectInByte(miniRuleRequest), SC_MINI_RULE, MINI_RULE, ApiStatsDto.HttpMethod.POST);
        }
        catch(Exception e){
            log.error("Error while logging data for itineraryId " + context.getItineraryId() +  " in MiniRuleApplicationServiceImpl is ", e);
        }

        return miniRuleResponseList;
    }

    private List<FareRuleDetailOuterClass.FareRuleDetail> fetchMiniRuleV2(MiniRuleRequest miniRuleRequest) {
//        requestEnricher(miniRuleRequest);
        FetchFlightSolutionRes fetchFlightSolutionRes = fetchFlightSolutionRes(miniRuleRequest);
        MiniRuleDomainRequest domainRequest = miniRuleApplicnRequestAdapter.convert(miniRuleRequest, fetchFlightSolutionRes);
        log.error("Entering MiniRule Workflow with request {}", domainRequest);
        MiniRuleDomainResponse domainResponse = miniRuleDomainServiceV2.getDomainResponse(domainRequest);
        return miniRuleResponseAdapter.toMiniRuleResponse(domainResponse, miniRuleRequest.getPaxDetails());
    }

    private void requestEnricher(MiniRuleRequest miniRuleRequest) {
        miniRuleRequest.getSolutionDetails().values().forEach(solutionDetail -> {
            String solutionId = solutionDetail.getSolutionId();
            solutionDetail.setSolutionId(splitSolutionId(solutionId));
        });
    }

    private String splitSolutionId(String solutionId) {
        return solutionId.split(ASTERISK)[0];
    }

    private FetchFlightSolutionRes fetchFlightSolutionRes(MiniRuleRequest request){
        List<SelectedSolutionDetails> selectedSolutionDetails = request.getSolutionDetails().values().stream()
                .map(solutionDetail -> SelectedSolutionDetails.builder()
                        .solutionId(solutionDetail.getSolutionId())
                        .comboFBC(solutionDetail.getComboFbc().get(0))
                        .build())
                .collect(Collectors.toList());
        FetchFlightSolutionReq fetchFlightSolutionReq = FetchFlightSolutionReq.builder()
                .selectedSolution(selectedSolutionDetails)
                .isMultiFareReq(false)
                .build();
        long startTime = System.currentTimeMillis();
        FetchFlightSolutionRes fetchFlightSolutionRes = availabilitySolutionService.fetchFlightSolutionV3(fetchFlightSolutionReq);
        try{
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), statsUtil.getObjectInByte(fetchFlightSolutionRes), 200,
                    GET_FLIGHT_SOLUTION_V1, request.getContext(), statsUtil.getObjectInByte(fetchFlightSolutionReq), SC_MINI_RULE, SINGLE_SOL_SEARCH, ApiStatsDto.HttpMethod.POST);
        }
        catch(Exception e){
            log.error("Error in logging stats for req {} is {}", request, e);
        }
        return fetchFlightSolutionRes;
    }

    private void updateSupplierForSelectedSolutions(MiniRuleRequest miniRuleRequest, FetchFlightSolutionRes fetchFlightSolutionRes) {
        miniRuleRequest.getSolutionDetails().values().forEach(solutionDetail -> {
            String comboFbcToMatch = miniRuleRequest.getSolutionDetails().values().stream()
                    .findFirst()
                    .map(SolutionDetail::getComboFbc)
                    .orElse(Collections.emptyList())
                    .get(0);

            fetchFlightSolutionRes.getSolutionDTOList().values().stream()
                    .filter(flightSol -> solutionDetail.getSolutionId().equals(flightSol.getSolutionId()))
                    .findFirst()
                    .ifPresent(flightSolutionDTO -> {
                        String supplier = flightSolutionDTO.getFareFamilyDTO().stream()
                                .filter(fare -> Objects.equals(fare.getComboFbc(), comboFbcToMatch))
                                .findFirst()
                                .map(fare -> fare.getFareMetaInfoDTO().getSupplierInfoDTO().getSupplier())
                                .orElse(null);

                        if (supplier != null) {
                            solutionDetail.setSupplier(Supplier.valueOf(supplier));
                        }
                    });
        });
    }
}
