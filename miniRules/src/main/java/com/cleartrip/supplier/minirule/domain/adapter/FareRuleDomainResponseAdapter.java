package com.cleartrip.supplier.minirule.domain.adapter;

import com.cleartrip.supplier.minirule.repository.model.response.Amount;
import com.cleartrip.supplier.minirule.repository.model.response.FareRule;
import com.cleartrip.supplier.minirule.repository.model.response.FareRuleCharge;
import com.cleartrip.supplier.minirule.application.models.response.FareRuleType;
import com.cleartrip.supplier.minirule.timelinegenerator.Charge;
import com.cleartrip.supplier.minirule.timelinegenerator.TimedData;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class FareRuleDomainResponseAdapter {

    private final FareRuleChargesAdapter fareRuleChargesAdapter;

    public FareRuleDomainResponseAdapter() {
        this.fareRuleChargesAdapter = new FareRuleChargesAdapter();
    }

    public FareRule toFareRule(List<TimedData<Charge>> mergedFareRules, FareRuleType fareRuleType) {
        List<FareRuleCharge> fareRuleCharges = new ArrayList<>();
        mergedFareRules.forEach(timedData -> {
            fareRuleCharges.add(fareRuleChargesAdapter.getFareRuleCharges(timedData, fareRuleType));
        });
        List<Amount> amountList = fareRuleCharges.stream()
                .flatMap(fareRuleCharge -> fareRuleCharge.getPassengerFareRuleCharges().values().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        FareRule fareRule = new FareRule(fareRuleType, CollectionUtils.isNotEmpty(amountList), "", fareRuleCharges);
        return fareRule;
    }

    public FareRule toFareRuleV2(List<TimedData<Charge>> mergedFareRules, FareRuleType fareRuleType) {
        List<FareRuleCharge> fareRuleCharges = new ArrayList<>();
        mergedFareRules.forEach(timedData -> {
            fareRuleCharges.add(fareRuleChargesAdapter.getFareRuleChargesV2(timedData, fareRuleType));
        });
        List<Amount> amountList = fareRuleCharges.stream()
                .flatMap(fareRuleCharge -> fareRuleCharge.getPassengerFareRuleCharges().values().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        FareRule fareRule = new FareRule(fareRuleType, CollectionUtils.isNotEmpty(amountList), "", fareRuleCharges);
        return fareRule;
    }
}
