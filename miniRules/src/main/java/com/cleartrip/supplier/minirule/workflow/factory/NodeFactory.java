package com.cleartrip.supplier.minirule.workflow.factory;

import com.cleartrip.config.management.api.resources.airport.AirportInfo;
import com.cleartrip.config.management.core.resources.airport.AirportInfoResource;
import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.node.AdaptRuleEngineResponseNode;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.node.FetchFromRuleEngineNode;
import com.cleartrip.supplier.minirule.utils.JsonUtil;
import com.cleartrip.supplier.minirule.workflow.adapters.FetchCacheResponseAdapter;
import com.cleartrip.supplier.minirule.workflow.enums.NodeType;
import com.cleartrip.supplier.minirule.workflow.keyGenerator.KeyGenerator;
import com.cleartrip.supplier.minirule.workflow.nodes.conditional.MiniRuleFarePresentConditionNode;
import com.cleartrip.supplier.minirule.workflow.nodes.conditional.SingleSolutionPresenceConditionNode;
import com.cleartrip.supplier.minirule.workflow.nodes.conditional.SolutionPresentConditionNode;
import com.cleartrip.supplier.minirule.workflow.nodes.execution.*;
import com.cleartrip.utility.workflow.design.SequentialNode;
import com.cleartrip.utility.workflow.design.Task;
import com.cleartrip.utility.workflow.design.WorkflowContext;
import com.cleartrip.utility.workflow.impl.EmptySequentialNode;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

public class NodeFactory {
    private final Map<NodeType, Supplier<SequentialNode<?, ?, ? extends WorkflowContext, ? extends Task<?, ?>>>> nodeMap;

    @Inject
    public NodeFactory(final KeyGenerator keyGenerator,
                       final @Named("fetchMiniRuleFromRuleEngineExecutor") ExecutorService executorService,
                       final @Named("miniRuleNewRelicHelper") NewRelicHelper newRelicHelper,
                       @Named("miniRuleJsonUtil") JsonUtil util,
                       FetchCacheResponseAdapter fetchCacheResponseAdapter,
                       AirportInfoResource airportInfoResource) {
        this.nodeMap = new HashMap<>();
        register(NodeType.FETCH_MINI_RULE_FROM_SUPPLIER_NODE, FetchMiniRuleFromSupplierNode::new);
        register(NodeType.ADAPT_GAL_DOM_RESPONSE_NODE, AdaptGALDomesticResponseNode::new);
        register(NodeType.RESPONSE_CONSTRUCTION_NODE, PrepareResponseConstructionNode::new);
        register(NodeType.FETCH_MINI_RULE_FROM_CACHE_NODE, () -> new FetchMiniRuleFromCacheNode(keyGenerator));
        register(NodeType.MINI_RULE_FARE_PRESENT_CONDITION_NODE, MiniRuleFarePresentConditionNode::new);
        register(NodeType.SOLUTION_PRESENT_CONDITION_NODE, SolutionPresentConditionNode::new);
        register(NodeType.PERSIST_MINI_RULE_RESPONSE_NODE, PersistMiniRuleFromSupplierNode::new);
        register(NodeType.FARE_RULE_AGGREGATOR_NODE, FareRuleAggregatorNode::new);
        register(NodeType.FETCH_FROM_RULE_ENGINE_NODE, () -> new FetchFromRuleEngineNode(executorService, newRelicHelper, util, airportInfoResource));
        register(NodeType.ADAPT_RESPONSE_FROM_RULE_ENGINE_NODE, AdaptRuleEngineResponseNode::new);
        register(NodeType.VERTIEL_FARE_RULE_NODE, VerteilFareRuleNode::new);
        register(NodeType.HANDLE_CURRENCY_IN_SUPPLIER_RESPONSE_NODE, HandleCurrencyInSupplierResponseNode::new);
        register(NodeType.FETCH_CACHE_RESPONSE_NODE, () -> new FetchCacheResponseNode(fetchCacheResponseAdapter));
        register(NodeType.SINGLE_SOLUTION_CONDITION_NODE, SingleSolutionPresenceConditionNode::new);
        register(NodeType.CONSTRUCT_FARE_RULES_NODE, ConstructFareRulesResponseNode::new);
        register(NodeType.EMPTY_RESPONSE_NODE, EmptySequentialNode::new);
    }

    public void register(NodeType nodeType, Supplier<SequentialNode<?, ?, ? extends WorkflowContext, ? extends Task<?, ?>>> node) {
        this.nodeMap.put(nodeType, node);
    }

    public SequentialNode<?, ?, ? extends WorkflowContext, ? extends Task<?, ?>> getInstance(NodeType nodeType) {
        return this.nodeMap.get(nodeType).get();
    }
}
