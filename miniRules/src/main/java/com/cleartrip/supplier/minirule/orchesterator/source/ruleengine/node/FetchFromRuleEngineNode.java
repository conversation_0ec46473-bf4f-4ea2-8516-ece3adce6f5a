package com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.node;

import com.cleartrip.config.management.core.resources.airport.AirportInfoResource;
import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.supplier.minirule.monitoring.NewRelicUtil;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.adapters.RequestAdapter;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.dto.FareRuleDetailResponse;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.dto.RuleEngineRequestDTO;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.exception.MiniRuleOrchestratorException;
import com.cleartrip.supplier.minirule.orchesterator.source.ruleengine.task.FetchFromRuleEngineTask;
import com.cleartrip.supplier.minirule.utils.JsonUtil;
import com.cleartrip.supplier.minirule.workflow.dto.MiniRuleWorkflowContext;
import com.cleartrip.supplier.minirule.workflow.dto.request.farerules.FareDetailsWorkflowDTO;
import com.cleartrip.supplier.minirule.workflow.strategy.MiniRuleMergingStrategy;

import com.cleartrip.utility.workflow.impl.SequentialBatchNodeImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Slf4j
public class FetchFromRuleEngineNode extends SequentialBatchNodeImpl<RuleEngineRequestDTO, FareRuleDetailResponse,
        MiniRuleWorkflowContext, FetchFromRuleEngineTask> {

    public static final String FETCH_FARE_RULE_FROM_RULE_ENGINE = "FetchFareRuleFromRuleEngine";
    public static final String WHICHEVER_IS_LOWER = "Whichever is lower";

    private final ExecutorService executorService;

    private final RequestAdapter requestAdapter;

    private final NewRelicHelper newRelicHelper;

    private final JsonUtil jsonUtil;

    private final AirportInfoResource airportInfoResource;


    public FetchFromRuleEngineNode(ExecutorService executorService, NewRelicHelper newRelicHelper, JsonUtil jsonUtil, AirportInfoResource airportInfoResource) {
        this.executorService = executorService;
        this.newRelicHelper = newRelicHelper;
        this.airportInfoResource = airportInfoResource;
        this.requestAdapter = new RequestAdapter(airportInfoResource.getResource());
        this.jsonUtil = jsonUtil;
    }

    @Override
    public List<RuleEngineRequestDTO> prepareTaskInput(MiniRuleWorkflowContext miniRuleWorkflowContext) {
        return requestAdapter.adapt(miniRuleWorkflowContext);
    }

    @Override
    public void process(MiniRuleWorkflowContext miniRuleWorkflowContext) {
        FetchFromRuleEngineTask task = this.getTasks().stream().findFirst().orElseThrow( () -> new MiniRuleOrchestratorException("MiniRule, Rule Engine Task Not Found", null));

        List<FareRuleDetailResponse> allResponses = new ArrayList<>();
        List<RuleEngineRequestDTO> ruleEngineRequests = prepareTaskInput(miniRuleWorkflowContext);
        for(FareDetailsWorkflowDTO fareDetails: miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getFlightSolution().getFareDetailsList()){
            try{
                long  startTime = System.currentTimeMillis();
                List<Callable<FareRuleDetailResponse>> fareRulesTaskList = new ArrayList<>();
                List<FareRuleDetailResponse> fareRulesResponseList = new ArrayList<>();
                ruleEngineRequests.stream().filter(requestDto -> requestDto.getComoFbc().equalsIgnoreCase(fareDetails.getComboFbc()))
                        .forEach(requestDto -> fareRulesTaskList.add(() -> task.run(requestDto)));
                List<Future<FareRuleDetailResponse>> futureList = executorService.invokeAll(fareRulesTaskList);
                for (int idx = 0; idx < futureList.size(); idx++) {
                    RuleEngineRequestDTO ruleEngineRequestDTO = ruleEngineRequests.get(idx);
                    try{
                        FareRuleDetailResponse ruleEngineResponse = futureList.get(idx).get();
                        fareRulesResponseList.add(ruleEngineResponse);
                        newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleRuleEngineParams(ruleEngineRequestDTO,
                                ruleEngineResponse,
                                startTime,
                                miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getContext(),
                                Optional.empty()));
                    }
                    catch(Exception e){
                        log.error("Error in fetching fare rules from rule Engine for solnId:comboFbc {}, request {} is {}", ruleEngineRequestDTO.getSolutionId() + ":" + ruleEngineRequestDTO.getComoFbc(),
                                jsonUtil.writeValueAsString(ruleEngineRequestDTO), e.getCause());
                        newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleRuleEngineParams(ruleEngineRequestDTO,
                                null,
                                startTime,
                                miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getContext(),
                                Objects.isNull(e.getCause()) ? Optional.of(e) : Optional.of(e.getCause())));

                        fareRulesResponseList.clear();
                        break;
                    }
                }
                allResponses.addAll(fareRulesResponseList);
            }
            catch(Exception e){
                log.error("Error in fetching fare rules from rule Engine for solnId {} is {}", miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getSolutionId(), e);
                newRelicHelper.pushToNewRelic(NewRelicUtil.prepareMiniRuleRuleEngineParams(null,
                        null,
                        0,
                        miniRuleWorkflowContext.getMiniRuleWorkflowRequest().getContext(),
                        Optional.of(e))
                );
            }
        }
        handleTaskOutput(allResponses, miniRuleWorkflowContext);
    }

    @Override
    public void handleTaskOutput(List<FareRuleDetailResponse> list, MiniRuleWorkflowContext miniRuleWorkflowContext) {
        miniRuleWorkflowContext.setRuleEngineResponse(list);
    }

    private MiniRuleMergingStrategy getStrategy(){
        return MiniRuleMergingStrategy.HIGHER_VALUE;
    }

    @Override
    public String getNodeName() {
        return FETCH_FARE_RULE_FROM_RULE_ENGINE;
    }
}
