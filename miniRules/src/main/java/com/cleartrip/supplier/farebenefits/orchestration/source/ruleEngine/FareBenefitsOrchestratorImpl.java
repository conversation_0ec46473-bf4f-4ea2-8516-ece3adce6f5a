package com.cleartrip.supplier.farebenefits.orchestration.source.ruleEngine;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.farebenefits.orchestration.IFareBenefitsOrchestrator;
import com.cleartrip.supplier.farebenefits.orchestration.dto.FareBenefitsOrchestratorRequest;
import com.cleartrip.supplier.farebenefits.orchestration.dto.FareBenefitsOrchestratorResponse;
import com.cleartrip.supplier.farebenefits.orchestration.dto.SolutionDetailsOrchestratorDto;
import com.cleartrip.supplier.farebenefits.orchestration.dto.SolutionWiseFareBenefitsOrchestratorDto;
import com.cleartrip.supplier.farebenefits.orchestration.source.ruleEngine.workflow.FareBenefitsWorkflowGenerator;
import com.cleartrip.supplier.minirule.monitoring.NewRelicParams;
import com.cleartrip.supplier.minirule.monitoring.StatsUtil;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Named;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Slf4j
public class FareBenefitsOrchestratorImpl implements IFareBenefitsOrchestrator {

    private final FareBenefitsWorkflowGenerator fareBenefitsWorkflowGenerator;
    private final ExecutorService executorService;
    private final StatsHelper statsHelper;
    private final StatsUtil statsUtil;
    private final NewRelicHelper newRelicHelper;

    @Inject
    public FareBenefitsOrchestratorImpl(FareBenefitsWorkflowGenerator fareBenefitsWorkflowGenerator, @Named("fareBenefitsOrchestratorExecutorService") ExecutorService executorService,
                                        @Named("miniRuleStatsHelper") StatsHelper statsHelper,
                                        StatsUtil statsUtil,
                                        @Named("fareBenefitNewRelicHelper") NewRelicHelper newRelicHelper) {
        this.fareBenefitsWorkflowGenerator = fareBenefitsWorkflowGenerator;
        this.executorService = executorService;
        this.statsHelper = statsHelper;
        this.statsUtil = statsUtil;
        this.newRelicHelper = newRelicHelper;
    }

    @Override
    public FareBenefitsOrchestratorResponse orchestrate(FareBenefitsOrchestratorRequest input) {
        List<Callable<List<SolutionWiseFareBenefitsOrchestratorDto>>> fareBenefitsWorkflowTasks = new ArrayList<>();
        List<SolutionWiseFareBenefitsOrchestratorDto> solutionWiseFareBenefitsOrchestratorDtoList = new ArrayList<>();

        fareBenefitsWorkflowGenerator.generate(input).forEach(fareBenefitWorkflow -> fareBenefitsWorkflowTasks.add(fareBenefitWorkflow::process));
        List<Future<List<SolutionWiseFareBenefitsOrchestratorDto>>> futures;

        long startTime = System.currentTimeMillis();

        try {
            futures = executorService.invokeAll(fareBenefitsWorkflowTasks);
            for (Future<List<SolutionWiseFareBenefitsOrchestratorDto>> future : futures) {
                solutionWiseFareBenefitsOrchestratorDtoList.addAll(future.get());
            }
            CompletableFuture.runAsync(() -> pushToMonitoring(startTime, System.currentTimeMillis(), solutionWiseFareBenefitsOrchestratorDtoList, 200, input.getMonitoringContext(), input.getSolutionDetailsOrchestratorDtoList(),null));
        } catch (Exception ex) {
            CompletableFuture.runAsync(() -> pushToMonitoring(startTime, System.currentTimeMillis(), null, 500, input.getMonitoringContext(), input.getSolutionDetailsOrchestratorDtoList(), ex));
            log.error("Exception while executing fare benefit workflow tasks for {} , exception {} at {}", input, ex.getClass().getName(), ex.getStackTrace());
        }


        return FareBenefitsOrchestratorResponse.builder().solutionWiseFareBenefitsOrchestratorDtoList(solutionWiseFareBenefitsOrchestratorDtoList).build();
    }

    private void pushToMonitoring(long start, long end, List<SolutionWiseFareBenefitsOrchestratorDto> response, int statusCode, Context monitoringContext, List<SolutionDetailsOrchestratorDto> request, Exception exception) {
        try {
            statsHelper.pushToStats(start, end, statsUtil.getObjectInByte(Objects.nonNull(exception) ? exception : response), statusCode, "FareBenefitsAPI", monitoringContext, statsUtil.getObjectInByte(request), "FARE_BENEFITS", "FARE_BENEFITS", ApiStatsDto.HttpMethod.POST);

            Map<String, Object> newRelicData = new HashMap<>();

            newRelicData.put(NewRelicParams.TIME_TAKEN.name(), end - start);
            newRelicData.put(NewRelicParams.STATUS.name(), statusCode);
            if(Objects.nonNull(exception)) {
                newRelicData.put("ERROR", exception);
            }
            newRelicData.put(NewRelicParams.SUPPLIER.name(), request.size() > 0 ? request.get(0).getSupplier() : "UNKNOWN");
            newRelicData.put(NewRelicParams.ITINERARY_ID.name(), monitoringContext.getItineraryId());
            newRelicData.put("LAYER", "ORCHESTRATOR");

            newRelicHelper.pushToNewRelic(newRelicData);
        } catch (Exception ex) {
            log.error("Exception while pushing to stats - {}", ex);
        }
    }
}
