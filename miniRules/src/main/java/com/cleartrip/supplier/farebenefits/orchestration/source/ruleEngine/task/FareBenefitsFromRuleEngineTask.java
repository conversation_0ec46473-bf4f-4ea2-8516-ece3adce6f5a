package com.cleartrip.supplier.farebenefits.orchestration.source.ruleEngine.task;

import com.air.sis.rule.FareBenefitRuleEngine;
import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.supplier.farebenefits.orchestration.source.ruleEngine.dto.FareBenefitRuleEngineRequestDto;
import com.cleartrip.supplier.farebenefits.orchestration.source.ruleEngine.dto.FareBenefitsRuleEngineResponseDto;
import com.cleartrip.supplier.minirule.monitoring.NewRelicParams;
import com.cleartrip.supplier.search.ruleEngine.RuleEngineWrapper;
import com.cleartrip.utility.workflow.design.Task;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openl.generated.beans.FareBenefitInfo;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
public class FareBenefitsFromRuleEngineTask implements Task<FareBenefitRuleEngineRequestDto, FareBenefitsRuleEngineResponseDto> {

    private final RuleEngineWrapper<FareBenefitRuleEngine> ruleEngineWrapper;
    private final NewRelicHelper newRelicHelper;

    public FareBenefitsFromRuleEngineTask(RuleEngineWrapper<FareBenefitRuleEngine> fareBenefitRuleEngineRuleEngineWrapper,
                                          NewRelicHelper newRelicHelper) {
        this.ruleEngineWrapper = fareBenefitRuleEngineRuleEngineWrapper;
        this.newRelicHelper = newRelicHelper;
    }

    @Override
    public FareBenefitsRuleEngineResponseDto run(FareBenefitRuleEngineRequestDto request) {
        List<FareBenefitInfo> fareBenefitInfoList = new ArrayList<>();

        try {
            fareBenefitInfoList = Arrays.stream(ruleEngineWrapper.getRuleEngine().getFareBenefits(request.getSupplier(),
                            request.getAirlineCode(),
                            request.getFareCategory(),
                            request.getFareSubCategory(),
                            request.getProductClass(),
                            request.getBookingType(),
                            request.getCountry(),
                            request.getCabinClass(),
                            StringUtils.split(request.getMetaData().getComboFbc(), ",")[0]
                    ))
                    .collect(Collectors.toList());
            List<FareBenefitInfo> finalFareBenefitInfoList = fareBenefitInfoList;
//            CompletableFuture.runAsync(() -> pushDataForMonitoring(request, null, finalFareBenefitInfoList));
        } catch (Exception ex) {
//            CompletableFuture.runAsync(() -> pushDataForMonitoring(request, ex, null));
            log.error("Exception while getting fare benefits data from rule engine for {}, and exception {} at {}",
                    request, ex.getClass().getName(), ex.getStackTrace());
        }

        return FareBenefitsRuleEngineResponseDto.builder()
                .fareBenefitInfoList(fareBenefitInfoList)
                .comboFbc(request.getMetaData().getComboFbc())
                .journeyDetails(request.getMetaData().getJourneyDetails())
                .build();
    }

    private void pushDataForMonitoring(FareBenefitRuleEngineRequestDto request, Exception exception, List<FareBenefitInfo> fareBenefitInfoList) {
        try {
            Map<String, Object> newRelicData = new HashMap<>();

            newRelicData.put(NewRelicParams.SUPPLIER.name(), request.getSupplier());
            newRelicData.put("AIRLINE_CODE", request.getAirlineCode());
            newRelicData.put("FARE_CATEGORY", request.getFareCategory());
            newRelicData.put("FARE_SUB_CATEGORY", request.getFareSubCategory());
            newRelicData.put("PRODUCT_CLASS", request.getProductClass());
            newRelicData.put("BOOKING_TYPE", request.getBookingType());
            newRelicData.put("COUNTRY", request.getCountry());
            newRelicData.put("CABIN_CLASS", request.getCabinClass());
            newRelicData.put(NewRelicParams.COMBO_FBC.name(), request.getMetaData().getComboFbc());
            newRelicData.put("JOURNEY", request.getMetaData().getJourneyDetails());
            newRelicData.put("LAYER", "TASK");

            int status = 200;
            if(Objects.nonNull(exception)) {
                newRelicData.put("ERROR", exception.getClass().getName() + "at" + exception.getMessage());
                status = 500;
            }
            newRelicData.put("STATUS", status);
            if(Objects.nonNull(fareBenefitInfoList)) {
                String response = fareBenefitInfoList.stream()
                        .map(fareBenefitInfo -> fareBenefitInfo.getBenefitType() + " " + fareBenefitInfo.getValue())
                        .collect(Collectors.joining(","));
                newRelicData.put("RESPONSE", response);
            }

            newRelicHelper.pushToNewRelic(newRelicData);

        } catch (Exception ex) {
            log.error("Exception while pushing data to stats : {}", ex.getStackTrace());
        }

    }
}
