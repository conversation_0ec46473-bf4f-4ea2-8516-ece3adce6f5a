buildscript {
    repositories {
        mavenCentral()
        mavenLocal()
        maven {
            allowInsecureProtocol = true
            url "${ARTIFACTORY_URL}/${ARTIFACTORY_REPO}"
            credentials {
                username = "${ARTIFACTORY_USERNAME}"
                password = "${ARTIFACTORY_PASSWORD}"
            }
            name = "artifactory"
        }
        maven { url "https://repo.spring.io/snapshot" }
        maven { url "https://repo.spring.io/milestone" }
    }
    dependencies {
        classpath 'org.openl.rules.gradle:openl-gradle-plugin:release-2.0.2'
    }
}
plugins {
    id 'java'
}

group 'com.cleartrip.supplier'
version 'unspecified'

repositories {
    mavenCentral()
    mavenLocal()
    maven {
        allowInsecureProtocol = true
        url "${ARTIFACTORY_URL}/${ARTIFACTORY_REPO}"
        credentials {
            username = "${ARTIFACTORY_USERNAME}"
            password = "${ARTIFACTORY_PASSWORD}"
        }
        name = "artifactory"
    }
    maven { url "https://repo.spring.io/snapshot" }
    maven { url "https://repo.spring.io/milestone" }
}

dependencies {
    implementation project(":infrastructure")
    implementation project(":starter")
    implementation project(":search")
    implementation project(":api")
    implementation project(":common")
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
    implementation 'com.cleartrip.utility:workflow:1.0.4-SNAPSHOT'
    implementation group: 'org.springframework.data', name: 'spring-data-redis', version: '2.6.1'
    implementation group: 'redis.clients', name: 'jedis', version: '4.2.1'
    implementation(group: 'com.cleartrip.air.sms', name: 'api', version: '26.24-SNAPSHOT') {
        exclude group: 'com.cleartrip.air.config.management', module: 'api'
    }
    implementation 'com.google.protobuf:protobuf-java:3.22.2'
    implementation group: 'com.google.protobuf', name: 'protobuf-java-util', version: '3.22.2'
    implementation group: 'com.squareup.retrofit2', name: 'retrofit', version: '2.9.0'
    implementation group: 'com.squareup.retrofit2', name: 'converter-jackson', version: '2.9.0'
    implementation group: 'com.squareup.retrofit2', name: 'converter-scalars', version: '2.9.0'
    implementation group: 'com.github.luben', name: 'zstd-jni', version: '1.5.2-3'

    implementation group: 'io.lettuce', name: 'lettuce-core', version: '6.1.6.RELEASE'
    implementation group: 'redis.clients', name: 'jedis', version: '4.2.1'

    implementation 'org.mapstruct:mapstruct:1.5.3.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
    implementation("com.ct.instrumentation:ct-instrumentation:1.1.0-SNAPSHOT")
    implementation ('org.cleartrip.utility:monitoring:1.2.7-RELEASE') {
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-databind'
    }
    implementation group: 'com.cleartrip.air.themis.client', name: 'client', version: '1.0-SNAPSHOT'
    implementation 'org.openl.rules:org.openl.rules:5.25.14'
    implementation("com.cleartrip.air.config.management:core:$CT_CM_VERSION") {
        force = true
        exclude group: 'org.projectlombok'
        exclude group: 'ch.qos.logback'
        exclude group: 'org.quartz-scheduler'
        exclude module: 'spring-cloud-starter-consul-discovery'
        exclude module: 'jedis'
        exclude module: 'spring-context-support'
        //exclude group: 'org.eclipse.jetty'
        exclude module: 'spring-boot-starter-webflux'
        exclude module: 'spring-boot-starter-actuator'
        exclude module: 'spring-tx'
        exclude module: 'spring-boot-starter-validation'
        exclude module: 'spring-cloud-starter-consul-all'
    }
}

test {
    useJUnitPlatform()
}

apply plugin: "org.openl.rules.gradle.openl.gen.plugin"
openlgenPluginConfiguration {
    [
            fareRuleIDsRule {
                interfaceClass = "com.cleartrip.supplier.rule.FareRuleConfigRuleEngine"
                sourceDirectory = file('src/main/resources/openl/qa/FareRules')
            }
    ]
}
task setSrcDirs {
    File outputDirectory = file('build/generated-sources/openl')

    if (outputDirectory.isDirectory())
        sourceSets.main.java.srcDirs += outputDirectory
}