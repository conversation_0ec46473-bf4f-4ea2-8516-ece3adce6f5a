image: maven:3.8.6-jdk-11

pipelines:
  custom:
    BUILD:
    - step:
        name: Build
        services:
          - docker
        caches:
          - gradle
        script: # Modify the commands below to build your repository.
          - APP_NAME=me-supply-core
          - sed -i "s/http:\/\/***********:9080/https:\/\/$DOCKER_REG_URL/g" build.gradle gradle.properties
          - ./gradlew clean openlgen build -xtest
          - mv Manager/build/libs/*-all.jar app.jar
          - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
          ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
          - <NAME_EMAIL>:me-cleartrip/docker_dependency.git 
          - mv docker_dependency/* .
          - sed -i "s/\${NEW_RELIC_LICENSE_KEY}/$MON_LICENSE_KEY/g" newrelic/newrelic.yml
          - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
          - docker build -t $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
          - rm -rf /root/.docker/config.json
          - docker login -u $DOCKER_REG_UN -p $DOCKER_REG_PW $DOCKER_REG_URL
          - docker push $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER
          
    BUILD_ME_SUPPLY_CORE_META:
    - step:
        name: Build
        services:
          - docker
        caches:
          - gradle
        script: # Modify the commands below to build your repository.
          - APP_NAME=me-supply-core-meta
          - sed -i "s/http:\/\/***********:9080/https:\/\/$DOCKER_REG_URL/g" build.gradle gradle.properties
          - ./gradlew clean openlgen build -xtest
          - mv Manager/build/libs/*-all.jar app.jar
          - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
          ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
          - <NAME_EMAIL>:me-cleartrip/docker_dependency.git 
          - mv docker_dependency/* .
          - sed -i "s/\${NEW_RELIC_LICENSE_KEY}/$MON_LICENSE_KEY/g" newrelic/newrelic.yml
          - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
          - docker build -t $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
          - rm -rf /root/.docker/config.json
          - docker login -u $DOCKER_REG_UN -p $DOCKER_REG_PW $DOCKER_REG_URL
          - docker push $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER
          
    DEPLOY_ME_SUPPLY_CORE_META:
    - variables: #list variable names under here
        - name: BUILD_NUMBER
    - step:
        name: Deploy APP to QA meta
        deployment: nonpci-qa
        script:
          - if [ -z $BUILD_NUMBER ]; then echo "BUILD_NUMBER IS MANDATORY FOR THE DEPLOYMENT" ; exit 1 ; fi
          - APP_NAME=me-supply-core-meta
          - Team=air
          - ssh $GKE_USER_NAME@$GKE_PROXY_SERVER_IP "docker pull $IMAGE_URL/$APP_NAME:$BUILD_NUMBER ; docker tag $IMAGE_URL/$APP_NAME:$BUILD_NUMBER $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; docker push $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; kubectl set image deployment $APP_NAME $APP_NAME=$GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER --namespace $Team --record"

    DEPLOY_MULTI_OR_SINGLE_APP_TO_CANARY_AND_PROD:
        - variables:
             - name: MULTI
               default: true
               allowed-values:
                - true
                - false
             - name: Project
             - name: FLAVOURS
        - step:
            name: Build GKE Prod
            deployment: production
            services:
              - docker
            caches:
              - gradle
            script: # Modify the commands below to build your repository
            - APP_NAME=me-supply-core
            - Team=air
            - sed -i "s/http:\/\/***********:9080/https:\/\/$DOCKER_REG_URL/g" build.gradle gradle.properties
            - ./gradlew clean openlgen build -xtest
            - mv Manager/build/libs/*-all.jar app.jar
            - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
            ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
            - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - sed -i "s/\${NEW_RELIC_LICENSE_KEY}/$MON_LICENSE_KEY/g" newrelic/newrelic.yml
            - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
            - docker build -t $GCR_REG_NAME/$GCLOUD_ME_PROD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
            - docker save --output app.tar $GCR_REG_NAME/$GCLOUD_ME_PROD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER
            - rm -rf /root/.docker/config.json

            artifacts:
                - app.tar

        - step:
            name: Build Image push to GCR
            image: google/cloud-sdk:alpine
            services:
              - docker
            script:
              - APP_NAME=me-supply-core
              - docker load --input app.tar
              - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
              - mv docker_dependency/* .
              - echo "$GCR_GSA_json" > /var/run/gcr_gsa.json
              - gcloud auth activate-service-account --key-file /var/run/gcr_gsa.json
              - gcloud auth configure-docker
              - docker push $GCR_REG_NAME/$GCLOUD_ME_PROD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER

        - step:
            name: Deploy app to canary
            trigger: manual
            script:
              - Team=air
              - APP_NAME=me-supply-core
              - Type=DEPLOY
              - if [[ "$MULTI" = "true" && "$FLAVOURS" =~ " " ]]; then
              - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
              - mv docker_dependency/* .
              - chmod +x $CANARY_MULTI
              - ./$CANARY_MULTI $Team $Type $APP_NAME
              - elif [[ "$MULTI" = "false" && ! $FLAVOURS =~ " " ]]; then
              - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
              - mv docker_dependency/* .
              - chmod +x $CANARY_MULTI
              - ./$CANARY_MULTI $Team $Type $APP_NAME
              - else
              - echo "Some error in flavours input"
              - exit 1
              - fi

        - step:
            name: Deploy to GKE
            trigger: manual #Only allow admins to deploy to this environment
            script:
              - Team=air
              - APP_NAME=me-supply-core
              - Type=DEPLOY
              - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
              - mv docker_dependency/* .
              - chmod +x $PROD_MULTI
              - ./$PROD_MULTI $Team $Type $APP_NAME

    DEPLOY_BETA_TO_PROD:
        - variables:
             - name: Project
        - step:
            name: Build GKE Prod
            deployment: production
            services:
              - docker
            caches:
              - gradle
            script: # Modify the commands below to build your repository
            - APP_NAME=me-supply-core
            - Team=air
            - sed -i "s/http:\/\/***********:9080/https:\/\/$DOCKER_REG_URL/g" build.gradle gradle.properties
            - ./gradlew clean openlgen build -xtest
            - mv Manager/build/libs/*-all.jar app.jar
            - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
            ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
            - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - sed -i "s/\${NEW_RELIC_LICENSE_KEY}/$MON_LICENSE_KEY/g" newrelic/newrelic.yml
            - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
            - docker build -t $GCR_REG_NAME/$GCLOUD_ME_PROD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
            - docker save --output app.tar $GCR_REG_NAME/$GCLOUD_ME_PROD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER
            - rm -rf /root/.docker/config.json

            artifacts:
                - app.tar

        - step:
            name: Build Image push to GCR
            image: google/cloud-sdk:alpine
            services:
              - docker
            script:
              - APP_NAME=me-supply-core
              - docker load --input app.tar
              - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
              - mv docker_dependency/* .
              - echo "$GCR_GSA_json" > /var/run/gcr_gsa.json
              - gcloud auth activate-service-account --key-file /var/run/gcr_gsa.json
              - gcloud auth configure-docker
              - docker push $GCR_REG_NAME/$GCLOUD_ME_PROD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER

        - step:
            name: Deploy to GKE
            trigger: manual #Only allow admins to deploy to this environment
            script:
              - Team=air
              - APP_NAME=me-supply-core
              - Type=DEPLOY
              - ssh root@$ME_PROD_NON_PCI_PROXY_IP "kubectl set image deployment supply-core-beta supply-core-beta=$GCR_REG_NAME/$GCLOUD_ME_PROD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER --namespace $Team"

    BETA_DEPLOYMENT_SCALEDOWN:
      - variables:
          - name: DEPLOYMENT_NAME
      - step:
          name: BETA DEPLOYMENT SCALE DOWN
          script:
            - Team=air
            - ssh root@$ME_PROD_NON_PCI_PROXY_IP "kubectl scale deployment DEPLOYMENT_NAME --replicas=0 --namespace $Team"

    DEPLOY_APP_TO_GKE:
    - variables: #list variable names under here
        - name: BUILD_NUMBER
    - step:
        name: Deploy APP to PROD
        deployment: production
        script:
          - if [ -z $BUILD_NUMBER ]; then echo "BUILD_NUMBER IS MANDATORY FOR THE DEPLOYMENT" ; exit 1 ; fi
          - APP_NAME=me-supply-core
          - Team=air
          - ssh $GKE_USER_NAME@$GKE_PROXY_SERVER_IP "docker pull $IMAGE_URL/$APP_NAME:$BUILD_NUMBER ; docker tag $IMAGE_URL/$APP_NAME:$BUILD_NUMBER $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; docker push $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; kubectl set image deployment $APP_NAME $APP_NAME=$GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER --namespace $Team --record"

    BUILD_ME_SUPPLY_CORE_DC:
    - step:
        name: Build
        services:
          - docker
        caches:
          - gradle
        script: # Modify the commands below to build your repository.
          - APP_NAME=me-supply-core-dc
          - sed -i "s/http:\/\/***********:9080/https:\/\/$DOCKER_REG_URL/g" build.gradle gradle.properties
          - ./gradlew clean openlgen build -xtest
          - mv Manager/build/libs/*-all.jar app.jar
          - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
          ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
          - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
          - mv docker_dependency/* .
          - sed -i "s/\${NEW_RELIC_LICENSE_KEY}/$MON_LICENSE_KEY/g" newrelic/newrelic.yml
          - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
          - docker build -t $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
          - rm -rf /root/.docker/config.json
          - docker login -u $DOCKER_REG_UN -p $DOCKER_REG_PW $DOCKER_REG_URL
          - docker push $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER

    DEPLOY_ME_SUPPLY_CORE_DC:
    - variables: #list variable names under here
        - name: BUILD_NUMBER
    - step:
        name: Deploy APP to PROD
        deployment: production
        script:
          - if [ -z $BUILD_NUMBER ]; then echo "BUILD_NUMBER IS MANDATORY FOR THE DEPLOYMENT" ; exit 1 ; fi
          - APP_NAME=me-supply-core-dc
          - Team=air
          - ssh $GKE_USER_NAME@$GKE_PROXY_SERVER_IP "docker pull $IMAGE_URL/$APP_NAME:$BUILD_NUMBER ; docker tag $IMAGE_URL/$APP_NAME:$BUILD_NUMBER $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; docker push $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; kubectl set image deployment $APP_NAME $APP_NAME=$GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER --namespace $Team --record"

    BUILD_ME_SUPPLY_CORE_4HOLD:
    - step:
        name: Build
        services:
          - docker
        caches:
          - gradle
        script: # Modify the commands below to build your repository.
          - APP_NAME=me-supply-core-4hold
          - sed -i "s/http:\/\/***********:9080/https:\/\/$DOCKER_REG_URL/g" build.gradle gradle.properties
          - ./gradlew clean openlgen build -xtest
          - mv Manager/build/libs/*-all.jar app.jar
          - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
          ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
          - <NAME_EMAIL>:me-cleartrip/docker_dependency.git
          - mv docker_dependency/* .
          - sed -i "s/\${NEW_RELIC_LICENSE_KEY}/$MON_LICENSE_KEY/g" newrelic/newrelic.yml
          - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
          - docker build -t $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
          - rm -rf /root/.docker/config.json
          - docker login -u $DOCKER_REG_UN -p $DOCKER_REG_PW $DOCKER_REG_URL
          - docker push $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER

    DEPLOY_ME_SUPPLY_CORE_4HOLD:
    - variables: #list variable names under here
        - name: BUILD_NUMBER
    - step:
        name: Deploy APP to PROD
        deployment: production
        script:
          - if [ -z $BUILD_NUMBER ]; then echo "BUILD_NUMBER IS MANDATORY FOR THE DEPLOYMENT" ; exit 1 ; fi
          - APP_NAME=me-supply-core-4hold
          - Team=air
          - ssh $GKE_USER_NAME@$GKE_PROXY_SERVER_IP "docker pull $IMAGE_URL/$APP_NAME:$BUILD_NUMBER ; docker tag $IMAGE_URL/$APP_NAME:$BUILD_NUMBER $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; docker push $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER ; kubectl set image deployment $APP_NAME $APP_NAME=$GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BUILD_NUMBER --namespace $Team --record"
