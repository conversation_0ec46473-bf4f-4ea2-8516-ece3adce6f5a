package com.cleartrip.supplier.credentials;

import com.cleartrip.config.management.core.resources.credential.CachedCredentials;
import com.cleartrip.supplier.credentials.config.CredentialPropertiesProvider;
import com.cleartrip.supplier.credentials.dto.CredentialRuleEvaluatorRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CredentialServiceImpl implements CredentialService {
    private final CredentialRuleEvaluatorImpl credentialRuleEvaluator;
    private final CachedCredentials cachedCredentials;

    private final CredentialPropertiesProvider credentialPropertiesProvider;

    private final ObjectMapper objectMapper;

    private static final String CREDENTIAL_KEY = "credentialId";

    private static final String PROMO_CODE = "commission";

    @Inject
    public CredentialServiceImpl(CredentialRuleEvaluatorImpl credentialRuleEvaluator, CachedCredentials cachedCredentials,
                                 ObjectMapper objectMapper, CredentialPropertiesProvider credentialPropertiesProvider) {
        this.credentialRuleEvaluator = credentialRuleEvaluator;
        this.cachedCredentials = cachedCredentials;
        this.objectMapper = objectMapper;
        this.credentialPropertiesProvider = credentialPropertiesProvider;
    }

    @Override
    public Map<String, String> getCredentialFromId(String credentialId) {
        Map<String, Map<String, String>> resourceValue = cachedCredentials.getResourceValue();
        Map<String, String> credentials = resourceValue.get(credentialId);
        credentials.put(CREDENTIAL_KEY, credentialId);
        return credentials;
    }

    @Override
    public Optional<String> getCredentialKey(CredentialRuleEvaluatorRequest credentialRuleEvaluatorRequest) {
        return credentialRuleEvaluator.fetchCredentialKey(credentialRuleEvaluatorRequest);
    }

    @Override
    public Optional<String> getAgentName(String supplier, String credentialId) {
        Map<String, Map<String, String>> resourceValue = cachedCredentials.getResourceValue();
        Map<String, String> credentials = resourceValue.get(credentialId);
        Map<String, List<String>> supplierPCCKeyMapping = getSupplierPCCKeyMapping();
        final List<String> agentNameKeys = supplierPCCKeyMapping.get(supplier);
        return getAgentKeyValue(supplier, credentials, agentNameKeys);
    }

    @Override
    public Optional<String> getPromoCodeFromCredential(String credentialKey) {
        Map<String, Map<String, String>> resourceValue = cachedCredentials.getResourceValue();
        Map<String, String> credentials = resourceValue.get(credentialKey);
        if (MapUtils.isNotEmpty(credentials)) {
            String promoCode = credentials.get(PROMO_CODE);
            return (StringUtils.isBlank(promoCode) || "retail".equalsIgnoreCase(promoCode)
                    || "corp".equalsIgnoreCase(promoCode)) || "0".equalsIgnoreCase(promoCode) ? Optional.empty() : Optional.ofNullable(promoCode);
        }
        return Optional.empty();
    }

    @Override
    public Optional<String> getAgentPcc(String supplier, String credentialId) {
        Map<String, Map<String, String>> resourceValue = cachedCredentials.getResourceValue();
        Map<String, String> credentials = resourceValue.get(credentialId);
        Map<String, List<String>> supplierPCCKeyMapping = getSupplierAgentPCCKeyMapping();
        final List<String> agentNameKeys = supplierPCCKeyMapping.get(supplier);
        return getAgentKeyValue(supplier, credentials, agentNameKeys);
    }

    private Optional<String> getAgentKeyValue(String supplier, Map<String, String> credentials, List<String> agentNameKeys) {
        if (agentNameKeys.size() > 1) {
            final String agentNameValue = getAgentNameValue(agentNameKeys, credentials, supplier);
            return Optional.ofNullable(agentNameValue);
        }
        return Optional.ofNullable(credentials.get(agentNameKeys.get(0)));
    }

    private String getAgentNameValue(List<String> agentNameKeys, Map<String, String> credentials, String supplier) {
        final String delimiter = getDelimiter(supplier);
        return agentNameKeys.stream()
                .filter(StringUtils::isNotEmpty)
                .map(credentials::get)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(delimiter));
    }

    private String getDelimiter(String supplier) {
        Map<String, String> supplierPCCKeyDelimiterMapping = getSupplierPCCKeyDelimiterMapping();
        final String delimiter = supplierPCCKeyDelimiterMapping.get(supplier);
        if (StringUtils.isEmpty(delimiter)) {
            log.error("Not Found delimiter for supplier: {}", supplier);
            throw new RuntimeException("Not Found delimiter for supplier: " + supplier);
        }
        return delimiter;
    }

    public Map<String, String> getSupplierPCCKeyDelimiterMapping() {
        try {
            String jsonMapping = credentialPropertiesProvider.getSupplierPCCKeyDelimiterMappingConfiguration();
            return objectMapper.readValue(jsonMapping, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            log.error("error while parsing supplier pcc delimiter key config map", e);
        }
        return Collections.emptyMap();
    }

    private Map<String, List<String>> getSupplierPCCKeyMapping() {
        try {
            String jsonMapping = credentialPropertiesProvider.getSupplierPCCKeyMappingConfiguration();
            return objectMapper.readValue(jsonMapping, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("error while parsing supplier pcc key config map", e);
        }
        return Collections.emptyMap();
    }

    private Map<String, List<String>> getSupplierAgentPCCKeyMapping() {
        try {
            String jsonMapping = credentialPropertiesProvider.getSupplierAgentPCCKeyMappingConfiguration();
            return objectMapper.readValue(jsonMapping, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("error while parsing supplier pcc key config map", e);
        }
        return Collections.emptyMap();
    }


}
