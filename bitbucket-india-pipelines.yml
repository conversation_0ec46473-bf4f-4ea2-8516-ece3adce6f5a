image: maven:3.8.6-jdk-11

definitions:
  steps:
    - step: &build-test-sonarcloud
        name: Sonarcloud Analysis
        caches:
          - maven
          - sonar
        script:
          - sed -i "s/http:\/\/*************:9080/https:\/\/$QA2_DOCKER_REG_URL/g" pom.xml
          - echo $SETTINGS_XML > ~/.m2/settings.xml
          - mvn -B verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar
  caches:
    sonar: ~/.sonar
    maven: ~/.m2

clone:
  depth: full

pipelines:
  custom:
    Analyze on SonarCloud:
      - step:
          *build-test-sonarcloud
    BUILD_QA:
      - step:
          name: Build
          services:
            - docker
          caches:
            - maven
            - docker
          script: # Modify the commands below to build your repository.
            - APP_NAME=supply-core
            - sed -i "s/http:\/\/*************:9080/https:\/\/$QA2_DOCKER_REG_URL/g" build.gradle gradle.properties
            - ./gradlew openlgen :Manager:build
            - mv Manager/build/libs/*-all.jar app.jar
            - cp Manager/src/main/resources/config-qa.yaml config-qa.yaml
            ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
            - docker build -t $DOCKER_REG_URL/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
            - chmod +x $QA2_LOGIN
            - ./$QA2_LOGIN $APP_NAME

    DEPLOY_TO_QA:
      - variables:
          - name: Environment
          - name: Project
          - name: BUILD_NUMBER
          - name: QA_DEPLOYMENT_NAME
            default: supply-core
            allowed-values:
              - "supply-core"
              - "supply-core-book"
              - "air-supply-core-book"
      - step:
          name: Deploy to QA
          deployment: test
          script:
            - if [ -z $BUILD_NUMBER ]; then echo "BUILD_NUMBER IS MANDATORY FOR THE DEPLOYMENT" ; exit 1 ; fi
            - export APP_NAME=supply-core
            - Team=air
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - chmod +x $QA_APP_DEPLOY
            - ./$QA_APP_DEPLOY $Team $APP_NAME


    GCPQA_DEPLOYMENT_RESTART:
      - step:
          name: QA Deployment Restart
          script:
            - APP_NAME=supply-core
            - Team=air
            - ssh root@$QA2_PROXY_SERVER_IP "kubectl set env deployment/$APP_NAME LASTRESTART=`date +%y%m%d%H%M%S` --namespace $Team"


    BUILD_AND_DEPLOY_TO_BETA:
      - variables:
          - name: Project
      - step:
          name: Build GKE Beta
          deployment: beta
          services:
            - docker
          caches:
            - gradle
          script: # Modify the commands below to build your repository
            - Team=air
            - APP_NAME=supply-core
            - sed -i "s/http:\/\/*************:9080/https:\/\/$QA2_DOCKER_REG_URL/g" build.gradle gradle.properties
            - ./gradlew openlgen :Manager:build
            - mv Manager/build/libs/*-all.jar app.jar
            - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
            ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
            - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
            - docker build -t $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
            - docker save --output app.tar $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER
            - rm -rf /root/.docker/config.json

          artifacts:
            - app.tar
      - step:
          name: Build Image push to GCR
          image: google/cloud-sdk:alpine
          services:
            - docker
          script:
            - APP_NAME=supply-core
            - docker load --input app.tar
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - echo "$GCR_GSA_json" > /var/run/gcr_gsa.json
            - gcloud auth activate-service-account --key-file /var/run/gcr_gsa.json
            - gcloud auth configure-docker
            - docker push $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER
      - step:
          name: Deploy to BETA
          script:
            - Team=air
            - APP_NAME=supply-core
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - chmod +x $BETA_DEPLOY_APP
            - ./$BETA_DEPLOY_APP $APP_NAME $Team

    DEPLOY_MULTI_OR_SINGLE_APP_TO_CANARY_AND_PROD:
      - variables:
          - name: MULTI
            default: true
            allowed-values:
              - true
              - false
          - name: Project
          - name: FLAVOURS
      - step:
          name: Build GKE Prod
          deployment: prod-build
          services:
            - docker
          caches:
            - gradle
          script: # Modify the commands below to build your repository
            - APP_NAME=supply-core
            - Team=air
            - sed -i "s/http:\/\/*************:9080/https:\/\/$QA2_DOCKER_REG_URL/g" build.gradle gradle.properties
            - ./gradlew openlgen :Manager:build
            - mv Manager/build/libs/*-all.jar app.jar
            - cp Manager/src/main/resources/config-prod.yaml config-prod.yaml
            ## Set $DOCKER_REG_URL, $DOCKER_REG_UN, $DOCKER_REG_PW and $PROXY_SERVER_IP as environment variables in repository settings
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - sed -i "s/\${NEW_RELIC_LICENSE_KEY}/$MON_LICENSE_KEY/g" newrelic/newrelic.yml
            - docker login -u $DOCKERHUB_UN -p $DOCKERHUB_PW
            - docker build -t $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER .
            - docker save --output app.tar $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER
            - rm -rf /root/.docker/config.json

          artifacts:
            - app.tar

      - step:
          name: Build Image push to GCR
          image: google/cloud-sdk:alpine
          services:
            - docker
          script:
            - APP_NAME=supply-core
            - docker load --input app.tar
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - echo "$GCR_GSA_json" > /var/run/gcr_gsa.json
            - gcloud auth activate-service-account --key-file /var/run/gcr_gsa.json
            - gcloud auth configure-docker
            - docker push $GCR_REG_NAME/$GCLOUD_PROJECT/$APP_NAME:$BITBUCKET_BUILD_NUMBER

      - step:
          name: Deploy app to canary
          trigger: manual
          script:
            - Team=air
            - APP_NAME=supply-core
            - Type=DEPLOY
            - if [[ "$MULTI" = "true" && "$FLAVOURS" =~ " " ]]; then
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - chmod +x $CANARY_MULTI
            - ./$CANARY_MULTI $Team $Type $APP_NAME
            - elif [[ "$MULTI" = "false" && ! $FLAVOURS =~ " " ]]; then
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - chmod +x $CANARY_MULTI
            - ./$CANARY_MULTI $Team $Type $APP_NAME
            - else
            - echo "Some error in flavours input"
            - exit 1
            - fi

      - step:
          name: Deploy to GKE
          trigger: manual #Only allow admins to deploy to this environment
          script:
            - Team=air
            - APP_NAME=supply-core
            - Type=DEPLOY
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - chmod +x $PROD_MULTI
            - ./$PROD_MULTI $Team $Type $APP_NAME
          after-script:
            - APP_NAME=supply-core
            - cp ./docker-build/jq /usr/bin/jq
            - chmod +x /usr/bin/jq
            - chmod +x $SLACK_NOTIFICATION
            - ./$SLACK_NOTIFICATION $APP_NAME
            - chmod +x $NEWRELIC_NOTIFICATION
            - ./$NEWRELIC_NOTIFICATION $APP_NAME

    GKE_MULTI_PROD_DEPLOYMENT_RESTART:
      - variables:
          - name: FLAVOURS
          - name: Project
      - step:
          name: Deployment Restart
          script:
            - Team=air
            - APP_NAME=distribution-core
            - Type=RESTART
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - chmod +x $PROD_MULTI
            - ./$PROD_MULTI $Team $Type $APP_NAME


    MULTI_CANARY_GKE_DEPLOYMENT_SCALEDOWN:
      - variables:
          - name: FLAVOURS
          - name: Project
      - step:
          name: MULTI CANARY DEPLOYMENT SCALE DOWN
          script:
            - Team=air
            - Type=CANARYSCALEDOWN
            - <NAME_EMAIL>:cleartrip/docker_dependency.git
            - mv docker_dependency/* .
            - chmod +x $MULTI_CANARY_SCALEDOWN
            - ./$MULTI_CANARY_SCALEDOWN $Team $Type $APP_NAME

    supply-core_PROD_DEPLOYMENT_RESTART:
      - step:
          name: Deployment Restart
          script:
            - APP_NAME=supply-core
            - Team=air
            - ssh root@$PROXY_SERVER_IP "kubectl set env deployment/$APP_NAME LASTRESTART=`date +%y%m%d%H%M%S` --namespace $Team"

    GKE_PROD_DEPLOYMENT_ROLLBACKALL:
      - variables:
          - name: BUILD_NUMBER
          - name: DEPLOYMENT_NAME
      - step:
          name: Deployment Rollback
          script:
            - Team=air
            - REVISION=$(ssh -o LogLevel=quiet root@$PROXY_SERVER_IP "kubectl rollout history deploy $DEPLOYMENT_NAME -n $Team | grep $BUILD_NUMBER | cut -d ' ' -f1 | head -n 1")
            - ssh -o LogLevel=quiet root@$PROXY_SERVER_IP "kubectl rollout undo deploy $DEPLOYMENT_NAME -n $Team --to-revision=$REVISION"
