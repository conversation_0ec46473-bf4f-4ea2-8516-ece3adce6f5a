package com.cleartrip.supplier.hold.comparator;

import com.cleartrip.air.sms.api.v2.search.BookedPromise;
import com.cleartrip.supplier.hold.domain.value.hold.enums.PricingIndicator;

public class PricingComparator {

    public PricingIndicator compareBaseFare(BookedPromise upatedBookedPromise, double totalAmount) {
        final double holdTotalAmount = extractBaseFareFromBookedPromise(upatedBookedPromise);
        if (totalAmount != holdTotalAmount) {
            return PricingIndicator.PRICE_CHANGED;
        }
        return PricingIndicator.NO_CHANGE;
    }

    public double extractBaseFareFromBookedPromise(BookedPromise bookedPromise) {
        return bookedPromise.getFareDetails()
                .getTripFareSummary()
                .getTotalAmount();
    }

}
