package com.cleartrip.supplier.hold.application.impl;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.credentials.CredentialService;
import com.cleartrip.supplier.hold.application.IFlightHoldApplicationService;
import com.cleartrip.supplier.hold.application.adapters.SFFHoldRequestAdapter;
import com.cleartrip.supplier.hold.application.factory.JourneyContextFactory;
import com.cleartrip.supplier.hold.application.models.ReleaseHoldRequest;
import com.cleartrip.supplier.hold.application.models.ReleaseHoldResponse;
import com.cleartrip.supplier.hold.application.models.ReleaseHoldStatus;
import com.cleartrip.supplier.hold.db.daos.IJourneyContextRepository;
import com.cleartrip.supplier.hold.domain.entity.*;
import com.cleartrip.supplier.hold.domain.service.IFlightHoldDomainService;
import com.cleartrip.supplier.hold.domain.value.hold.BookingInformation;
import com.cleartrip.supplier.hold.domain.value.hold.*;
import com.cleartrip.supplier.hold.domain.value.hold.enums.FareMatchStrategy;
import com.cleartrip.supplier.hold.domain.value.hold.enums.HoldStatus;
import com.cleartrip.supplier.hold.domain.value.hold.enums.ReleaseHoldDomainStatus;
import com.cleartrip.supplier.hold.mapper.BookingInformationProtoMapper;
import com.cleartrip.supplier.hold.mapper.response.proto.HoldResponseMapper;
import com.cleartrip.supplier.hold.mapper.response.proto.ReHoldResponseMapper;
import com.cleartrip.supplier.hold.monitoring.NewRelicUtil;
import com.cleartrip.supplier.hold.monitoring.MapperUtil;
import com.cleartrip.supplier.hold.util.JourneyIdSuffixUtil;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.DTO.SupplierInfoDTO;
import com.cleartrip.supplier.sff.ISffHoldApplicationService;
import com.cleartrip.supplier.sff.SffHoldRequest;
import com.google.inject.Inject;
import com.google.inject.name.Named;
//import db.repository.JourneyStoreRepository;
import db.repository.dto.JourneyContextDTO;
import json.request.hold.HoldRequestApi;
import json.response.hold.HoldResponseApi;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.http.HttpStatus;

import java.util.*;

@Slf4j
public class FlightHoldApplicationService implements IFlightHoldApplicationService,
        ISffHoldApplicationService {

    private static final String HOLD_TASK_NAME = "HOLD";
    private static final String RE_HOLD_TASK_NAME = "RE_HOLD";
    private static final String SOS_APP_LAYER = "SUPPLY_CORE_APP_LAYER";
    private static final String HOLD_URL = "v1/journey/hold";
    private static final String RENEW_HOLD = "RENEW_HOLD";
    private static final String RE_HOLD_URL = "v1/journey/re-hold";
    private static final String AGENT_NAME = "agentName";

    private static final String SUPPLIER_MARKER = "SUPPLIER";
    private final IFlightHoldDomainService flightHoldDomainService;
    private final JourneyContextFactory journeyContextFactory;
    private final IJourneyContextRepository journeyContextRepository;

//    private final JourneyStoreRepository journeyStoreRepository;
    private final BookingInformationProtoMapper bookingInformationProtoMapper;
    private final HoldResponseMapper holdResponseMapper;
    private final ReHoldResponseMapper reHoldResponseMapper;
    private final MapperUtil mapperUtil;
    private final NewRelicHelper newRelicHelper;
    private final StatsHelper statsHelper;
    private final CredentialService credentialService;
    private final SFFHoldRequestAdapter sffHoldRequestAdapter;

    @Inject
    public FlightHoldApplicationService(
            final IFlightHoldDomainService flightHoldDomainService,
            final JourneyContextFactory journeyContextFactory,
            @Named("journeyContextRepository") final IJourneyContextRepository journeyContextRepository,
            final BookingInformationProtoMapper bookingInformationProtoMapper,
            final HoldResponseMapper holdResponseMapper,
            final ReHoldResponseMapper reHoldResponseMapper,
            final MapperUtil mapperUtil,
            @Named("holdNewRelicHelper") final NewRelicHelper newRelicHelper,
            @Named("holdStatsHelper") final StatsHelper statsHelper,
            final CredentialService credentialService) {
        this.flightHoldDomainService = flightHoldDomainService;
        this.journeyContextFactory = journeyContextFactory;
        this.journeyContextRepository = journeyContextRepository;
        this.bookingInformationProtoMapper = bookingInformationProtoMapper;
        this.holdResponseMapper = holdResponseMapper;
        this.reHoldResponseMapper = reHoldResponseMapper;
        this.mapperUtil = mapperUtil;
        this.newRelicHelper = newRelicHelper;
        this.statsHelper = statsHelper;
        this.credentialService = credentialService;
        sffHoldRequestAdapter = new SFFHoldRequestAdapter();
    }

    @Override
    public HoldResponseOuter.HoldResponse holdAndCreateJourney(final HoldRequestOuter.HoldRequest holdRequest, final Context context) {
        final long startTime = System.currentTimeMillis();
        try {
            log.info("itn id: {} performing hold", context.getItineraryId());
            final BookingInformation bookingInformation = bookingInformationProtoMapper.toBookingInformation(holdRequest.getBookingInformation());
            FareMatchStrategy fareMatchStrategy = getFareMatchStrategy(holdRequest);
            final HoldRequest holdDomainRequest = new HoldRequest(bookingInformation, holdRequest.getSolutionId(), holdRequest.getComboFbc(), context, fareMatchStrategy);
            final HoldResponse holdResponse = flightHoldDomainService.hold(holdDomainRequest);
            Map<String, String> additionalProperties = new HashMap<>();
            if (!HoldStatus.FLIGHT_NOT_AVAILABLE.equals(holdResponse.getStatus())) {
                additionalProperties = prepareAdditionalProperties(holdResponse.getFlightSolutionDTO());
                holdResponse.getAdditionalProperties().putAll(additionalProperties);
                final JourneyContext journeyContext = journeyContextFactory.createHoldJourneyContext(bookingInformation, fareMatchStrategy, holdResponse, null);
                journeyContextRepository.insert(journeyContext);
                persistInDBStore(journeyContext, holdResponse.getStatus());
            }
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareSuccessHoldParams(holdDomainRequest, holdResponse, startTime, context, HOLD_TASK_NAME));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(holdResponse), getStatusCode(holdResponse.getStatus()),
                    HOLD_URL, context, mapperUtil.getObjectToByte(holdDomainRequest), SOS_APP_LAYER, HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return holdResponseMapper.toHoldResponseProto(holdResponse, additionalProperties);
        } catch (Exception ex) {
            log.error("Exception occurred while hold", ex);
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareFailureHoldParams(holdRequest, context, startTime, ex.getMessage()));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(ex), HttpStatus.INTERNAL_SERVER_ERROR_500,
                    HOLD_URL, context, mapperUtil.getObjectToByte(holdRequest.toString()), SOS_APP_LAYER, HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return HoldResponseOuter.HoldResponse.newBuilder()
                    .setHoldStatus(HoldStatusOuterClass.HoldStatus.FLIGHT_NOT_AVAILABLE)
                    .build();
        }
    }

    private void persistInDBStore(JourneyContext journeyContext, HoldStatus status) {
        try {
            JourneyContextDTO journeyContextDTO = JourneyContextDTO.builder()
                    .journeyId(journeyContext.getJourneyId())
                    .context(mapperUtil.getObjectToString(journeyContext))
                    .status(status.name())
                    .build();

//            journeyStoreRepository.save(journeyContextDTO);
        } catch (Exception exception) {
            log.error("Error while serializing context while storing to journey store", exception);
        }
    }

    private int getStatusCode(HoldStatus status) {
        return HoldStatus.FLIGHT_NOT_AVAILABLE.equals(status) ? HttpStatus.INTERNAL_SERVER_ERROR_500 : HttpStatus.OK_200;
    }

    @Override
    public ReHoldResponseOuter.ReHoldResponse reHoldJourney(ReHoldRequestOuter.ReHoldRequest reHoldRequest, Context context) {
        final long startTime = System.currentTimeMillis();
        try {
            log.info("itn id: {} performing re-hold", context.getItineraryId());
            final String journeyId = JourneyIdSuffixUtil.getJourneyIdWithoutSuffix(reHoldRequest.getJourneyId());
            final JourneyContext journeyContext = journeyContextRepository.fetch(journeyId);
            if (Objects.isNull(journeyContext)) {
                log.error("Journey Context is null for journey id: {}", reHoldRequest.getJourneyId());
                throw new RuntimeException("Journey Context is null for journey id: {}" + reHoldRequest.getJourneyId());
            }
            final FlightSolutionDTO flightSolutionDTO = journeyContext.getFlightSolutionDTO();
            final BookingInformation bookingInformation = bookingInformationProtoMapper.toBookingInformation(reHoldRequest.getBookingInformation());
            final String credentialKey = flightSolutionDTO.getFareFamilyDTO().stream()
                    .filter(fareFamilyDTO -> fareFamilyDTO.getFareSolutionId().equalsIgnoreCase(reHoldRequest.getSolutionId()))
                    .map(fareFamilyDTO -> fareFamilyDTO.getFareMetaInfoDTO().getSupplierInfoDTO().getCredentialKey()).findAny()
                    .orElseGet(() -> flightSolutionDTO.getSolutionMetaInfoDTO().getSupplierInfoDTO().getCredentialKey());
            final ReHoldRequest reHoldDomainRequest = new ReHoldRequest(bookingInformation, journeyId, credentialKey, flightSolutionDTO, reHoldRequest.getSolutionId(), context, reHoldRequest.getComboFbc(), journeyContext.getFareMatchStrategy());
            final ReHoldResponse reHoldResponse = doReHold(reHoldDomainRequest, startTime, bookingInformation, context, journeyContext);
            return reHoldResponseMapper.toReHoldResponseProto(reHoldResponse, reHoldResponse.getAdditionalProperties());
        } catch (Exception ex) {
            log.error("Exception occurred while while doing re-hold: ", ex);
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareFailedReHoldParams(reHoldRequest, startTime, context, ex.getMessage()));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(ex), HttpStatus.INTERNAL_SERVER_ERROR_500,
                    RE_HOLD_URL, context, mapperUtil.getObjectToByte(reHoldRequest.toString()), SOS_APP_LAYER, RE_HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return ReHoldResponseOuter.ReHoldResponse.newBuilder()
                    .setHoldStatus(HoldStatusOuterClass.HoldStatus.FLIGHT_NOT_AVAILABLE)
                    .build();
        }
    }

    @Override
    public ReHoldResponse reHoldJourneyWithJourneyId(String journeyId, Context context) {
        final long startTime = System.currentTimeMillis();
        try {
            log.info("performing re-hold with journey id: {}", journeyId);
            final JourneyContext journeyContext = journeyContextRepository.fetch(journeyId);
            final FlightSolutionDTO flightSolutionDTO = journeyContext.getFlightSolutionDTO();
            final BookingInformation bookingInformation = journeyContext.getBookingInformation();
            final String solutionId = flightSolutionDTO.getRequestedFareFamilyDTO().getFareSolutionId();
            final String credentialKey = flightSolutionDTO.getSolutionMetaInfoDTO().getSupplierInfoDTO().getCredentialKey();
            final String comboFbc = flightSolutionDTO.getRequestedFareFamilyDTO().getComboFbc();
            final FareMatchStrategy fareMatchStrategy = journeyContext.getFareMatchStrategy();
            final ReHoldRequest reHoldDomainRequest = new ReHoldRequest(bookingInformation, journeyId, credentialKey, flightSolutionDTO, solutionId, context, comboFbc, fareMatchStrategy);
            return doReHold(reHoldDomainRequest, startTime, bookingInformation, context, journeyContext);
        } catch (Exception ex) {
            log.error("Exception occurred while doing re-hold with journey id: {}", ex.getMessage());
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareFailedReHoldWithJourneyIdParams(journeyId, startTime, context, ex.getMessage()));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(ex), HttpStatus.INTERNAL_SERVER_ERROR_500,
                    RE_HOLD_URL, context, mapperUtil.getObjectToByte(journeyId), SOS_APP_LAYER, RE_HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return ReHoldResponse.builder()
                    .status(HoldStatus.FLIGHT_NOT_AVAILABLE)
                    .build();
        }
    }

    @Override
    public ReleaseHoldResponse releaseHoldJourney(ReleaseHoldRequest releaseHoldRequest) {
        ReleaseHoldDomainResponse releaseHoldDomainResponse = flightHoldDomainService.releaseHold(ReleaseHoldDomainRequest.builder()
                .journeyId(releaseHoldRequest.getJourneyId())
                .credentialKey(releaseHoldRequest.getCredentialKey())
                .supplier(releaseHoldRequest.getSupplier())
                .build());
        ReleaseHoldStatus status = releaseHoldDomainResponse.getStatus().equals(ReleaseHoldDomainStatus.SUCCESS) ? ReleaseHoldStatus.SUCCESS : ReleaseHoldStatus.FAILURE;
        return ReleaseHoldResponse.builder()
                .journeyId(releaseHoldDomainResponse.getJourneyId())
                .credentialKey(releaseHoldDomainResponse.getCredentialKey())
                .status(status)
                .build();
    }

    @Override
    public JourneyContext getJourneyContext(String journeyId) {
        return journeyContextRepository.fetch(journeyId);
    }

    @Override
    public void updateJourneyContext(JourneyContext journeyContext) {
        journeyContextRepository.upsert(journeyContext.getJourneyId(), journeyContext);
    }

    private FareMatchStrategy getFareMatchStrategy(HoldRequestOuter.HoldRequest holdRequest) {
        return holdRequest.getFareMatchStrategy().equals(com.cleartrip.supplier.inventory.protos.FareMatchStrategy.UNRECOGNIZED) ||
                holdRequest.getFareMatchStrategy().equals(com.cleartrip.supplier.inventory.protos.FareMatchStrategy.UNKNOWN_FARE_MATCH_STRATEGY) ?
                FareMatchStrategy.CHEAPEST :
                FareMatchStrategy.valueOf(holdRequest.getFareMatchStrategy().name());
    }

    @Override
    public HoldResponseApi holdAndCreateJourneyNonProto(final HoldRequestApi holdRequest, final Context context) {
        final long startTime = System.currentTimeMillis();
        try{
            log.info("itn id: {} performing holdJson", context.getItineraryId());
            final BookingInformation bookingInformation = bookingInformationProtoMapper.toBookingInformation(holdRequest.getBookingInformation());
            final HoldRequest holdDomainRequest = new HoldRequest(bookingInformation, holdRequest.getSolutionId(), holdRequest.getComboFbc(), context, FareMatchStrategy.valueOf(holdRequest.getFareMatchStrategy().name()), holdRequest.getSource(), holdRequest.getAction(), holdRequest.getSisSearchRequest(), holdRequest.getFareContext());
            final HoldResponse holdResponse = flightHoldDomainService.holdV2(holdDomainRequest);
            Map<String, String> additionalProperties = new HashMap<>();
            if (!HoldStatus.FLIGHT_NOT_AVAILABLE.equals(holdResponse.getStatus())) {
                additionalProperties = prepareAdditionalProperties(holdResponse.getFlightSolutionDTO());
                holdResponse.getAdditionalProperties().putAll(additionalProperties);
                final JourneyContext journeyContext = journeyContextFactory.createHoldJourneyContext(bookingInformation, FareMatchStrategy.valueOf(holdRequest.getFareMatchStrategy().name()), holdResponse, holdRequest);
                journeyContextRepository.insert(journeyContext);
                persistInDBStore(journeyContext, holdResponse.getStatus());
            }
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareSuccessHoldParams(holdDomainRequest, holdResponse, startTime, context, HOLD_TASK_NAME));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(holdResponse), getStatusCode(holdResponse.getStatus()),
                    HOLD_URL, context, mapperUtil.getObjectToByte(holdDomainRequest), SOS_APP_LAYER, HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return holdResponseMapper.toHoldResponseProtoV2(holdResponse, additionalProperties);
        }catch (Exception ex){
            log.error("Exception occurred while holdJson", ex);
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareFailureHoldParams(holdRequest, context, startTime, ex.getMessage()));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(ex), HttpStatus.INTERNAL_SERVER_ERROR_500,
                    HOLD_URL, context, mapperUtil.getObjectToByte(holdRequest.toString()), SOS_APP_LAYER, HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return HoldResponseApi.builder()
                    .status(json.response.hold.HoldStatus.FLIGHT_NOT_AVAILABLE)
                    .build();
        }
    }

    @Override
    public HoldResponse holdFareForSFFBook(SffHoldRequest sffHoldRequest) {
        final long startTime = System.currentTimeMillis();
        Context context = sffHoldRequest.getContext();
        try {
            log.info("performing hold for sff with journey id :{} and solutionId :{}", sffHoldRequest.getJourneyId(), sffHoldRequest.getSolutionId());
            final JourneyContext journeyContext = journeyContextRepository.fetch(sffHoldRequest.getJourneyId());

            final HoldRequest holdDomainRequest = sffHoldRequestAdapter.getHoldDomainRequest(sffHoldRequest, journeyContext);
            final HoldResponse holdResponse = flightHoldDomainService.hold(holdDomainRequest);
            if (!HoldStatus.FLIGHT_NOT_AVAILABLE.equals(holdResponse.getStatus())) {
                final JourneyContext newJourneyContext = journeyContextFactory.createHoldJourneyContext(holdDomainRequest.getBookingInformation(), FareMatchStrategy.EXACT, holdResponse, null);
                journeyContextRepository.insert(newJourneyContext);
                persistInDBStore(journeyContext, holdResponse.getStatus());
                final Map<String, String> additionalProperties = prepareAdditionalProperties(holdResponse.getFlightSolutionDTO());
                holdResponse.getAdditionalProperties().putAll(additionalProperties);
            }
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareSuccessHoldParams(holdDomainRequest, holdResponse, startTime, context, RENEW_HOLD));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(holdResponse), getStatusCode(holdResponse.getStatus()),
                    "hold/sffBook", context, mapperUtil.getObjectToByte(holdDomainRequest), SOS_APP_LAYER, RENEW_HOLD, ApiStatsDto.HttpMethod.POST);
            return holdResponse;
        } catch (Exception ex) {
            log.error("Exception occurred while performing renew hold with journey id: {}", ex.getMessage());
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareFailureRenewHoldParams(sffHoldRequest.getJourneyId(), context, startTime, ex.getMessage(), SOS_APP_LAYER, RENEW_HOLD));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(ex), HttpStatus.INTERNAL_SERVER_ERROR_500,
                    "hold/sffBook", context, mapperUtil.getObjectToByte(sffHoldRequest.getJourneyId()), SOS_APP_LAYER, HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return HoldResponse.getFNAResponse();
        }
    }

    public HoldResponse renewHoldWithJourneyId(final String journeyId, final Context context) {
        final long startTime = System.currentTimeMillis();
        try {
            log.info("performing renew hold with journey id :{}", journeyId);
            final JourneyContext journeyContext = journeyContextRepository.fetch(journeyId);
            final BookingInformation bookingInformation = journeyContext.getBookingInformation();
            final FlightSolutionDTO flightSolutionDTO = journeyContext.getFlightSolutionDTO();
            final FareMatchStrategy fareMatchStrategy = journeyContext.getFareMatchStrategy();
            final String solutionId = flightSolutionDTO.getRequestedFareFamilyDTO().getFareSolutionId();
            final String comboFbc = flightSolutionDTO.getRequestedFareFamilyDTO().getComboFbc();
            final HoldRequest holdDomainRequest = new HoldRequest(bookingInformation, solutionId, comboFbc, context, fareMatchStrategy);
            final HoldResponse holdResponse = flightHoldDomainService.hold(holdDomainRequest);
            if (!HoldStatus.FLIGHT_NOT_AVAILABLE.equals(holdResponse.getStatus())) {
                final JourneyContext newJourneyContext = journeyContextFactory.createHoldJourneyContext(bookingInformation, fareMatchStrategy, holdResponse, null);
                journeyContextRepository.insert(newJourneyContext);
                persistInDBStore(journeyContext, holdResponse.getStatus());
                final Map<String, String> additionalProperties = prepareAdditionalProperties(holdResponse.getFlightSolutionDTO());
                holdResponse.getAdditionalProperties().putAll(additionalProperties);
            }
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareSuccessHoldParams(holdDomainRequest, holdResponse, startTime, context, RENEW_HOLD));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(holdResponse), getStatusCode(holdResponse.getStatus()),
                    "renew/hold/journeyId", context, mapperUtil.getObjectToByte(holdDomainRequest), SOS_APP_LAYER, RENEW_HOLD, ApiStatsDto.HttpMethod.POST);
            return holdResponse;
        } catch (Exception ex) {
            log.error("Exception occurred while performing renew hold with journey id: {}", ex.getMessage());
            newRelicHelper.pushToNewRelic(NewRelicUtil.prepareFailureRenewHoldParams(journeyId, context, startTime, ex.getMessage(), SOS_APP_LAYER, RENEW_HOLD));
            statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(ex), HttpStatus.INTERNAL_SERVER_ERROR_500,
                    "renew/hold/journeyId", context, mapperUtil.getObjectToByte(journeyId), SOS_APP_LAYER, HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
            return HoldResponse.getFNAResponse();
        }
    }

    private String getBookPcc(String credentialId, String supplier) {
        final Optional<String> agentName = credentialService.getAgentName(supplier, credentialId);
        if (agentName.isEmpty()) {
            log.error("Not Found agent name for supplier: {} and credential Id: {}", supplier, credentialId);
        }
        return agentName.orElse(null);
    }

    private ReHoldResponse doReHold(ReHoldRequest reHoldDomainRequest, long startTime, BookingInformation bookingInformation, Context context, JourneyContext fetchedJourneyContext) {
        final ReHoldResponse reHoldResponse = flightHoldDomainService.reHold(reHoldDomainRequest);
        newRelicHelper.pushToNewRelic(NewRelicUtil.prepareReHoldParams(reHoldDomainRequest, reHoldResponse, startTime, context));
        statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(reHoldResponse), getStatusCode(reHoldResponse.getStatus()),
                RE_HOLD_URL, context, mapperUtil.getObjectToByte(reHoldDomainRequest), SOS_APP_LAYER, RE_HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
        if (!HoldStatus.FLIGHT_NOT_AVAILABLE.equals(reHoldResponse.getStatus())) {
            Map<String, String> additionalProperties = prepareAdditionalProperties(reHoldResponse.getFlightSolutionDTO());
            reHoldResponse.getAdditionalProperties().putAll(additionalProperties);
            final JourneyContext journeyContext = journeyContextFactory.createReHoldJourneyContext(bookingInformation, reHoldDomainRequest.getFareMatchStrategy(), reHoldResponse, fetchedJourneyContext);
            journeyContextRepository.upsert(journeyContext.getJourneyId(), journeyContext);
            persistInDBStore(journeyContext, reHoldResponse.getStatus());
        }
        return reHoldResponse;
    }

    private Map<String, String> prepareAdditionalProperties(FlightSolutionDTO flightSolutionDTO) {
        final SupplierInfoDTO supplierInfoDTO = flightSolutionDTO.getRequestedFareFamilyDTO().getFareMetaInfoDTO().getSupplierInfoDTO();
        try {
            Map<String, String> additionalProperties = new HashMap<>();
            final String supplier = supplierInfoDTO.getSupplier();
            final String credentialKey = supplierInfoDTO.getCredentialKey();
            final String bookPcc = getBookPcc(credentialKey, supplier);
            additionalProperties.put(SUPPLIER_MARKER, supplier);
            additionalProperties.put(AGENT_NAME, bookPcc);
            return additionalProperties;
        } catch (Exception ex) {
            log.error("Exception occurred while retrieving book-pcc for credential id: {} and supplier is: {} and exception is: {}", supplierInfoDTO.getCredentialKey(), supplierInfoDTO.getSupplier(), ex.getMessage(), ex);
            //TODO:SHUBHAM Understand why book-pcc is needed this much, and un-comment it
            //throw new RuntimeException("Exception occurred while retrieving book-pcc for credential id: " + supplierInfoDTO.getCredentialKey() + " and supplier is: " + supplierInfoDTO.getSupplier() + " and exception is: " + ex.getMessage());
        }
        return Collections.emptyMap();
    }

    @Override
    public HoldResponse holdAndCreateJourneyTest(final HoldRequest holdRequest, final Context context) {
        final long startTime = System.currentTimeMillis();
        final BookingInformation bookingInformation = holdRequest.getBookingInformation();
        final HoldRequest holdDomainRequest = new HoldRequest(bookingInformation, holdRequest.getSolutionId(), holdRequest.getComboFbc(), context, holdRequest.getFareMatchStrategy(), holdRequest.getSource(), holdRequest.getAction());
        final HoldResponse holdResponse = flightHoldDomainService.hold(holdDomainRequest);
        newRelicHelper.pushToNewRelic(NewRelicUtil.prepareSuccessHoldParams(holdDomainRequest, holdResponse, startTime, context, HOLD_TASK_NAME));
        statsHelper.pushToStats(startTime, System.currentTimeMillis(), mapperUtil.getObjectToByte(holdResponse), getStatusCode(holdResponse.getStatus()),
                HOLD_URL, context, mapperUtil.getObjectToByte(holdDomainRequest), SOS_APP_LAYER, HOLD_TASK_NAME, ApiStatsDto.HttpMethod.POST);
        final JourneyContext journeyContext = journeyContextFactory.createHoldJourneyContext(bookingInformation, holdRequest.getFareMatchStrategy(), holdResponse, null);
        journeyContextRepository.insert(journeyContext);
        persistInDBStore(journeyContext, holdResponse.getStatus());
        return holdResponse;
    }

}
