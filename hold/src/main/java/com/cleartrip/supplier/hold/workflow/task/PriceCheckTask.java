package com.cleartrip.supplier.hold.workflow.task;

import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.supplier.hold.domain.value.hold.AncillaryHoldStatus;
import com.cleartrip.supplier.hold.domain.value.hold.PassengerWiseHoldInformation;
import com.cleartrip.supplier.hold.domain.value.hold.enums.HoldStatus;
import com.cleartrip.supplier.hold.monitoring.NewRelicUtil;
import com.cleartrip.supplier.hold.workflow.enums.PriceCheckDecisionCase;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.utility.workflow.design.Task;
import com.google.inject.Inject;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
public class PriceCheckTask implements Task<PriceCheckTask.Input, PriceCheckDecisionCase> {
    
    private static final double ZERO = 0.0;
    private final NewRelicHelper newRelicHelper;

    @Inject
    public PriceCheckTask(final NewRelicHelper newRelicHelper) {
        this.newRelicHelper = newRelicHelper;
    }

    @Override
    public PriceCheckDecisionCase run(PriceCheckTask.Input input) {
        log.info("checking price change or not");
        final double totalFareFromBookedPromise = input.getTotalFare();
        final List<PassengerWiseHoldInformation> passengersWiseHoldInformation = input.getPassengerWiseHoldInformationList();
        final double totalFareOfFlight = input.getFlightSolutionDTO().getRequestedFareFamilyDTO().getTotalFareDTO().getNetFare();
        final double failedSmbFare = extractTotalSmbFare(passengersWiseHoldInformation, AncillaryHoldStatus.Status.FAILURE);
        final double successSmbFare = extractTotalSmbFare(passengersWiseHoldInformation, AncillaryHoldStatus.Status.SUCCESS);
        final double totalSmbFare = successSmbFare + failedSmbFare;
        final double totalUpdatedFareWithSmB = totalFareFromBookedPromise + failedSmbFare;
        final double flightFareFromBookedPromise = totalUpdatedFareWithSmB - totalSmbFare;
        log.info("totalFareOfFlight: {}, totalFareFromBookedPromise:{}, failedSmbFare: {}, successSmbFare: {}, totalSmbFare:{}, flightFareFromBookedPromise:{}", totalFareOfFlight, totalFareFromBookedPromise, failedSmbFare, successSmbFare, totalSmbFare, flightFareFromBookedPromise);
        final PriceCheckDecisionCase priceCheckDecisionCase = Math.abs(totalFareOfFlight - flightFareFromBookedPromise) < 1 ? PriceCheckDecisionCase.NO_CHANGE : PriceCheckDecisionCase.PRICE_CHANGE;
        pushToNR(totalFareFromBookedPromise, totalFareOfFlight, failedSmbFare, successSmbFare, totalSmbFare, totalUpdatedFareWithSmB, flightFareFromBookedPromise, priceCheckDecisionCase, input.getItnId());
        return priceCheckDecisionCase;
    }

    private void pushToNR(double totalFareFromBookedPromise, double totalFareOfFlight, double failedSmbFare, double successSmbFare, double totalSmbFare, double totalUpdatedFareWithSmB, double flightFareFromBookedPromise, PriceCheckDecisionCase priceCheckDecisionCase, String itnId) {
        newRelicHelper.pushToNewRelic(NewRelicUtil.preparePriceCheckTaskParams(totalFareFromBookedPromise, totalFareOfFlight, failedSmbFare, successSmbFare, totalSmbFare, totalUpdatedFareWithSmB, flightFareFromBookedPromise, priceCheckDecisionCase, itnId));
    }

    @Getter
    public static class Input {
        private final String itnId;
        private final double totalFare;
        private final HoldStatus holdStatus;
        private final FlightSolutionDTO flightSolutionDTO;
        private final List<PassengerWiseHoldInformation> passengerWiseHoldInformationList;

        @Builder
        public Input(String itnId, HoldStatus holdStatus, double totalFare, FlightSolutionDTO flightSolutionDTO,
                     List<PassengerWiseHoldInformation> passengerWiseHoldInformationList) {
            this.itnId = itnId;
            this.holdStatus = holdStatus;
            this.totalFare = totalFare;
            this.flightSolutionDTO = flightSolutionDTO;
            this.passengerWiseHoldInformationList = passengerWiseHoldInformationList;
        }
    }

    private double extractTotalSmbFare(List<PassengerWiseHoldInformation> passengersWiseHoldInformation, AncillaryHoldStatus.Status status) {
        if (CollectionUtils.isEmpty(passengersWiseHoldInformation)) {
            return ZERO;
        }
        return passengersWiseHoldInformation.stream()
                .filter(Objects::nonNull)
                .mapToDouble(passengerWiseHoldInformation -> passengerWiseHoldInformation.getAncillaryAmount(status))
                .sum();
    }

}
