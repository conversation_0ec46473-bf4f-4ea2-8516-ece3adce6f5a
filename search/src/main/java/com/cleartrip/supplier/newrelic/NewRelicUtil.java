package com.cleartrip.supplier.newrelic;

import com.cleartrip.air.sms.api.request.search.PaxFareType;
import com.cleartrip.air.sms.api.v2.book.AccountCode;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.inventory.protos.v1.GetFlightSolutionRequestV2;
import com.cleartrip.supplier.search.constant.ThreadContextParams;
import com.cleartrip.supplier.search.data.AvailabilityTaskRequest;
import com.cleartrip.supplier.search.data.Credential;
import com.cleartrip.supplier.search.enums.BaggageSource;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.models.*;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.SelectedSolution;
import com.cleartrip.supplier.search.models.application.FetchFlightSolutionReq;
import com.cleartrip.supplier.search.models.application.SelectedSearchRequest;
import com.cleartrip.supplier.search.models.application.SelectedSolutionInformation;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import com.cleartrip.supplier.search.util.CommonUtil;
import com.cleartrip.supplier.search.utils.EventUtil;
import com.cleartrip.supplier.search.workflow.tasks.execution.TaskJourneyType;
import com.google.common.collect.ImmutableList;
import com.google.common.eventbus.EventBus;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.net.InetAddress;
import java.util.*;
import java.util.stream.Collectors;

import static com.cleartrip.supplier.newrelic.SisNewRelicParams.*;
import static com.cleartrip.supplier.search.constant.ThreadContextParams.IS_RETRY;

@Getter
@Log4j2
public class NewRelicUtil {

    private static NewRelicEventTransmitter newRelicEventTransmitter;
    private final SearchConfigContainer properties;
    private static EventBus newRelicEventBus;
    private static final String baggageEventTable = "SIS_BAGGAGE_EVENT";
    private static final String cacheEventTable = "SIS_CACHE_EVENT";
    private static final String flightEventTable = "SIS_FLIGHT_SEARCH_EVENT";
    private static final String flightSearchSS1 = "SIS_FLIGHT_SEARCH_SS1_EVENT";
    private static final String flightTaskEventTable = "SIS_FLIGHT_SEARCH_TASK_EVENT";
    private static final String clearCacheTable = "SIS_CACHE_RPC";
    private static final String flightSearchTimeEventTable = "SIS_FLIGHT_SEARCH_TIME_EVENT";
    private static final String nodeErrorTable = "SIS_NODE_ERROR_EVENT";
    private static final String companyCacheTable = "SIS_COMPANY_TINY_CACHE";
    private static final String sisProductClassData = "SIS_PRODUCT_CLASS_INFO";
    private static final String regularFareGroupMissingTable = "SIS_REGULAR_FAREGROUP_MESSING";
    private static final String productClassTable = "SIS_PRODUCT_CLASS_AND_FARE_TYPE";
    private static final String selectedSearchTable = "SELECTED_SEARCH";
    private static final String singleSearchTable = "SINGLE_SOLUTION_SEARCH";
    private static final String amendSearchTable = "AMEND_SEARCH";
    private static final String asyncTable = "ASYNC_TABLE";
    private static final String sisRuleEngineException = "SIS_RULE_ENGINE_EXCEPTION";
    private static final List<FareGroupInfo> PTC_FARE_GROUP_INFO = ImmutableList.of(FareGroupInfo.STUDENT, FareGroupInfo.ARMY, FareGroupInfo.SENIOR_CITIZEN);
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(NewRelicUtil.class);

    @Inject
    public NewRelicUtil(NewRelicEventTransmitter newRelicEventTransmitter, SearchConfigContainer properties, @Named("NewRelicEventBus") EventBus eventBus) {
        this.newRelicEventTransmitter = newRelicEventTransmitter;
        this.properties = properties;
        this.newRelicEventBus = eventBus;
    }

    public static void addEventToNewRelic(NewRelicEvent newRelicEvent) {
        try {
            newRelicEvent.getEventMap().put(HOST_NAME.name(), InetAddress.getLocalHost().getHostName());
            newRelicEventBus.post(newRelicEvent);
        } catch (Exception e) {
            log.error("unable to add event in EventBus for eventName: {}", newRelicEvent.getEventMap().get("eventType"), e);
        }
    }

    public static void sendRegularFareMissingEvent(String solutionId) {
        Map<String, Object> newRelicData = new HashMap<>() {
            {
                put(SOLUTION_ID.name(), solutionId);
                put(EVENT_TIME.name(), new Date(System.currentTimeMillis()));
            }
        };
        addEventToNewRelic(new NewRelicEvent(regularFareGroupMissingTable, newRelicData));
    }

    public static void sendCompanyCacheDataToNewRelic(String companyId, String key, String fareCategory, String value) {
        Map<String, Object> newRelicData = new HashMap<>() {
            {
                put(COMPANY_ID.name(), companyId);
                put(CACHE_KEY.name(), key);
                put(FARE_CATEGORY.name(), fareCategory);
                put(VALUE.name(), value);
            }
        };
        addEventToNewRelic(new NewRelicEvent(companyCacheTable, newRelicData));
    }

    public static void sendProductClassInfo(String requestId, String credentialKey, String supplier,
                                            String productClass, String fareType, String fareCategory, String source, String promoCode, String accountCode, String companyId,
                                            String journeyType) {
        Map<String, Object> newRelicData = new HashMap<>() {{
            put(REQUEST_ID.name(), requestId);
            put(PRODUCT_CLASS.name(), productClass);
            put(COMPANY_ID.name(), companyId);
            put(CREDENTIAL_KEY.name(), credentialKey);
            put(ACCOUNT_CODE.name(), accountCode);
            put(PROMO_CODE.name(), promoCode);
            put(SUPPLIER.name(), supplier);
            put(FARE_TYPE.name(), fareType);
            put(FARE_CATEGORY.name(), fareCategory);
            put(SOURCE_TYPE.name(), source);
            put(JOURNEY_TYPE.name(), journeyType);
        }};
        addEventToNewRelic(new NewRelicEvent(sisProductClassData, newRelicData));
    }

    public static void sendSetCacheEvent(String eventName, String key, long responseTime, @Value("SINGLE") String operationType, long expiryTime, long eventTime) {
        sendSetCacheEvent(eventName, key, responseTime, expiryTime, eventTime, "HIT");
    }

    public static void sendSetCacheEvent(String eventName, String key, long responseTime, long expiryTime, long eventTime, String status) {
        try {
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(REQUEST_ID.name(), ThreadContext.get(ThreadContextParams.REQUEST_ID));
                put(CACHE_KEY.name(), key);
                put(EVENT_NAME.toString(), eventName);
                put(STATUS.name(), status);
                put(EXPIRY_TIME.name(), new Date(expiryTime));
                put(EVENT_TIME.toString(), new Date(eventTime));
                put(RESPONSE_TIME.name(), responseTime);
            }};
            addEventToNewRelic(new NewRelicEvent(cacheEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendSetCacheEvent search event {}", ex);

        }
    }

    public static void sendGetCacheEvent(String eventName, String key, boolean isMiss, long responseTime, String operationType, long eventTime) {
        try {
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(REQUEST_ID.name(), ThreadContext.get(ThreadContextParams.REQUEST_ID));
                put(CACHE_KEY.name(), key);
                put(EVENT_NAME.name(), eventName);
                put(STATUS.name(), isMiss ? "MISS" : "HIT");
                put(EVENT_TIME.name(), eventTime);
                put(RESPONSE_TIME.name(), responseTime);
            }};

            // Add EventUtil parameters if present
            EventUtil.getEventParam(EventUtil.PARTNER_ID).ifPresent(value -> newReStringStringMap.put(EventUtil.PARTNER_ID, value));
            EventUtil.getEventParam(EventUtil.SELLING_COUNTRY).ifPresent(value -> newReStringStringMap.put(EventUtil.SELLING_COUNTRY, value));
            EventUtil.getEventParam(EventUtil.SUPPLIER).ifPresent(value -> newReStringStringMap.put(EventUtil.SUPPLIER, value));
            EventUtil.getEventParam(EventUtil.CREDENTIAL_KEY).ifPresent(value -> newReStringStringMap.put(EventUtil.CREDENTIAL_KEY, value));
            EventUtil.getEventParam(EventUtil.SECTOR).ifPresent(value -> newReStringStringMap.put(EventUtil.SECTOR, value));
            EventUtil.getEventParam(EventUtil.REQ_ID).ifPresent(value -> newReStringStringMap.put(EventUtil.REQ_ID, value));
            EventUtil.getEventParam(EventUtil.DX).ifPresent(value -> newReStringStringMap.put(EventUtil.DX, value));

            addEventToNewRelic(new NewRelicEvent(cacheEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendGetCacheEvent search event {}", ex);
        }
    }

    public static void sendBaggageEvent(String requestId, BaggageSource baggageSource, BaggageCriteria baggageCriteria,
                                        SolutionInfo solutionInfo, String comboFbc, String cacheKey, SolutionBaggageResponse solutionBaggageResponse,
                                        long responseTime, String error, long eventTime) {
        try {
            List<FlightSegment> segmentList = solutionInfo.getFlights(0).getSegmentsList();
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(REQUEST_ID.name(), requestId);
                put((CACHE_KEY.name()), cacheKey);
                put(SOURCE.name(), baggageSource.toString());
                put(SUPPLIER.name(), solutionInfo.getSupplierInfo().getSupplier());
                put(CRED_KEY.name(), solutionInfo.getSupplierInfo().getCredentialKey());
                put(INTL.name(), baggageCriteria.getIsInternational());
                put(DEPARTURE.name(), segmentList.get(0).getDepartFromStop().getAirport());
                put(ARRIVAL.name(), segmentList.get(segmentList.size() - 1).getArriveAtStop().getAirport());
                put(RESPONSE_TIME.name(), responseTime);
                put(EVENT_NAME.toString(), "BaggageSearch");
                put(COMBO_FBC.name(), comboFbc);
                put(EVENT_TIME.name(), eventTime);
                put(STATUS.name()
                        , (solutionBaggageResponse == null || CollectionUtils.isEmpty(solutionBaggageResponse.getFltBaggageDetailsList())) ? "MISS" : "HIT");
                if (!StringUtils.isEmpty(error))
                    put(ERROR.name(), error);
            }};
            addEventToNewRelic(new NewRelicEvent(baggageEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendBaggageEvent search event {}", ex);
        }
    }

    public static void sendBaggageErrorEvent(BaggageRequest request, String error, long eventTime) {
        try {
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(EVENT_NAME.toString(), "BaggageSearch");
                put(REQUEST_ID.name(), request.getRequestId());
                put(SUPPLIER.name(), request.getSolutions(0).getSupplierInfo().getSupplier());
                put(CRED_KEY.name(), request.getSolutions(0).getSupplierInfo().getCredentialKey());
                put(INTL.name(), request.getFreeBaggageInfoCriteria().getIsInternational());
                put(STATUS.name(), "MISS");
                put(ERROR.name(), error);
                put(EVENT_TIME.name(), eventTime);

            }};
            addEventToNewRelic(new NewRelicEvent(baggageEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendBaggageErrorEvent search event {}", ex);
        }
    }

    public static void sendFlightSearchEvent(String eventName, SearchRequest request, int taskCount,
                                             int finalSolutionCount, int duplicateSolutionCount, long responseTime,
                                             String error, int searchAttemptCount, long eventTime, String fingerprint) {
        try {
            CustomerSearchCriteria customerSearchCriteria = request.getFlightSearchCriteria().getCustomerSearchCriteria();
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(EVENT_NAME.name(), eventName);
                put(REQUEST_ID.name(), request.getSearchId());
                put(IS_SFF_REQUEST.name(), request.getFlightSearchCriteria().getSearchOptions().getFareCategoryList().stream().anyMatch(x -> FareCategoryType.SFF == x.getType()));
                put(IS_PTC_REQUEST.name(), request.getFlightSearchCriteria().getSearchOptions().getFareCategoryList().stream().anyMatch(x -> isPTCRequest(x.getSubCategoryList())));
                put(STRATEGY.name(), request.getFlightSearchCriteria().getSearchOptions().getSearchStrategy().name());
                put(IS_RETRY_SEARCH.name(), ThreadContext.get(IS_RETRY));
                put(TASK_COUNT.name(), taskCount);
                put(SELLING_COUNTRY.name(), request.getFlightSearchCriteria().getSellingCountry());
                put(ADT.name(), customerSearchCriteria.getPaxCount().getAdults());
                put(CHD.name(), customerSearchCriteria.getPaxCount().getChildren());
                put(INF.name(), customerSearchCriteria.getPaxCount().getInfants());
                put(INTL.name(), request.getFlightSearchCriteria().getIsIntl());
                put(USER_ID.name(), request.getCustomerInfo().getUserId());
                put(COMPANY_ID.name(), request.getCustomerInfo().getCompanyId());
                put(SOURCE_TYPE.name(), request.getCustomerInfo().getSourceType().toString());
                put(CABIN_TYPE.name(), customerSearchCriteria.getCabinType().toString());
                put(PREFERRED_AIRLINE.name(), CollectionUtils.isNotEmpty(customerSearchCriteria.getPreferredAirlinesList())
                        ? String.join(",", customerSearchCriteria.getPreferredAirlinesList()) : "");
                put(IS_RADIAL_SEARCH.name(), request.getFlightSearchCriteria().getSearchOptions().getIsRadialSearch());
                put(TRIP_TYPE.name(), CommonUtil.isRoundTrip(customerSearchCriteria.getBaseCriteriaList()) ? "ROUND_TRIP"
                        : CommonUtil.isMultiCity(customerSearchCriteria.getBaseCriteriaList()) ? "MULTI_CITY_TRIP"
                        : "ONE_WAY");
                put(RESPONSE_TIME.name(), responseTime);
                if (StringUtils.isEmpty(error)) {
                    put(SOLUTION_COUNT.name(), finalSolutionCount);
                    put(DUPLICATE_SOLUTION_COUNT.name(), duplicateSolutionCount);
                } else
                    put(ERROR.name(), error);
                put(SEARCH_ATTEMPT_COUNT.name(), searchAttemptCount);
                put(EVENT_TIME.name(), eventTime);
                put(DX.name(), getDxValue(customerSearchCriteria.getBaseCriteriaList().get(0).getDate(), eventTime));
                put(FINGERPRINT.name(), fingerprint);
            }};
            setOriginDestination(newReStringStringMap, customerSearchCriteria.getBaseCriteriaList());
            addEventToNewRelic(new NewRelicEvent(flightEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchEvent search event {}", ex);
        }

    }

    public static void sendFlightSearchSMSTaskEvent(int taskNumber, int attemptCount, AvailabilityTaskRequest taskRequest, boolean isBG, String expiryType, boolean isTaskCompleted,
                                                    boolean isConnectorCalled, Optional<String[]> fareType,
                                                    Optional<String[]> productClass, int promiseCount, int solutionCount,
                                                    long responseTime, String error, int searchAttemptCount, long eventTime) {
        try {
            Map<String, Object> newReStringStringMap = getFlightTaskMap(taskRequest, taskNumber, attemptCount, expiryType, promiseCount, solutionCount, isBG
                    , responseTime, isConnectorCalled, isTaskCompleted, error, searchAttemptCount, eventTime);
            newReStringStringMap.put(FARE_TYPE.name(), fareType.isPresent()
                    ? ConvertListToString(new ArrayList<>() {{
                addAll(Arrays.stream(fareType.get()).collect(Collectors.toList()));
            }}) : "");
            newReStringStringMap.put(PRODUCT_CLASS.name(), productClass.isPresent()
                    ? ConvertListToString(new ArrayList<>() {{
                addAll(Arrays.stream(productClass.get()).collect(Collectors.toList()));
            }}) : "");

            addEventToNewRelic(new NewRelicEvent(flightTaskEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchTaskEvent search event {}", ex);
        }
    }

    public static void sendFlightSearchSMSTaskEvent(NodeType nodeType, String criteriaId, String callType, int attemptCount, WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest, boolean isBG, String expiryType, boolean isTaskCompleted,
                                                    boolean isConnectorCalled, Optional<String[]> fareType, Optional<String[]> productClass, Optional<List<AccountCode>> accountCodes, Optional<String> promoCodes, List<PaxFareType> paxFareTypes,
                                                    FareCategoryType fareCategoryType, boolean isIntl, int promiseCount, int onwardSolCount, int returnSolCount, int splrtSolCount,
                                                    long responseTime, String error, int searchAttemptCount, long eventTime) {
        try {
            Map<String, Object> newReStringStringMap = getFlightTaskMap(nodeType, workFlowTaskAvailSearchRequest.getCustomerSearchCriteriaDTO(), workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO(),
                    workFlowTaskAvailSearchRequest.getBaseSearchCriteria().get(0), workFlowTaskAvailSearchRequest.getCredential(), workFlowTaskAvailSearchRequest.getCompanyInfoDTO(),
                    workFlowTaskAvailSearchRequest.getSupplierCarrierPair().getSupplier(), workFlowTaskAvailSearchRequest.getSearchId(), workFlowTaskAvailSearchRequest.getSearchType().name(), criteriaId, attemptCount, expiryType,
                    onwardSolCount + returnSolCount + splrtSolCount, isBG, responseTime, isConnectorCalled, isTaskCompleted, error, searchAttemptCount, eventTime, null);

            newReStringStringMap.put(SisNewRelicParams.SMS_CALL_TYPE.name(), callType);
            newReStringStringMap.put(SisNewRelicParams.PROMISE_COUNT.name(), promiseCount);
            newReStringStringMap.put(SisNewRelicParams.ONEWAY_SOLUTION_COUNT.name(), onwardSolCount);
            newReStringStringMap.put(SisNewRelicParams.RETURN_SOLUTION_COUNT.name(), returnSolCount);
            newReStringStringMap.put(SisNewRelicParams.ROUNDTRIP_SOLUTION_COUNT.name(), splrtSolCount);
            newReStringStringMap.put(FARE_CATEGORY.name(), fareCategoryType);
            newReStringStringMap.put(INTL.name(), isIntl);
            newReStringStringMap.put(SisNewRelicParams.FARE_TYPE.name(), fareType.isPresent()
                    ? ConvertListToString(new ArrayList<>() {{
                addAll(Arrays.stream(fareType.get()).collect(Collectors.toList()));
            }}) : "");
            newReStringStringMap.put(PRODUCT_CLASS.name(), productClass.isPresent()
                    ? ConvertListToString(new ArrayList<>() {{
                addAll(Arrays.stream(productClass.get()).collect(Collectors.toList()));
            }}) : "");
            newReStringStringMap.put(ACCOUNT_CODE.name(), accountCodes.isPresent()
                    ? ConvertListToString(new ArrayList<>() {{
                addAll(accountCodes.get().stream().map(accountCode -> accountCode.getCode() + "^" + accountCode.getAirline()).collect(Collectors.toList()));
            }}) : "");
            newReStringStringMap.put(PROMO_CODE.name(), promoCodes.isPresent()
                    ? promoCodes.get() : "");
            newReStringStringMap.put(PAX_FARE_TYPE.name(), ConvertListToString(new ArrayList<>() {{
                addAll(paxFareTypes.stream().map(paxFareType -> paxFareType.name()).collect(Collectors.toList()));
            }}));
            addEventToNewRelic(new NewRelicEvent(flightTaskEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchTaskEvent search event {}", ex);
        }
    }

    public static void sendFlightSearchTaskEvent(int taskNumber, int attemptCount, AvailabilityTaskRequest taskRequest, boolean isBG, String expiryType, boolean isTaskCompleted,
                                                 boolean isConnectorCalled, int promiseCount, int solutionCount, long responseTime, String error, int searchAttemptCount, long eventTime) {
        try {
            Map<String, Object> newReStringStringMap = getFlightTaskMap(taskRequest, taskNumber, attemptCount, expiryType, promiseCount, solutionCount, isBG
                    , responseTime, isConnectorCalled, isTaskCompleted, error, searchAttemptCount, eventTime);
            addEventToNewRelic(new NewRelicEvent(flightTaskEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchTaskEvent search event {}", ex);
        }
    }

    public static void sendFlightSearchTaskEvent(NodeType nodeType, String criteriaId, int attemptCount, WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest, boolean isBG, String expiryType, boolean isTaskCompleted,
                                                 boolean isConnectorCalled, int solutionCount, long responseTime, String error, int searchAttemptCount, long eventTime, String dataStore) {
        try {
            Map<String, Object> newReStringStringMap = getFlightTaskMap(nodeType, workFlowTaskAvailSearchRequest.getCustomerSearchCriteriaDTO(), workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO(),
                    workFlowTaskAvailSearchRequest.getBaseSearchCriteria().get(0), workFlowTaskAvailSearchRequest.getCredential(), workFlowTaskAvailSearchRequest.getCompanyInfoDTO(),
                    workFlowTaskAvailSearchRequest.getSupplierCarrierPair().getSupplier(), workFlowTaskAvailSearchRequest.getSearchId(), workFlowTaskAvailSearchRequest.getSearchType().name(), criteriaId, attemptCount, expiryType, solutionCount, isBG
                    , responseTime, isConnectorCalled, isTaskCompleted, error, searchAttemptCount, eventTime, dataStore);
            addEventToNewRelic(new NewRelicEvent(flightTaskEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchTaskEvent search event {}", ex);
        }
    }

    public static void sendNodeErrorEvent(NodeType nodeType, String searchId, long responseTime, String errorMessage, long eventTime) {
        Map<String, Object> newReStringStringMap = new HashMap<>() {{
            put(REQUEST_ID.name(), searchId);
            put(NODE_TYPE.name(), nodeType.name());
            put(RESPONSE_TIME.name(), responseTime);
            put(EVENT_TIME.name(), eventTime);
            put(ERROR.name(), errorMessage);

        }};
        addEventToNewRelic(new NewRelicEvent(nodeErrorTable, newReStringStringMap));
    }

    public static void sendSelectedSearchNodeEvent(NodeType nodeType, String itnId, String searchId, long responseTime, String errorMessage, long eventTime) {
        Map<String, Object> newReStringStringMap = new HashMap<>();
        newReStringStringMap.put(SisNewRelicParams.REQUEST_ID.name(), searchId);
        newReStringStringMap.put(SisNewRelicParams.NODE_TYPE.name(), nodeType.name());
        newReStringStringMap.put(SisNewRelicParams.RESPONSE_TIME.name(), responseTime);
        newReStringStringMap.put(SisNewRelicParams.EVENT_TIME.name(), eventTime);
        newReStringStringMap.put(SisNewRelicParams.ERROR.name(), errorMessage);
        newReStringStringMap.put(ITINERARY_ID.name(), itnId);
        addEventToNewRelic(new NewRelicEvent(selectedSearchTable, newReStringStringMap));
    }

    public static void sendSelectedSearchEvent(SelectedSearchRequest selectedSearchRequest, long responseTime, long eventTime, Optional<String> error) {
        Map<String, Object> newReStringStringMap = new HashMap<>();
        newReStringStringMap.put(SEARCH_STRATEGY.name(), selectedSearchRequest.getSearchStrategy());
        newReStringStringMap.put(SisNewRelicParams.RESPONSE_TIME.name(), responseTime);
        newReStringStringMap.put(SisNewRelicParams.EVENT_TIME.name(), eventTime);
        error.ifPresent(er -> newReStringStringMap.put(ERROR.name(), er));
        newReStringStringMap.put(ITINERARY_ID.name(), selectedSearchRequest.getItnId());
        addEventToNewRelic(new NewRelicEvent(selectedSearchTable, newReStringStringMap));
    }

    public static void sendSelectedSearchWorkflowEvent(SelectedSolutionInformation selectedSolutionInformation, boolean isSolutionPresent, long responseTime, long eventTime, String itnId, String error) {
        Map<String, Object> newReStringStringMap = new HashMap<>();
        newReStringStringMap.put(SOLUTION_ID.name(), selectedSolutionInformation.getSolutionId());
        newReStringStringMap.put(IS_SOLUTION_PRESENT.name(), isSolutionPresent);
        newReStringStringMap.put(FARE_CATEGORY.name(), selectedSolutionInformation.getFareCategoryType());
        newReStringStringMap.put(SUPPLIER.name(), selectedSolutionInformation.getAirSupplier().name());
        newReStringStringMap.put(CABIN_TYPE.name(), selectedSolutionInformation.getCabinType());
        newReStringStringMap.put(FARE_GROUP_INFO.name(), selectedSolutionInformation.getFareGroupInfo());
        newReStringStringMap.put(FARE_DISPLAY_NAME.name(), selectedSolutionInformation.getFareDisplayName());
        newReStringStringMap.put(SisNewRelicParams.RESPONSE_TIME.name(), responseTime);
        newReStringStringMap.put(SisNewRelicParams.EVENT_TIME.name(), eventTime);
        newReStringStringMap.put(ITINERARY_ID.name(), itnId);
        if(StringUtils.isNotEmpty(error))
            newReStringStringMap.put(ERROR.name(), error);
        addEventToNewRelic(new NewRelicEvent(selectedSearchTable, newReStringStringMap));
    }

    public static void sendSingleSearchNodeEvent(NodeType nodeType, boolean solutionPresent, String searchId, long responseTime, String errorMessage, long eventTime, String itineraryId) {
        Map<String, Object> newReStringStringMap = new HashMap<>();
        newReStringStringMap.put(SOLUTION_ID.name(), searchId);
        newReStringStringMap.put(IS_SOLUTION_PRESENT.name(),solutionPresent);
        newReStringStringMap.put(SisNewRelicParams.NODE_TYPE.name(), nodeType.name());
        newReStringStringMap.put(SisNewRelicParams.RESPONSE_TIME.name(), responseTime);
        newReStringStringMap.put(SisNewRelicParams.EVENT_TIME.name(), eventTime);
        newReStringStringMap.put(ITINERARY_ID.name(), itineraryId);
        newReStringStringMap.put(EVENT_NAME.name(), "SINGLE_SEARCH_NODE_EVENT");
        if(StringUtils.isNotEmpty(errorMessage))
            newReStringStringMap.put(SisNewRelicParams.ERROR.name(), errorMessage);
        addEventToNewRelic(new NewRelicEvent(singleSearchTable, newReStringStringMap));
    }

    public static void sendSingleSearchEvent(GetFlightSolutionRequestV2 getFlightSolutionRequestV2, long eventTime, long responseTime, Optional<String> error) {
        Map<String, Object> newReStringStringMap = new HashMap<>();
        String solutionIDs = getFlightSolutionRequestV2.getSelectedSolutionInformationList().stream().map(com.cleartrip.supplier.inventory.protos.v1.SelectedSolutionInformation::getSolutionId).collect(Collectors.joining(","));
        String comboFBCs = getFlightSolutionRequestV2.getSelectedSolutionInformationList().stream().map(com.cleartrip.supplier.inventory.protos.v1.SelectedSolutionInformation::getComboFareBasisCode).collect(Collectors.joining(","));
        String suppliers = getFlightSolutionRequestV2.getSelectedSolutionInformationList().stream().map(com.cleartrip.supplier.inventory.protos.v1.SelectedSolutionInformation::getAirSupplier).collect(Collectors.joining(","));
        String fareCategories = getFlightSolutionRequestV2.getSelectedSolutionInformationList().stream().map(selectedSolutionInformation -> selectedSolutionInformation.getFareCategoryType().name()).collect(Collectors.joining(","));
        String fareGroupInfoes = getFlightSolutionRequestV2.getSelectedSolutionInformationList().stream().map(selectedSolutionInformation -> selectedSolutionInformation.getFareGroupInfo().name()).collect(Collectors.joining(","));
        String fareDisplayNames = getFlightSolutionRequestV2.getSelectedSolutionInformationList().stream().map(com.cleartrip.supplier.inventory.protos.v1.SelectedSolutionInformation::getFareDisplayName).collect(Collectors.joining(","));

        newReStringStringMap.put(SOLUTION_ID.name(), solutionIDs);
        newReStringStringMap.put(COMBO_FBC.name(), comboFBCs);
        newReStringStringMap.put(FARE_CATEGORY.name(), fareCategories);
        newReStringStringMap.put(FARE_GROUP_INFO.name(), fareGroupInfoes);
        newReStringStringMap.put(SUPPLIER.name(), suppliers);
        newReStringStringMap.put(FARE_DISPLAY_NAME.name(), fareDisplayNames);
        newReStringStringMap.put(ITINERARY_ID.name(), getFlightSolutionRequestV2.getItineraryId());
        newReStringStringMap.put(SisNewRelicParams.RESPONSE_TIME.name(), responseTime);
        newReStringStringMap.put(SisNewRelicParams.EVENT_TIME.name(), eventTime);
        newReStringStringMap.put(SisNewRelicParams.TIME_TAKEN.name(), responseTime - eventTime);
        newReStringStringMap.put(EVENT_NAME.name(), "SINGLE_SEARCH");
        error.ifPresent(er -> newReStringStringMap.put(ERROR.name(), er));
        addEventToNewRelic(new NewRelicEvent(singleSearchTable, newReStringStringMap));
    }

    public static void sendSingleSearchWorkflowEvent(SingleSolutionSearchWorkflowRequest singleSolutionSearchWorkflowRequest, long responseTime, long eventTime, SingleSolutionWorkflowResponse singleSolutionWorkflowResponse) {
        Map<String, Object> newReStringStringMap = new HashMap<>();
        SelectedSolution selectedSolution = singleSolutionSearchWorkflowRequest.getSelectedSolutionInformation();
        newReStringStringMap.put(SOLUTION_ID.name(), selectedSolution.getSolutionId());
        newReStringStringMap.put(SUPPLIER.name(), selectedSolution.getAirSupplier().name());
        newReStringStringMap.put(COMBO_FBC.name(), selectedSolution.getComboFareBasisCode());
        newReStringStringMap.put(FARE_CATEGORY.name(), selectedSolution.getFareCategoryType().name());
        newReStringStringMap.put(FARE_GROUP_INFO.name(), selectedSolution.getFareGroupInfo().name());
        newReStringStringMap.put(FARE_DISPLAY_NAME.name(), selectedSolution.getFareDisplayName());
        newReStringStringMap.put(SUPPLIER.name(), selectedSolution.getAirSupplier().name());
        newReStringStringMap.put(SisNewRelicParams.RESPONSE_TIME.name(), responseTime);
        newReStringStringMap.put(EVENT_NAME.name(), "SINGLE_SEARCH_WORKFLOW_EVENT");
        newReStringStringMap.put(ITINERARY_ID.name(), singleSolutionSearchWorkflowRequest.getItineraryId());
        newReStringStringMap.put(SisNewRelicParams.EVENT_TIME.name(), eventTime);
        if(Objects.nonNull(singleSolutionWorkflowResponse)) {
            newReStringStringMap.put(IS_SINGLE_SOLUTION_CALL_DONE.name(), singleSolutionWorkflowResponse.isSingleSearchCallDone());
            newReStringStringMap.put(IS_SELECTED_SEARCH_CALL_DONE.name(), singleSolutionWorkflowResponse.isSelectedSearchDone());
            newReStringStringMap.put(IS_PRICE_CHANGE.name(), singleSolutionWorkflowResponse.isPriceChange());
            newReStringStringMap.put(NEW_PRICE.name(), singleSolutionWorkflowResponse.getNewPrice());
            newReStringStringMap.put(OLD_PRICE.name(), singleSolutionWorkflowResponse.getOldPrice());
            FlightSolutionDTO flightSolutionDTO = singleSolutionWorkflowResponse.getFlightSolutionDTO();
            if(Objects.nonNull(flightSolutionDTO) && Objects.nonNull(flightSolutionDTO.getFareFamilyDTO())) {
                newReStringStringMap.put(SisNewRelicParams.FARE_LIST_SIZE.name(), flightSolutionDTO.getFareFamilyDTO().size());
            }
        }
        addEventToNewRelic(new NewRelicEvent(singleSearchTable, newReStringStringMap));
    }

    public static void sendSS1Event(String eventName, FetchFlightSolutionReq request, List<FlightSolutionDTO> flightSolutionsResponse, long responseTime, String error, long eventTime) {
        try {
            for (int index = 0; index < request.getSelectedSolution().size(); index++) {
                String solutionId = request.getSelectedSolution().get(index).getSolutionId();
                String comboFbc = request.getSelectedSolution().get(index).getComboFBC();

                Map<String, Object> newReStringStringMap = new HashMap<>() {{
                    put(EVENT_NAME.name(), eventName);
                    put(ITINERARY_ID.name(), request.getItineraryId());
                    put(API_VERSION.name(), "cache");
                    put(SOLUTION_ID.name(), solutionId);
                    put(COMBO_FBC.name(), comboFbc);
                    put(RESPONSE_TIME.name(), responseTime);
                    put(EVENT_TIME.name(), eventTime);
                    if (StringUtils.isNoneBlank(error))
                        put(ERROR.name(), error);
                }};

                if (flightSolutionsResponse != null)
                    newReStringStringMap.put(STATUS.name(), CollectionUtils.isNotEmpty(flightSolutionsResponse.stream()
                                    .filter(x->x.getFareFamilyDTO().stream()
                                            .anyMatch(y->y.getFareSolutionId().equalsIgnoreCase(solutionId)))
                                    .collect(Collectors.toList()))
                                    ? "HIT" : "MISS");

                addEventToNewRelic(new NewRelicEvent(flightSearchSS1, newReStringStringMap));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchEvent search event {}", ex);
        }
    }

    public static void sendSS1Event(String eventName, GetFlightSolutionsRequest request, List<FlightSolutionDTO> flightSolutionsResponse, long responseTime, String error, long eventTime) {
        try {
            for (int index = 0; index < request.getSelectedSolutionsList().size(); index++) {
                String solutionId = request.getSelectedSolutions(index).getSolutionId();
                String comboFbc = request.getSelectedSolutions(index).getComboFbc();

                Map<String, Object> newReStringStringMap = new HashMap<>() {{
                    put(EVENT_NAME.name(), eventName);
                    put(API_VERSION.name(), "v1");
                    put(ITINERARY_ID.name(), "");
                    put(SOLUTION_ID.name(), solutionId);
                    put(COMBO_FBC.name(), comboFbc);
                    put(RESPONSE_TIME.name(), responseTime);
                    put(EVENT_TIME.name(), eventTime);
                    if (StringUtils.isNoneBlank(error))
                        put(ERROR.name(), error);
                }};

                if (flightSolutionsResponse != null)
                    newReStringStringMap.put(STATUS.name(), CollectionUtils.isNotEmpty(flightSolutionsResponse.stream()
                            .filter(x->x.getFareFamilyDTO().stream()
                                    .anyMatch(y->y.getFareSolutionId().equalsIgnoreCase(solutionId)))
                            .collect(Collectors.toList()))
                            ? "HIT" : "MISS");

                addEventToNewRelic(new NewRelicEvent(flightSearchSS1, newReStringStringMap));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchEvent search event {}", ex);
        }
    }
    public static void sendSS1V2Event(String eventName, GetFlightSolutionRequestV2 request, GetFlightSolutionsResponse flightSolutionsResponse, long responseTime, String error, long eventTime) {
        try {
            for (int index = 0; index < request.getSelectedSolutionInformationCount(); index++) {
                String solutionId = request.getSelectedSolutionInformation(index).getSolutionId();
                String comboFbc = request.getSelectedSolutionInformation(index).getComboFareBasisCode();

                Map<String, Object> newReStringStringMap = new HashMap<>() {{
                    put(EVENT_NAME.name(), eventName);
                    put(ITINERARY_ID.name(), request.getItineraryId());
                    put(API_VERSION.name(), "v2");
                    put(SOLUTION_ID.name(), solutionId);
                    put(COMBO_FBC.name(), comboFbc);
                    put(RESPONSE_TIME.name(), responseTime);
                    put(EVENT_TIME.name(), eventTime);
                    if (StringUtils.isNoneBlank(error))
                        put(ERROR.name(), error);
                }};

                if (flightSolutionsResponse != null)
                    newReStringStringMap.put(STATUS.name(),
                            CollectionUtils.isNotEmpty(flightSolutionsResponse.getFlightSolutions(index).getFlightsList()) ? "HIT" : "MISS");

                addEventToNewRelic(new NewRelicEvent(flightSearchSS1, newReStringStringMap));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendFlightSearchEvent search event {}", ex);
        }
    }


    public static void sendCacheRpcEvents(CacheRequest request, boolean result, String error, long eventTime) {
        try {
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(EVENT_NAME.toString(), "CACHE_" + request.getType().name());
                put(REQUEST_ID.name(), request.getRequestId());
                put(CACHE_REQUEST_TYPE.name(), request.getType().name());
                put(EVENT_TIME.name(), eventTime);
                if (!StringUtils.isEmpty(request.getSolution().getSolutionId())) {
                    put(SOLUTION_ID.name(), request.getSolution().getSolutionId());
                }
                if (!StringUtils.isEmpty(request.getItineraryId())) {
                    put(ITINERARY_ID.name(), request.getItineraryId());
                }
                put(INTL.name(), request.getRequest().getFlightSearchCriteria().getIsIntl());
                put(STATUS.name(), result);
                if (!StringUtils.isEmpty(error)) {
                    put(ERROR.name(), error);
                }
            }};
            addEventToNewRelic(new NewRelicEvent(clearCacheTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording cacheRpc event {}", ex);
        }
    }

    private static Map<String, Object> getFlightTaskMap(AvailabilityTaskRequest taskRequest, int taskNumber, int attemptCount, String expiryType, int promiseCount, int solutionCount, boolean isBG
            , long responseTime, boolean isConnectorCalled, boolean isTaskCompleted, String error, int searchAttemptCount, long eventTime) {
        Map<String, Object> newReStringStringMap = new HashMap<>() {{
            put(REQUEST_ID.name(), taskRequest.getRequest().getSearchId());
            put(IS_RETRY_SEARCH.name(), ThreadContext.get(IS_RETRY));
            put(TASK_NUMBER.name(), taskNumber);
            put(TASK_ATTEMPT_COUNT.name(), attemptCount);
            put(STRATEGY.name(), taskRequest.getRequest().getFlightSearchCriteria().getSearchOptions().getSearchStrategy().name());
            put(ADT.name(), taskRequest.getCustomerSearchCriteria().getPaxCount().getAdults());
            put(CHD.name(), taskRequest.getCustomerSearchCriteria().getPaxCount().getChildren());
            put(INF.name(), taskRequest.getCustomerSearchCriteria().getPaxCount().getInfants());
            put(SECTOR.name(), taskRequest.getCustomerSearchCriteria().getBaseCriteriaList().get(0).getOriginDestInfo().getFrom() + "_" + taskRequest.getCustomerSearchCriteria().getBaseCriteriaList().get(0).getOriginDestInfo().getTo());
            put(CABIN_TYPE.name(), taskRequest.getCustomerSearchCriteria().getCabinType().toString());
            put(SOURCE_TYPE.name(), taskRequest.getRequest().getCustomerInfo().getSourceType().toString());
            put(COMPANY_ID.name(), taskRequest.getRequest().getCustomerInfo().getCompanyId());
            put(USER_ID.name(), taskRequest.getRequest().getCustomerInfo().getUserId());
            put(EXPIRY_TYPE.name(), expiryType);
            put(PREFERRED_AIRLINE.name(), ConvertListToString(taskRequest.getRequest().getFlightSearchCriteria().getCustomerSearchCriteria().getPreferredAirlinesList()));
            put(SUPPLIER.name(), taskRequest.getSupplierCarriers().getSupplier().toString());
            put(CARRIERS.name(), ConvertListToString(taskRequest.getSupplierCarriers().getRequiredCarriers()));
            put(CRED_KEY.name(), taskRequest.getCredential().getKey());
            put(ARRIVAL.name(), taskRequest.getCustomerSearchCriteria().getBaseCriteriaList().get(0).getOriginDestInfo().getTo());
            put(DEPARTURE.name(), taskRequest.getCustomerSearchCriteria().getBaseCriteriaList().get(0).getOriginDestInfo().getFrom());
            put(DEPART_DATE.name(), taskRequest.getCustomerSearchCriteria().getBaseCriteriaList().get(0).getDate());
            put(PROMISE_COUNT.name(), promiseCount);
            put(SOLUTION_COUNT.name(), solutionCount);
            put(IS_BG.name(), isBG);
            put(RESPONSE_TIME.name(), responseTime);
            put(IS_CORP_COMMISSION.name(), taskRequest.getCredential().isCorpCommission());
            put(IS_RETAIL_COMMISSION.name(), taskRequest.getCredential().isRetailCommission());
            put(TRIP_TYPE.name(), taskRequest.isRoundTripTask() ? "ROUND_TRIP" : taskRequest.isMultiCityTask() ? "MULTI_CITY" : "ONE_WAY");
            put(IS_CONNECTOR_CALLED.name(), isConnectorCalled);
            put(IS_TASK_COMPLETED.name(), isTaskCompleted);
            if (!StringUtils.isEmpty(error))
                put(ERROR.name(), error);
            put(SEARCH_ATTEMPT_COUNT.name(), searchAttemptCount);
            put(EVENT_TIME.name(), eventTime);
            put(DX.name(), getDxValue(taskRequest.getCustomerSearchCriteria().getBaseCriteriaList().get(0).getDate(), eventTime));
        }};

        return newReStringStringMap;
    }

    private static Map<String, Object> getFlightTaskMap(NodeType nodeType, CustomerSearchCriteriaDTO customerSearchCriteriaDTO, FlightSearchCriteriaDTO flightSearchCriteriaDTO,
                                                        BaseSearchCriteriaDTO baseSearchCriteriaDTO, Credential credential, CompanyInfoDTO companyInfoDTO,
                                                        Supplier supplier, String searchId, String searchType, String criteriaId, int attemptCount,
                                                        String expiryType, int solutionCount, boolean isBG, long responseTime, boolean isConnectorCalled,
                                                        boolean isTaskCompleted, String error, int searchAttemptCount, long eventTime, String dataStore) {
        Map<String, Object> newReStringStringMap = new HashMap<>() {{
            put(NODE_TYPE.name(), nodeType.name());
            put(REQUEST_ID.name(), searchId);
            put(IS_RETRY_SEARCH.name(), ThreadContext.get(IS_RETRY));
            put(TASK_NUMBER.name(), criteriaId);
            put(TASK_ATTEMPT_COUNT.name(), attemptCount);
            put(STRATEGY.name(), searchType);
            put(ADT.name(), customerSearchCriteriaDTO.getPaxCount().getAdults());
            put(CHD.name(), customerSearchCriteriaDTO.getPaxCount().getChildren());
            put(INF.name(), customerSearchCriteriaDTO.getPaxCount().getInfants());
            put(SECTOR.name(), baseSearchCriteriaDTO.getOriginDestinationInfoDTO().getFrom() + "_" + baseSearchCriteriaDTO.getOriginDestinationInfoDTO().getTo());
            put(CABIN_TYPE.name(), customerSearchCriteriaDTO.getCabinType().toString());
            put(SOURCE_TYPE.name(), companyInfoDTO.getSourceType().toString());
            put(COMPANY_ID.name(), companyInfoDTO.getCompanyId());
            put(USER_ID.name(), companyInfoDTO.getUserId());
            put(EXPIRY_TYPE.name(), expiryType);
            put(PREFERRED_AIRLINE.name(), ConvertListToString(flightSearchCriteriaDTO.getPreferredAirlines()));
            put(SUPPLIER.name(), supplier.toString());
            put(CARRIERS.name(), ConvertListToString(flightSearchCriteriaDTO.getPreferredAirlines()));
            put(CRED_KEY.name(), credential.getKey());
            put(ARRIVAL.name(), baseSearchCriteriaDTO.getOriginDestinationInfoDTO().getTo());
            put(DEPARTURE.name(), baseSearchCriteriaDTO.getOriginDestinationInfoDTO().getFrom());
            put(DEPART_DATE.name(), baseSearchCriteriaDTO.getDate());
            put(SOLUTION_COUNT.name(), solutionCount);
            put(IS_BG.name(), isBG);
            put(RESPONSE_TIME.name(), responseTime);
            put(IS_CORP_COMMISSION.name(), credential.isCorpCommission());
            put(IS_RETAIL_COMMISSION.name(), credential.isRetailCommission());
            put(TRIP_TYPE.name(), flightSearchCriteriaDTO.isRoundTrip() ? "ROUND_TRIP" : "ONE_WAY");
            put(IS_CONNECTOR_CALLED.name(), isConnectorCalled);
            put(IS_TASK_COMPLETED.name(), isTaskCompleted);
            put(FARE_CATEGORY.name(), flightSearchCriteriaDTO.getFareCategoryType());
            if (!StringUtils.isEmpty(error))
                put(ERROR.name(), error);
            put(SEARCH_ATTEMPT_COUNT.name(), searchAttemptCount);
            put(EVENT_TIME.name(), eventTime);
            put(DATA_SOURCE.name(), dataStore);
            put(DX.name(), getDxValue(baseSearchCriteriaDTO.getDate(), eventTime));
        }};

        return newReStringStringMap;
    }

    private static void setOriginDestination(Map<String, Object> newReStringStringMap, List<BaseCriteria> baseCriteriaList) {
        if (CommonUtil.isMultiCity(baseCriteriaList)) {
            newReStringStringMap.put(MC_DEPARTURE.name(), ConvertListToString(baseCriteriaList.stream()
                    .map(x -> x.getOriginDestInfo().getFrom()).collect(Collectors.toList())));
            newReStringStringMap.put(MC_ARRIVAL.name(), ConvertListToString(baseCriteriaList.stream()
                    .map(x -> x.getOriginDestInfo().getTo()).collect(Collectors.toList())));
            newReStringStringMap.put(MC_DEPART_DATE.name(), ConvertListToString(baseCriteriaList.stream()
                    .map(x -> String.valueOf(x.getDate())).collect(Collectors.toList())));
        } else {
            newReStringStringMap.put(DEPARTURE.name(), baseCriteriaList.get(0).getOriginDestInfo().getFrom());
            newReStringStringMap.put(ARRIVAL.name(), baseCriteriaList.get(0).getOriginDestInfo().getTo());
            newReStringStringMap.put(SECTOR.name(), baseCriteriaList.get(0).getOriginDestInfo().getFrom() + "_" + baseCriteriaList.get(0).getOriginDestInfo().getTo());
            newReStringStringMap.put(DEPART_DATE.name(), baseCriteriaList.get(0).getDate());
        }
    }

    public static Map<String, Object> getBaggageCacheEventMap(String key, long responseTime, String operationType) {
        String[] cacheKeyAttributes = key.split("\\|");
        Map<String, Object> newReStringStringMap = new HashMap<>() {{
            put(REQUEST_ID.name(), ThreadContext.get(ThreadContextParams.REQUEST_ID));
            put(CACHE_KEY.name(), key);
            put(DEPART_DATE.name(), cacheKeyAttributes[4]);
            put(SUPPLIER.name(), cacheKeyAttributes[7]);
            put(CRED_KEY.name(), cacheKeyAttributes[8]);
            put(COMBO_FBC.name(), cacheKeyAttributes[6]);
            put(STATUS.name(), "HIT");
            put(OPERATION_TYPE.name(), operationType);
            put(RESPONSE_TIME.name(), responseTime);
        }};

        return newReStringStringMap;
    }

    public static Map<String, Object> getFlightCacheEventMap(String key, long responseTime, String operationType) {
        String[] cacheKeyAttributes = key.split("\\|");
        Map<String, Object> newReStringStringMap = new HashMap<>() {{
            put(REQUEST_ID.name(), ThreadContext.get(ThreadContextParams.REQUEST_ID));
            put(CACHE_KEY.name(), key);
            put(CRED_KEY.name(), cacheKeyAttributes[11]);
            put(STATUS.name(), "HIT");
            put(OPERATION_TYPE.name(), operationType);
            put(RESPONSE_TIME.name(), responseTime);
            put(DEPART_DATE.name(), cacheKeyAttributes[2]);
        }};

        return newReStringStringMap;
    }

    private static int getDxValue(long departDate, long eventTime) {
        return (int) (Math.floor(departDate - eventTime) / 86400000 + ((departDate - eventTime) % 86400000 != 0 ? 1 : 0));
    }

    private static String ConvertListToString(List<String> list) {

        StringBuilder sb = new StringBuilder();
        for (String str : list) {
            sb.append(str);
            sb.append(",");
        }
        if (sb.length() != 0)
            sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    private static boolean isPTCRequest(List<FareGroupInfo> fareGroupInfos) {
        return PTC_FARE_GROUP_INFO.stream().anyMatch(fareGroupInfo -> fareGroupInfos.contains(fareGroupInfo));
    }

    public static void productAndFareClass(long companyId, CabinType cabinType, FareCategoryType fareCategoryType, List<FareGroupInfo> fareGroupInfo, SourceTypeOuterClass.SourceType sourceType, String productClass, String supplier, String type) {
        try {
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(COMPANY_ID.name(), String.valueOf(companyId));
                put(CABIN_TYPE.name(), cabinType.name());
                put(FARE_CATEGORY.name(), fareCategoryType.name());
                put(PAX_FARE_TYPE.name(), ConvertListToString(fareGroupInfo.stream().map(x -> x.toString()).collect(Collectors.toList())));
                put(SOURCE_TYPE.name(), sourceType.name());
                put(PRODUCT_CLASS.name(), productClass);
                put(SUPPLIER.name(), supplier);
                put("Type", type);

            }};
            addEventToNewRelic(new NewRelicEvent(productClassTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording indigoPC event {}", ex);
        }
    }

    public static void sendAsyncMessageEvent(){
        try{
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put("message","Async call");

            }};
            addEventToNewRelic(new NewRelicEvent(asyncTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording indigoPC event {}", ex);
        }
    }
    public static void sendAsyncMessageEvent(String error){
        try{
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put("message","Async call");
                put("error",error);
            }};
            addEventToNewRelic(new NewRelicEvent(asyncTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording indigoPC event {}", ex);
        }
    }

    public static void sendBaggageSupplierStrategy(String requestId, BaggageSource source, BaggageCriteria baggageCriteria, SolutionInfo solutionInfo) {
        try {
            Map<String, Object> newReStringStringMap = new HashMap<>() {{
                put(REQUEST_ID.name(), requestId);
                put(SOURCE.name(), source.toString());
                put(SUPPLIER.name(), solutionInfo.getSupplierInfo().getSupplier());
                put(FARE_CATEGORY.name(), solutionInfo.getFareGroupInfo().name());
                put(INTL.name(), baggageCriteria.getIsInternational());
                put(EVENT_NAME.toString(), "BaggageStrategy");
            }};
            addEventToNewRelic(new NewRelicEvent(baggageEventTable, newReStringStringMap));
        } catch (Exception ex) {
            LOGGER.error("Exception while recording sendBaggageEvent search event {}", ex);
        }
    }

    public static void sendAmendSearchEvent(Map<String, Object> newRelicParams, long startTime, long responseTime, Optional<Object> error) {
        Map<String, Object> newRelicParamsMap = new HashMap<>(newRelicParams);

        newRelicParamsMap.put(RESPONSE_TIME.name(), responseTime);
        newRelicParamsMap.put(EVENT_TIME.name(), startTime);
        newRelicParamsMap.put(TIME_TAKEN.name(), responseTime - startTime);

        error.ifPresent(er -> newRelicParams.put(ERROR.name(), er));

        addEventToNewRelic(new NewRelicEvent(amendSearchTable, newRelicParams));
    }


    public static void sendThemisException(String exception, String className)
    {
        Map<String, Object> maps = new HashMap<>(){{
           put(RULE_ENGINE_CLASSNAME.name(), className);
           put(EXCEPTION.name(), exception);
        }};
        addEventToNewRelic(new NewRelicEvent(sisRuleEngineException,maps));
    }
}
