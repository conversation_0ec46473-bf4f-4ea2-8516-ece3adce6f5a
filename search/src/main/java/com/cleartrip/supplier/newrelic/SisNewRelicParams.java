package com.cleartrip.supplier.newrelic;

public enum SisNewRelicParams {

    NODE_TYPE,
    HOST_NAME,
    SOLUTION_ID,
    FARE_TYPE,
    PRODUCT_CLASS,
    PAX_FARE_TYPE,
    EVENT_NAME,
    API_VERSION,
    REQUEST_ID,
    IS_RETRY_SEARCH,
    TASK_NUMBER,
    TASK_ATTEMPT_COUNT,
    SECTOR,
    SUPPLIER,
    ADT,
    CHD,
    INF,
    DEPART_DATE,
    INTL,
    TASK_COUNT,
    DEPARTURE,
    ARRIVAL,
    MC_DEPARTURE,
    MC_ARRIVAL,
    MC_DEPART_DATE,
    IS_CONNECTOR_CALLED,
    IS_TASK_COMPLETED,
    IS_BG,
    CACHE_KEY,
    CARRIERS,
    SOURCE,
    CABIN_TYPE,
    IS_CORP_COMMISSION,
    IS_RETAIL_COMMISSION,
    COMBO_FBC,
    STATUS,
    EXPIRY_TIME,
    OPERATION_TYPE,
    RESPONSE_TIME,
    TIME_TAKE<PERSON>,
    TRIP_TYPE,
    <PERSON><PERSON><PERSON>,
    IS_<PERSON>ESPONSE_COMPLETE,
    <PERSON>ER_ID,
    <PERSON>X<PERSON>RY_TYPE,
    <PERSON><PERSON><PERSON>Y_ID,
    SOURCE_TYPE,
    PREFERRED_AIRLINE,
    IS_RADIAL_SEARCH,
    SOLUTION_COUNT,
    DUPLICATE_SOLUTION_COUNT,
    CRED_KEY,
    SEARCH_ATTEMPT_COUNT,
    CACHE_REQUEST_TYPE,
    SELLING_COUNTRY,
    ITINERARY_ID,
    STRATEGY,
    EVENT_TIME,
    DX,
    SOFT_EXPIRED_COUNT,
    HARD_EXPIRED_COUNT,
    NOT_EXPIRED_COUNT,
    PROMISE_COUNT,
    ROUNDTRIP_SOLUTION_COUNT,
    ONEWAY_SOLUTION_COUNT,
    RETURN_SOLUTION_COUNT,
    CREDENTIAL_KEY,
    PROMO_CODE,
    ACCOUNT_CODE,
    FARE_CATEGORY,
    JOURNEY_TYPE,
    VALUE,
    IS_SFF_REQUEST,
    SEARCH_STRATEGY,
    FARE_GROUP_INFO,
    FARE_DISPLAY_NAME,
    IS_SINGLE_SOLUTION_CALL_DONE,
    IS_SELECTED_SEARCH_CALL_DONE,
    FARE_LIST_SIZE,
    IS_PTC_REQUEST,
    OLD_PRICE,
    NEW_PRICE,
    IS_PRICE_CHANGE,
    SMS_CALL_TYPE,
    IS_SOLUTION_PRESENT,
    TRIP_ID,
    SUPPLIER_PNR,
    VALIDATING_CARRIER,
    IS_PARTIAL_PAX,
    AMEND_SEARCH_WORKFLOW_STRATEGY,
    LAYER,
    APPLICATION,
    DOMAIN,
    ORCHESTRATOR,
    WORKFLOW,
    RULE_ENGINE_CLASSNAME,
    EXCEPTION,

    DATA_SOURCE,
    FINGERPRINT,
    AMEND_SOL_COUNT,
    SEARCH_SOL_COUNT
}
