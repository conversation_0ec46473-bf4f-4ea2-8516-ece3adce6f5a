package com.cleartrip.supplier.web.grpchandler;

import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.newrelic.NewRelicUtil;
import com.cleartrip.supplier.search.FlightAvailabilityService;
import com.cleartrip.supplier.search.application.FlightAvailApplication;
import com.cleartrip.supplier.search.application.FlightSearchApplication;
import com.cleartrip.supplier.search.application.FlightAvailApplicationImpl;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import io.grpc.stub.StreamObserver;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.logging.log4j.ThreadContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

import static com.cleartrip.supplier.search.constant.ThreadContextParams.*;

@Log4j2
public class FlightSearchServiceGrpcImpl extends FlightSearchServiceGrpc.FlightSearchServiceImplBase   {
    private static final int COMPUTE_THREAD_COUNT = 600;
    private static final Map<Integer,Integer> searchAttemptConfiguration = new HashMap<>(){{
        put(1, 1);
        put(2, FIRST_SEARCH_ATTEMPT_SLEEP_INTERVAL_IN_MILLIS);
        //put(3, SECOND_SEARCH_ATTEMPT_SLEEP_INTERVAL_IN_MILLIS);
    }};

    private static final int MAX_SEARCH_ATTEMPTS = 2;
    private static final int FIRST_SEARCH_ATTEMPT_SLEEP_INTERVAL_IN_MILLIS = 2400;
    private static final int SECOND_SEARCH_ATTEMPT_SLEEP_INTERVAL_IN_MILLIS = 1500;
    private final SearchConfigContainer properties;
    private final FlightAvailabilityService availabilityServiceV2;
    private final FlightSearchApplication flightSearchApplication;
    private ExecutorService searchRpcExecutor;
    private final FlightAvailApplication flightAvailApplication;

    private static final ScheduledExecutorService searchAttemptDelayExecutor = Executors.newScheduledThreadPool(200);

    @Inject
    public FlightSearchServiceGrpcImpl(SearchConfigContainer properties,
                                       @Named("FlightAvailabilityOrchestrator")FlightAvailabilityService availabilityServiceV2,
                                       @Named("FlightSearchApplicationImpl")FlightSearchApplication flightSearchApplication,
                                       @Named("FlightAvailApplicationImpl")FlightAvailApplication flightAvailApplication) {
        this.properties = properties;
        this.availabilityServiceV2 = availabilityServiceV2;
        this.flightSearchApplication = flightSearchApplication;
        this.flightAvailApplication = flightAvailApplication;
        this.searchRpcExecutor = Executors.newFixedThreadPool(COMPUTE_THREAD_COUNT);
    }

    public void searchRpc(SearchRequest searchRequest,
                          StreamObserver<SearchResponse> responseObserver) {
        AtomicInteger searchAttempt = new AtomicInteger(1);
        ThreadContext.put(REQUEST_ID, searchRequest.getSearchId());
        ThreadContext.put(IS_RETRY, "NO");
        log.info("flightSearchRpc request {}", searchRequest);
        long reqTime = System.currentTimeMillis();
        searchFlowV2(searchRequest, responseObserver);
    }
    public void searchRpcV2(SearchRequest searchRequest,
                          StreamObserver<SearchResponse> responseObserver) {
        AtomicInteger searchAttempt = new AtomicInteger(1);
        ThreadContext.put(REQUEST_ID, searchRequest.getSearchId());
        ThreadContext.put(IS_RETRY, "NO");
        log.info("flightSearchRpc request {}", searchRequest);
        long reqTime = System.currentTimeMillis();
        searchFlowV3(searchRequest, responseObserver);

    }

    Predicate<SearchStrategy> isSupplierSearch = SearchStrategy.SUPPLIER::equals;

    Predicate<SearchStrategy> isOptimisedSearch = SearchStrategy.OPTIMISED::equals;

    private void searchFlowV2(SearchRequest searchRequest, StreamObserver<SearchResponse> responseObserver) {
        try {
            responseObserver.onNext(availabilityServiceV2.getAvailability(searchRequest));
            responseObserver.onCompleted();
        } catch (Exception e) {
            if(Objects.isNull(availabilityServiceV2)) {
                log.error("availability service is null");
            }
            if(Objects.isNull(responseObserver)) {
                log.error("response observer is null");
            }
            log.error("Failed due to the {}", e.getMessage(), e);
            if(Objects.nonNull(e) && Objects.nonNull(e.getStackTrace())) {
                log.error("stack trace : {}", (Object[]) e.getStackTrace());
            }
            responseObserver.onError(e);
            responseObserver.onCompleted();
        }
    }
    private void searchFlowV3(SearchRequest searchRequest, StreamObserver<SearchResponse> responseObserver) {
        try {
            responseObserver.onNext(flightSearchApplication.performSearchWithProto(searchRequest));
            responseObserver.onCompleted();
        } catch (Exception e) {
            if(Objects.isNull(availabilityServiceV2)) {
                log.error("availability service is null");
            }
            if(Objects.isNull(responseObserver)) {
                log.error("response observer is null");
            }
            log.error("Failed due to the {}", e.getMessage(), e);
            if(Objects.nonNull(e) && Objects.nonNull(e.getStackTrace())) {
                log.error("stack trace : {}", (Object[]) e.getStackTrace());
            }
            responseObserver.onError(e);
            responseObserver.onCompleted();
        }
    }

    public void getFlightSolutions(GetFlightSolutionsRequest request,
                                   StreamObserver<GetFlightSolutionsResponse> responseObserver) {
        long startTime = System.currentTimeMillis();
        try {
            responseObserver
                    .onNext(availabilityServiceV2.fetchFlightSolutionV1(request));
            responseObserver
                    .onCompleted();

        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            NewRelicUtil.sendSS1Event("ss1", request, null, System.currentTimeMillis() - startTime, "getFlightSolutions_ " + e.getMessage(), startTime);
            responseObserver.onError(e);
        }
    }

    public void getCacheFlightSolutions(GetCacheSolutionRequest request,
                                   StreamObserver<GetCacheFlightSolutionsResponse> responseObserver) {
        try {
            responseObserver
                    .onNext(availabilityServiceV2.getCacheFlightSolutionWithFare(request.getParentId()));
            responseObserver
                    .onCompleted();

        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
        }
    }

    public void cacheRpc(CacheRequest request,
                                       StreamObserver<SearchResponse> responseObserver) {
        long startTime = System.currentTimeMillis();
        try {
            ThreadContext.put(REQUEST_ID, request.getRequestId());
            ThreadContext.put(SOLUTION_ID, request.getSolution().getSolutionId());
            log.info("clearCacheAndSearchRpc request {}", request);
            switch (request.getType()){
                case CLEAR : {
                    boolean isCacheClear;
                    isCacheClear = availabilityServiceV2.removeSolutionFromCache(request.getRequest(), request.getSolution());
                    NewRelicUtil.sendCacheRpcEvents(request, isCacheClear, null, startTime);
                    break;
                }
                case REFRESH : {
                    boolean isCacheRefreshed;
                    isCacheRefreshed = availabilityServiceV2.reSearchSolution(request.getRequest(), request.getSolution());
                    NewRelicUtil.sendCacheRpcEvents(request, isCacheRefreshed, null, startTime);
                    break;
                }
                case UNRECOGNIZED : {
                    //todo add a default error handler here
                    throw new UnsupportedOperationException("Unrecognized type of cache request");
                }
            }
            searchRpc(request.getRequest(),responseObserver);
        } catch (Exception e) {
            log.error("Exception while "+ request.getType().name() +" event for request id "+ request.getRequestId()+", error "+e.getMessage());
            log.error(ExceptionUtils.getStackTrace(e));
            NewRelicUtil.sendCacheRpcEvents(request,false, e.getMessage(), startTime);
            responseObserver.onError(e);
        }
    }

    public void getFlightSolutionV2(com.cleartrip.supplier.inventory.protos.v1.GetFlightSolutionRequestV2 request,
                                    io.grpc.stub.StreamObserver<com.cleartrip.supplier.inventory.protos.v1.GetFlightSolutionsResponse> responseObserver) {
        long startTime = System.currentTimeMillis();
        try {
            responseObserver
                    .onNext(flightAvailApplication.getFlightSolutionV2(request));
            responseObserver
                    .onCompleted();

        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            NewRelicUtil.sendSS1V2Event("ss1_v2", request, null, System.currentTimeMillis() - startTime, "getFlightSolutionsV2_ " + e.getMessage(), startTime);
            responseObserver.onError(e);
        }
    }
}