package com.cleartrip.supplier.credentials;

import com.cleartrip.config.management.api.resources.airport.AirportInfo;
import com.cleartrip.config.management.api.resources.airport.AirportInfoBean;
import com.cleartrip.config.management.core.enumaration.AirSupplier;
import com.cleartrip.config.management.core.enumaration.CredentialType;
import com.cleartrip.config.management.core.resources.airport.AirportInfoResource;
import com.cleartrip.config.management.core.resources.credential.CachedCredentials;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.search.constant.SisConfigKeys;
import com.cleartrip.supplier.search.data.SupplierChildInfo;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.exception.JsonParseRuntimeException;
import com.cleartrip.supplier.search.models.BaseSearchCriteriaDTO;
import com.cleartrip.supplier.search.models.idetification.CredentialRuleEvaluatorRequest;
import com.cleartrip.supplier.search.models.idetification.SupplierAdditionalInfo;
import com.cleartrip.supplier.search.resources.SISCachedConfigurationResources;
import com.cleartrip.supplier.search.util.AvailabilityUtil;
import com.cleartrip.supplier.search.util.SuppliersChildResolver;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Named;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cleartrip.supplier.inventory.protos.v1.FareCategoryType.CORPORATE;
import static com.cleartrip.supplier.inventory.protos.v1.FareCategoryType.RETAIL;

@Log4j2
public class CredentialRuleEvaluatorV2 {

    private final CachedCredentials cachedCredentials;
    private final SISCachedConfigurationResources cachedProperties;
    private final AirportInfoBean airportInfo;
    private final RuleEvaluator ruleEvaluator;
    private final SearchConfigContainer properties;
    private final Map<String, String> channelTypeMap;
    private final SuppliersChildResolver suppliersChildResolver;
    private static final String COMMISSION = "commission";
    private static final String CORP = "corp";

    @Inject
    public CredentialRuleEvaluatorV2(CachedCredentials cachedCredentials, @Named("sISCachedConfigurationResources") SISCachedConfigurationResources cachedProperties, AirportInfoResource airportInfoResource
            , RuleEvaluator ruleEvaluator, SearchConfigContainer properties, SuppliersChildResolver suppliersChildResolver) {
        this.cachedCredentials = cachedCredentials;
        this.cachedProperties = cachedProperties;
        this.ruleEvaluator = ruleEvaluator;
        this.airportInfo = airportInfoResource.getResourceValue();
        this.properties = properties;
        this.channelTypeMap = initialiseChannelType();
        this.suppliersChildResolver = suppliersChildResolver;
    }

    public Map<String, String> initialiseChannelType(){
        ObjectMapper mapper = new ObjectMapper();
        String channelTypeMap = properties.getPropertyValue(SisConfigKeys.CREDENTIAL_CHANNEL_TYPE_MAPPING);
        try{
            return mapper.readValue(channelTypeMap, new TypeReference<>() {
            });
        }catch (Exception e){
            log.error("Failed to read the config key: {} value: {} due to the {}",
                    SisConfigKeys.CREDENTIAL_CHANNEL_TYPE_MAPPING, channelTypeMap, e.getMessage(), e);
            throw new JsonParseRuntimeException(channelTypeMap, e);
        }
    }

    public Map<String, Map<String, String>> getCredentialForSisTask(CredentialRuleEvaluatorRequest request) {
        Map<String, Map<String, String>> credentialMap = new LinkedHashMap<>();

        SupplierAdditionalInfo supplierAdditionalInfo = request.getSupplierJourneyCombo().getSupplierCarrierPair().getSupplierAdditionalInfo();
        if(Objects.nonNull(supplierAdditionalInfo) && Objects.nonNull(supplierAdditionalInfo.getCredentialKey())) {
                String credentialKeyMultiPcc = supplierAdditionalInfo.getCredentialKey();
                Map<String, String> credentials = cachedCredentials.getResourceValue().get(credentialKeyMultiPcc);
                Map<String, String> attributeMap = new LinkedHashMap<>(credentials);
                attributeMap.put("credentialKey", credentialKeyMultiPcc);
                credentialMap.put(credentialKeyMultiPcc, attributeMap);
                return credentialMap;
        }

        Map<String, Object> attributes = getAttributesForSISTask(request);
        Set<Map<String, Object>> rules = getRelevantRulesForSISTask(request, request.getCredentialType());
        if (rules != null) {
            for (Map<String, Object> rule : rules) {
                Set<String> failedChecks = new HashSet<>();
                boolean result = isCredentialRuleApplicable(attributes, rule, failedChecks) && checkMiscAttributes(attributes, rule, failedChecks);

                if (result) {
                    Map<String, Object> consequence = (Map<String, Object>) rule.get("consequence");
                    String credentialKey = (String) consequence.get("lhs");
                    Map<String, String> credentials = cachedCredentials.getResourceValue().get(credentialKey);

                    if (credentials != null) {
                        Map<String, String> attributeMap = credentialMap.get(credentialKey);

                        if (attributeMap == null) {
                            attributeMap = new LinkedHashMap<>(credentials);
                            attributeMap.put("credentialKey", credentialKey);
                            if (Optional.ofNullable(rule.get("id")).isPresent())
                                attributeMap.put("ruleId", rule.get("id").toString());

                            // commission field is only present for LCC supplier, and since for GDS commission field is not present , hence the below logic is not applicable for GDS (GAL for domestic.)
                            // Moving foreward, we need to create the construct for fare category in cred. rules itself to avoid these hacks.
                            if ((request.getSupplierJourneyCombo().getSupplierCarrierPair().getSupplier() != Supplier.GALILEO) &&
                                (request.getSupplierJourneyCombo().getSupplierCarrierPair().getSupplier() != Supplier.GALILEO_LFS_INTERNATIONAL) &&
                                            ((RETAIL == request.getSupplierJourneyCombo().getFareCategoryType() && CORP.equalsIgnoreCase(credentials.get(COMMISSION))) ||
                                                    (CORPORATE == request.getSupplierJourneyCombo().getFareCategoryType() && !CORP.equalsIgnoreCase(credentials.get(COMMISSION))))) {
                                continue;
                            }
                            credentialMap.put(credentialKey, attributeMap);
                        }
                        populateMiscAttributes(attributeMap, rule);
                    }
                }
            }
        }
        return credentialMap;
    }


    private Map<String, Object> getAttributesForSISTask(CredentialRuleEvaluatorRequest request) {
        Map<String, Object> attributes = AirRuleUtil.createAttributes();
        AirRuleUtil.populateBookingDateAndTime(attributes);
        attributes.put("companyId", request.getCompanyId());
        attributes.put("userId", request.getUserId());

        BaseSearchCriteriaDTO onwardCriteria = request.getSupplierJourneyCombo().getBaseSearchCriterionDTOS().getBaseSearchCriteriaDTOList().get(0);

        Date bookingDate = (Date) attributes.get("bookingDate");
        Integer dplusx = getNumberOfNights(bookingDate, new Date(onwardCriteria.getDate()), false);

        String sector = onwardCriteria.getOriginDestinationInfoDTO().getFrom() + "-" + onwardCriteria.getOriginDestinationInfoDTO().getTo();

        AirportInfo originAirportInfo = airportInfo.getAirportInfoMap().get(onwardCriteria.getOriginDestinationInfoDTO().getFrom());
        String originCountry = (originAirportInfo != null) ? originAirportInfo.getCountryCode() : null;

        AirportInfo destinationAirportInfo = airportInfo.getAirportInfoMap().get(onwardCriteria.getOriginDestinationInfoDTO().getTo());
        String destinationCountry = (destinationAirportInfo != null) ? destinationAirportInfo.getCountryCode() : null;

        attributes.put("source", channelTypeMap.get(request.getSourceType().name()));
        attributes.put("dplusx", dplusx);
        attributes.put("carrier", request.getSupplierJourneyCombo().getSupplierCarrierPair().getRequiredCarriers());
        attributes.put("fareCategory", String.join("", request.getFareCategory()));
        attributes.put("origin", onwardCriteria.getOriginDestinationInfoDTO().getFrom());
        attributes.put("destination", onwardCriteria.getOriginDestinationInfoDTO().getTo());
        attributes.put("originCountry", originCountry);
        attributes.put("destinationCountry", destinationCountry);
        attributes.put("sector", sector);
        if (request.getSupplierJourneyCombo().getFareCategoryType() == CORPORATE) {
            attributes.put("fareSubType", "corp");
        } else {
            attributes.put("fareSubType", "retail");
        }
        attributes.put("onwardDepartureDate", AirRuleUtil.extractDateFromDateTime(new Date(onwardCriteria.getDate())));
        if (AvailabilityUtil.isRoundTrip(request.getSupplierJourneyCombo().getBaseSearchCriterionDTOS().getBaseSearchCriteriaDTOList())) {
            attributes.put("returnDepartureDate", AirRuleUtil.extractDateFromDateTime(new Date(request.getSupplierJourneyCombo().getBaseSearchCriterionDTOS().getBaseSearchCriteriaDTOList().get(1).getDate())));
        }
        attributes.put("showCorpFareForUser", request.getSupplierJourneyCombo().getFareCategoryType() == CORPORATE);

        return attributes;
    }

    private Set<Map<String, Object>> getRelevantRulesForSISTask(CredentialRuleEvaluatorRequest request, CredentialType credentialType) {
        Set<Map<String, Object>> rules = null;
        Map<String, Map<CredentialType, Map<AirSupplier, TreeSet<Map<String, Object>>>>> countryTypeRules = cachedProperties.getCachedCredentialRules().getResourceValue();

        if (countryTypeRules != null) {
            String sellingCountry = request.getSellingCountry();
            String sourceType = channelTypeMap.get(request.getSourceType().name());

            String key = sellingCountry + "-" + sourceType;
            Map<CredentialType, Map<AirSupplier, TreeSet<Map<String, Object>>>> credentialTypeRules = countryTypeRules.get(key);
            if (credentialTypeRules != null) {
                Map<AirSupplier, TreeSet<Map<String, Object>>> AirSupplierRules = credentialTypeRules.get(credentialType);

                if (AirSupplierRules != null) {
                    rules = AirSupplierRules.get(getAirSupplierFromSupplier(request));
                }
            }
        }
        return rules;
    }

    private AirSupplier getAirSupplierFromSupplier(CredentialRuleEvaluatorRequest request) {
        // This is a hack to enable NDC fare as GALILEO_IC is not in use.
        // So we are using it to power NDC fares
        if(request.getSupplierJourneyCombo().getSupplierCarrierPair().getSupplier() == Supplier.GAL_NDC)
            return AirSupplier.GALILEO_IC;
        SupplierChildInfo.ChildSupplier.ChildType childType = request.isIntl() ? SupplierChildInfo.ChildSupplier.ChildType.INTERNATIONAL :
                request.isRT() ? SupplierChildInfo.ChildSupplier.ChildType.ROUND_TRIP : null;
        if (childType != null)
            return AirSupplier.valueOf(suppliersChildResolver.getSupplierChild(Supplier.valueOf(request.getSupplierJourneyCombo().getSupplierCarrierPair().getSupplier().name()), childType));
        return AirSupplier.valueOf(request.getSupplierJourneyCombo().getSupplierCarrierPair().getSupplier().name());
    }

    private boolean isCredentialRuleApplicable(Map<String, Object> attributes, Map<String, Object> rule, Set<String> failedChecks) {
        boolean result = false;
        Map<String, Object> condition = (Map<String, Object>) rule.get("condition");

        try {
            result = ruleEvaluator.evaluateCondition(attributes, condition, cachedProperties, failedChecks, null, null, null, null, null, null);
        } catch (Exception e) {
            log.error("Invalid condition for credential-id : " + rule.get("id"), e);
        }

        return result;
    }

    private boolean checkMiscAttributes(Map<String, Object> attributes, Map<String, Object> rule, Set<String> failedChecks) {
        boolean result = true;
        Map<String, Object> miscAttributes = (Map<String, Object>) rule.get("misc");
        String ruleFareCategory = (String) miscAttributes.get("fare_category");
        List<String> ruleCarriers = (List<String>) miscAttributes.get("carrier");

        String fareCategory = (String) attributes.get("fareCategory");
        List<String> carriers = (List<String>) attributes.get("carrier");

        if ((StringUtils.isBlank(fareCategory) && getFareCategories().contains(ruleFareCategory))
                 || (StringUtils.isNotBlank(ruleFareCategory) && StringUtils.isNotBlank(fareCategory) && !fareCategory.contains(ruleFareCategory))) {
            result = false;
            failedChecks.add("fare_category");
        }

        if (result && ruleCarriers != null && carriers != null) {
            boolean carrierMatch = false;

            for (String carrier : carriers) {
                if (ruleCarriers.contains(carrier)) {
                    carrierMatch = true;
                    break;
                }
            }

            result = carrierMatch;
        }

        return result;
    }

    private void populateMiscAttributes(Map<String, String> attributesMap, Map<String, Object> rule) {
        Map<String, Object> misc = (Map<String, Object>) rule.get("misc");
        String opaque = (String) misc.get("opaque");
        String opaqueAirlineName = (String) misc.get("opaque_airline_name");
        String opaqueAirlineCode = (String) misc.get("opaque_airline_code");
        String opaqueFlightNumber = (String) misc.get("opaque_flight_number");
        String fareCategory = (String) misc.get("fare_category");
        String preferred = (String) misc.get("preferred");
        List<String> carriers = (List<String>) misc.get("carrier");

        if (opaque != null) {
            attributesMap.put("opaque", opaque);
        }
        if (opaqueAirlineName != null) {
            attributesMap.put("opaque_airline_name", opaqueAirlineName);
        }
        if (opaqueAirlineCode != null) {
            attributesMap.put("opaque_airline_code", opaqueAirlineCode);
        }
        if (opaqueFlightNumber != null) {
            attributesMap.put("opaque_flight_number", opaqueFlightNumber);
        }
        if (fareCategory != null) {
            attributesMap.put("fare_category", fareCategory);
        }
        if (preferred != null) {
            attributesMap.put("preferred", preferred);
        }
        if (carriers != null && carriers.size() > 0) {
            String carrier = attributesMap.get("carrier");

            if (carrier == null) {
                carrier = "";
            }

            carrier = carriers + "," + StringUtils.join(carriers, ",");
            attributesMap.put("carrier", carrier);
        }
    }

    public static int getNumberOfNights(Date startDate, Date endDate, boolean setNullStartDateAsToday) {
        if (setNullStartDateAsToday && startDate == null) {
            startDate = new Date();
        }
        int nights = 0;
        try {
            if (startDate != null && endDate != null) {
                startDate = formatDate(startDate, "yyyy-MM-dd");
                endDate = formatDate(endDate, "yyyy-MM-dd");
                if (startDate.before(endDate)) {
                    nights = getDifferenceBetweenTwoDatesInDays(startDate, endDate);
                }
            }
        } catch (Exception e) {
            log.error("Exception is:" + e.getMessage(), e);
        }
        return nights;
    }

    /**
     * parses the date string for the given formatter.
     * @param date Date to parse
     * @param dateFormatter Date Formatter to use
     * @return Formatted Date
     * @exception Exception on any exception
     */
    public static Date formatDate(Date date, String dateFormatter) throws Exception {
        SimpleDateFormat sdfInput = new SimpleDateFormat(dateFormatter);
        String formattedDate = sdfInput.format(date);
        date = sdfInput.parse(formattedDate);
        return date;
    }

    private static int getDifferenceBetweenTwoDatesInDays(Date startDate, Date endDate) {
        long diffInMillis = Math.abs(endDate.getTime() - startDate.getTime());
        return (int) TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
    }

    private List<String> getFareCategories() {
        return Arrays.stream(properties.getPropertyValue(SisConfigKeys.SFF_FARE_CATEGORY_PROPERTY, "tbf,coupon").split(",", -1)).collect(Collectors.toList());
    }
}
