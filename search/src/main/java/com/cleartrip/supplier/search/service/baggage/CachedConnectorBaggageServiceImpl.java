package com.cleartrip.supplier.search.service.baggage;

import com.cleartrip.air.sms.api.traveller.Traveller;
import com.cleartrip.air.sms.api.v2.freebaggage.FreeBaggageResponse;
import com.cleartrip.air.sms.api.v2.search.BaggageDetails;
import com.cleartrip.air.sms.api.v2.search.JourneyBaggageDetails;
import com.cleartrip.air.sms.api.v2.search.JourneyFareDetails;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.search.models.DTO.FareMetaInfoDTO;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.DTO.PromiseIdBasedBaggageConnectorRequest;
import com.cleartrip.supplier.search.models.cache.avail.AvailabilityCacheOutput;
import com.cleartrip.supplier.search.services.cachingService.repositry.AvailabilityCachingRepository;
import com.cleartrip.supplier.search.services.identification.SMSService;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Log4j2
public class CachedConnectorBaggageServiceImpl extends AbstractCacheBasedBaggageService {

    private final ExecutorService executor;
    private final SMSService smsService;
    private final AvailabilityCachingRepository cachingRepository;
    private final AbstractCacheBasedBaggageService cachedBaggageService;


    @Inject
    protected CachedConnectorBaggageServiceImpl(BaggageCacheKeyGenerator cacheKeyGenerator, BaggageCachingService cachingService,
                                                SMSService smsService,
                                                @Named("AvailabilityHotColdCacheRepoImpl") AvailabilityCachingRepository cachingRepository,
                                                CacheOnlyBaggageServiceImpl cacheOnlyBaggageService) {
        super(cacheKeyGenerator, cachingService);
        this.smsService = smsService;
        this.cachingRepository = cachingRepository;
        this.executor = Executors.newFixedThreadPool(10);
        this.cachedBaggageService = cacheOnlyBaggageService;
    }

    @Override
    public List<SolutionBaggageResponse> getBaggage(String requestId, BaggageCriteria criteria, List<SolutionInfo> solutionInfoList) {
        List<SolutionBaggageResponse> baggage = cachedBaggageService.getBaggage(requestId, criteria, solutionInfoList);
        log.info("cachedBaggageService.getBaggage {} ", baggage);
        if (Objects.nonNull(baggage) && !CollectionUtils.isEmpty(baggage) && passengerBaggageAvailable(baggage)) {
            return baggage;
        }
        List<SolutionBaggageResponse> solutionBaggageResponseList = new ArrayList<>();
        solutionInfoList.forEach(solutionInfo -> {
            List<FlightBaggage> flightBaggageList = new ArrayList<>();
            List<FreeBaggageResponse> smsRes = getSmsRes(solutionInfo);
            log.info("cachedBaggageService.getBaggage smsRes {} for solutionInfo {} ", smsRes, solutionInfo);
            AtomicInteger index = new AtomicInteger();
            solutionInfo.getFlightsList().forEach(flt -> {
                JourneyBaggageDetails journeyBaggageDetails = smsRes.size() > 1
                        ? smsRes.get(index.getAndIncrement()).getJourneyBaggageDetails().get(0)
                        : smsRes.get(0).getJourneyBaggageDetails().get(0);
                flightBaggageList.add(FlightBaggage.newBuilder()
                        .setOriginDestinationInfo(OriginDestinationInfoOuterClass.OriginDestinationInfo.newBuilder()
                                .setFrom(flt.getSegments(0).getDepartFromStop().getAirport())
                                .setTo(flt.getSegments(flt.getSegmentsList().size() - 1).getArriveAtStop().getAirport())
                                .build())
                        .addAllBaggageInfo(addSegmentBaggage(flt.getSegmentsList(), journeyBaggageDetails.getPassengerBaggageDetails()))
                        .build());
            });
            solutionBaggageResponseList.add(SolutionBaggageResponse.newBuilder()
                    .addAllFltBaggageDetails(flightBaggageList)
                    .build());
        });


        return solutionBaggageResponseList;

        /*long getStart = System.currentTimeMillis();
        Map<SolutionInfo, List<ImmutablePair<SolutionInfo, String>>> map =
                getSolutionInfoSolutionInfoComboFbcPairMap(solutionInfoList);
        Map<ImmutablePair<SolutionInfo, String>, String> solutionInfoPairCacheKeyMap =
                getSolutionInfoPairCacheKeyMap(criteria, getSolutionInfoPairsList(map));
        Map<String, SolutionBaggageResponse> cacheResponseMap =
                cachingService.getKVMap(solutionInfoPairCacheKeyMap.values().stream().collect(Collectors.toList()));
        Map<ImmutablePair<SolutionInfo, String>, SolutionBaggageResponse> solutionPairBaggageResponseMap = new HashMap<>();
        //Map<ImmutablePair<SolutionInfo, String>, FreeBaggageConnectorTask> taskMap = new HashMap<>();

        solutionInfoPairCacheKeyMap.forEach((pair, cacheKey) -> {
            SolutionBaggageResponse cachedResponse = cacheResponseMap.getOrDefault(cacheKey, null);
            if (cachedResponse != null) {
                solutionPairBaggageResponseMap.put(pair, cachedResponse);
                NewRelicUtil.sendBaggageEvent(requestId, BaggageSource.CACHE_ONLY, criteria, pair.getKey(), pair.getValue(),
                        cacheKey, solutionPairBaggageResponseMap.get(pair), System.currentTimeMillis() - getStart, null, getStart);
            } *//*else {
                FreeBaggageConnectorTask task = new FreeBaggageConnectorTask(BaggageConnectorTaskInfo.builder()
                        .comboFbc(pair.getValue())
                        .criteria(criteria)
                        .solutionInfo(pair.getKey())
                        .cacheKey(cacheKey).build());
                taskMap.put(pair, task);
            }*//*
        });
        //solutionPairBaggageResponseMap.putAll(getBaggageFromConnector(taskMap));
        return prepareSolutionBaggageResponse(map, solutionPairBaggageResponseMap);*/
    }

    private boolean passengerBaggageAvailable(List<SolutionBaggageResponse> baggages) {
        return baggages.stream().allMatch(baggage -> baggage.getFltBaggageDetailsList().stream().
                allMatch(fltBaggage -> fltBaggage.getBaggageInfoList().stream()
                        .allMatch(baggageInfo -> baggageInfo.getSegFareFamilyBaggageList().stream()
                                .allMatch(segmentFareFamilyBaggage -> segmentFareFamilyBaggage.getPaxBaggageList().stream()
                                        .allMatch(paxBaggage -> CollectionUtils.isNotEmpty(paxBaggage.getBaggageList()))))));
    }

    private List<SegmentBaggage> addSegmentBaggage(List<FlightSegment> segmentsList,
                                                   List<JourneyFareDetails.PassengerBaggageDetails> passengerBaggageDetails) {
        return segmentsList.stream().map(seg -> {
            return SegmentBaggage.newBuilder()
                    .setArriveAtStop(seg.getArriveAtStop())
                    .setDepartFromStop(seg.getDepartFromStop())
                    .addSegFareFamilyBaggage(SegmentFareFamilyBaggage.newBuilder()
                            .addAllPaxBaggage(fetchPaxBaggage(passengerBaggageDetails))
                            .build())
                    .build();
        }).collect(Collectors.toList());
    }

    private Iterable<PaxBaggage> fetchPaxBaggage(List<JourneyFareDetails.PassengerBaggageDetails> passengerBaggageDetails) {
        return passengerBaggageDetails.stream()
                .map(paxBag -> PaxBaggage.newBuilder()
                        .setPaxType(mapPaxType(paxBag.getPassengerType()))
                        .setFreeText(paxBag.getBaggageDetails().get(0).getDescription())
                        .addAllBaggage(fetchBaggage(paxBag.getBaggageDetails()))
                        .build()).collect(Collectors.toList());
    }

    private Iterable<Baggage> fetchBaggage(List<BaggageDetails> baggageDetails) {
        return baggageDetails.stream()
                .map(bd -> Baggage.newBuilder()
                        .setBaggageType(mapBaggageType(bd.getBaggageType()))
                        .addBaggageAllowances(BaggageAllowance.newBuilder()
                                .setDescription(bd.getDescription())
                                .setQty(bd.getQuantity())
                                .setUnit(mapUnit(bd.getUnit()))
                                .build())
                        .build())
                .collect(Collectors.toList());
    }

    private BaggageUnit mapUnit(BaggageDetails.Unit unit) {
        switch (unit) {
            case KG:
                return BaggageUnit.KG;
            case PIECE:
                return BaggageUnit.PIECE;
            case POUND:
                return BaggageUnit.POUND;
        }
        return BaggageUnit.KG;
    }

    private BaggageType mapBaggageType(BaggageDetails.BaggageType baggageType) {
        switch (baggageType) {
            case HAND_BAGGAGE:
                return BaggageType.BAGGAGE_CABIN;
            case CHECKIN_BAGGAGE:
                return BaggageType.BAGGAGE_CHECK_IN;
            case ADDON:
                return BaggageType.BAGGAGE_ADDON;
        }
        return BaggageType.BAGGAGE_UNKNOWN;
    }

    private PaxTypeOuterClass.PaxType mapPaxType(Traveller.TravellerType passengerType) {
        switch (passengerType) {
            case ADULT:
                return PaxTypeOuterClass.PaxType.ADULT;
            case INFANT:
                return PaxTypeOuterClass.PaxType.INFANT;
            case CHILD:
                return PaxTypeOuterClass.PaxType.CHILD;
        }
        return PaxTypeOuterClass.PaxType.ADULT;
    }

    @NotNull
    private List<FreeBaggageResponse> getSmsRes(SolutionInfo solutionInfo) {
        return solutionInfo.getSelectedSolutionList().stream().map(selectedSolution -> {
            AvailabilityCacheOutput solutionWithFare = cachingRepository
                    .getFlightSolutionWithFare(selectedSolution.getSolutionId().split("~")[0],
                            selectedSolution.getSolutionId().split("~")[1],
                            selectedSolution.getComboFbc());
            FlightSolutionDTO flightSolutionDTO = solutionWithFare.getFlightSolutions().values().stream().findFirst()
                    .orElseThrow(() -> new RuntimeException("No cache present for the " + selectedSolution.getSolutionId() + selectedSolution.getComboFbc()));
            FareMetaInfoDTO fareMetaInfoDTO = flightSolutionDTO
                    .getFareFamilyDTO().get(0).getFareMetaInfoDTO();
            //Call SMS
            PromiseIdBasedBaggageConnectorRequest request = PromiseIdBasedBaggageConnectorRequest.builder()
                    .credential(flightSolutionDTO.getSolutionMetaInfoDTO().getSupplierInfoDTO().getCredentialKey())
                    .supplier(flightSolutionDTO.getSolutionMetaInfoDTO().getSupplierInfoDTO().getSupplier())
                    .fareId(fareMetaInfoDTO.getFareId())
                    .promiseId(flightSolutionDTO.getSolutionMetaInfoDTO().getPromiseId()).build();
            return smsService.getBaggageDataUsingPromiseId(request);
        }).collect(Collectors.toList());
    }

   /* private Map<ImmutablePair<SolutionInfo, String>, SolutionBaggageResponse> getBaggageFromConnector
            (Map<ImmutablePair<SolutionInfo, String>, FreeBaggageConnectorTask> taskMap) {

        long getStart = System.currentTimeMillis();
        Map<ImmutablePair<SolutionInfo, String>, CompletableFuture<Void>> completableFutureMap = new ConcurrentHashMap<>();
        taskMap.forEach((pair, task) -> completableFutureMap.put(pair, CompletableFuture.runAsync(task, executor)));

        Map<ImmutablePair<SolutionInfo, String>, SolutionBaggageResponse> responseMap = new ConcurrentHashMap<>();
        completableFutureMap.forEach((pair, completableFuture) -> {
            SolutionBaggageResponse response = null;
            try {
                completableFuture.get();
                response = taskMap.get(pair).getSolutionBaggageResponse();
                NewRelicUtil.sendBaggageEvent(ThreadContext.get(REQUEST_ID), BaggageSource.CACHED_CONNECTOR, taskMap.get(pair).getBaggageConnectorTaskInfo().getCriteria(),
                        taskMap.get(pair).getBaggageConnectorTaskInfo().getSolutionInfo(), taskMap.get(pair).getBaggageConnectorTaskInfo().getComboFbc(),
                        taskMap.get(pair).getCacheKey(), taskMap.get(pair).getSolutionBaggageResponse() , System.currentTimeMillis()-getStart, null, getStart);

            } catch (InterruptedException | ExecutionException e) {
                log.error("Exception in getting response from connector " + e);
                NewRelicUtil.sendBaggageEvent(ThreadContext.get(REQUEST_ID), BaggageSource.CACHED_CONNECTOR, taskMap.get(pair).getBaggageConnectorTaskInfo().getCriteria(),
                        taskMap.get(pair).getBaggageConnectorTaskInfo().getSolutionInfo(), taskMap.get(pair).getBaggageConnectorTaskInfo().getComboFbc(),
                        taskMap.get(pair).getCacheKey(),taskMap.get(pair).getSolutionBaggageResponse(),System.currentTimeMillis()-getStart, e.getMessage(), getStart);

            }
            responseMap.put(pair, response);
        });
        return responseMap;
    }*/
}