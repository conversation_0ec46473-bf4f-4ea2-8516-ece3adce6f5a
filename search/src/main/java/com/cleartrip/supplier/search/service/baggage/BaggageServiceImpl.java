package com.cleartrip.supplier.search.service.baggage;

import com.air.sis.rule.BaggageSourceAndConfigRuleEngine;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.newrelic.NewRelicUtil;
import com.cleartrip.supplier.search.enums.BaggageSource;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.exception.ValidationException;
import com.cleartrip.supplier.search.ruleEngine.RuleEngineWrapper;
import com.cleartrip.supplier.search.service.BaggageService;
import com.google.common.collect.Lists;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.log4j.Log4j2;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Log4j2
public class BaggageServiceImpl implements BaggageService {

    public static final String DOMESTIC = "DOM";
    public static final String INTERNATIONAL = "INTL";
    public static final String DEFAULT = "DEFAULT";

    private final BaggageServiceFactory baggageServiceFactory;
    private final RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> baggageRuleEngine;

    private final ExecutorService executorService;

    @Inject
    public BaggageServiceImpl(BaggageServiceFactory baggageServiceFactory,
                              @Named("baggageSourceAndConfigRuleEngine") RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> baggageRuleEngine,
                              @Named("baggageExecutorService") ExecutorService executorService) {
        this.baggageServiceFactory = baggageServiceFactory;
        this.baggageRuleEngine = baggageRuleEngine;
        this.executorService = executorService;
    }

    @Override
    public BaggageResponse getBaggage(BaggageRequest baggageRequest) throws ValidationException {
        Map<BaggageSource, List<SolutionInfo>> sourceSolutionsMap = groupSolutionsToBaggageSource(baggageRequest);
        BaggageResponse.Builder builder = BaggageResponse.newBuilder().setRequestId(baggageRequest.getRequestId());
        sourceSolutionsMap.forEach((source, solutionsList) -> {
            log.debug("getBaggage request source identified as {} with solutionCount {}", source,
                    solutionsList.size());
            List<SolutionBaggageResponse> responseList = baggageServiceFactory.get(source)
                    .getBaggage(baggageRequest.getRequestId(), baggageRequest.getFreeBaggageInfoCriteria(), solutionsList);
            responseList = updateStopSolutionBaggageRes(solutionsList, responseList);
            builder.addAllSolutionBaggageResponse(responseList);
            NewRelicUtil.sendBaggageSupplierStrategy(baggageRequest.getRequestId(), source, baggageRequest.getFreeBaggageInfoCriteria(), solutionsList.get(0));
        });
        return builder.build();
    }

    @Override
    public BaggageSource getBaggageSource(Supplier supplier, boolean isInternational, FareGroupInfo fareGroupInfo) {
        BaggageSource baggageSource = BaggageSource.valueOf(baggageRuleEngine.getRuleEngine()
                .getBaggageStrategy(isInternational ? INTERNATIONAL : DOMESTIC,
                        supplier.toString(),
                        fareGroupInfo.name()));

        log.info("Fetching baggage data from {} for supplier {}", baggageSource, supplier);

        return baggageSource;
    }

    private Map<BaggageSource, List<SolutionInfo>> groupSolutionsToBaggageSource(BaggageRequest baggageRequest) {
        return baggageRequest.getSolutionsList().stream()
                .collect(Collectors.groupingBy(solution -> getBaggageSource(Supplier.valueOf(solution.getSupplierInfo().getSupplier()),
                        baggageRequest.getFreeBaggageInfoCriteria().getIsInternational(),
                        solution.getFareGroupInfo())));
    }

    @NotNull
    private List<SolutionBaggageResponse> updateStopSolutionBaggageRes(List<SolutionInfo> solutionsList, List<SolutionBaggageResponse> responseList) {
        try {
            List<SolutionBaggageResponse> updatedList = Lists.newArrayList();
            int index = 0;
            for (SolutionBaggageResponse solutionBaggageResponse : responseList) {
                SolutionInfo solutionInfo = solutionsList.get(index++);
                if (isTechnicalStopSol(solutionInfo)) {
                    List<FlightBaggage> flightBaggageList = solutionBaggageResponse.getFltBaggageDetailsList().stream().map(flt -> {
                        FlightsInfo flightsInfo = filterFlightInfo(solutionInfo, flt.getOriginDestinationInfo());
                        SegmentBaggage baggageInfo = flt.getBaggageInfo(0); /*getting any baggage segment*/
                        List<SegmentBaggage> segmentBaggageList = flightsInfo.getSegmentsList().stream().map(seg -> SegmentBaggage
                                .newBuilder(baggageInfo)
                                .setDepartFromStop(seg.getDepartFromStop())
                                .setArriveAtStop(seg.getArriveAtStop())
                                .build()).collect(Collectors.toList());
                        return FlightBaggage
                                .newBuilder(flt)
                                .clearBaggageInfo()
                                .addAllBaggageInfo(segmentBaggageList)
                                .build();
                    }).collect(Collectors.toList());
                    SolutionBaggageResponse baggageResponse = SolutionBaggageResponse.newBuilder(solutionBaggageResponse)
                            .clearFltBaggageDetails()
                            .addAllFltBaggageDetails(flightBaggageList)
                            .build();
                    updatedList.add(baggageResponse);
                } else {
                    updatedList.add(solutionBaggageResponse);
                }
            }
            return updatedList;
        } catch (Exception e) {
            log.error("Failed to update the baggage for stop due to the {}", e.getMessage(), e);
        }
        return responseList;
    }

    private FlightsInfo filterFlightInfo(SolutionInfo solutionInfo, OriginDestinationInfoOuterClass.OriginDestinationInfo originDestinationInfo) {
        return solutionInfo.getFlightsList().stream().filter(flt ->
                        flt.getSegmentsList().get(0).getDepartFromStop().getAirport().equalsIgnoreCase(originDestinationInfo.getFrom())
                                && flt.getSegmentsList().get(flt.getSegmentsList().size() - 1).getArriveAtStop().getAirport().equalsIgnoreCase(originDestinationInfo.getTo()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Unable to filter the flight with info -> " + originDestinationInfo.toString()));
    }

    private boolean isTechnicalStopSol(SolutionInfo solutionInfo) {
        List<String> flightList = solutionInfo.getFlightsList().stream().flatMap(flt -> flt.getSegmentsList().stream().map(seg -> seg.getFlightDetails().getFlightNumber() + seg.getFlightDetails().getAirline())).collect(Collectors.toList());
        Set<String> flightSet = new HashSet<>(flightList);
        return flightList.size() != flightSet.size();
    }

    @Override
    public List<BaggageResponse> getBulkBaggageResponse(List<BaggageRequest> baggageRequests) {
        List<BaggageResponse> responses = new ArrayList<>();
        try {
            List<CompletableFuture<BaggageResponse>> futures = baggageRequests.stream()
                    .map(baggageRequest -> CompletableFuture.supplyAsync(
                            () -> handleBaggageRequestSafely(baggageRequest),
                            executorService
                    ))
                    .collect(Collectors.toList());

            for (CompletableFuture<BaggageResponse> future : futures) {
                try {
                    BaggageResponse response = future.join();
                    responses.add(response);
                } catch (CompletionException e) {
                    log.error("Error occurred while processing baggage request", e.getCause());
                } catch (Exception e) {
                    log.error("Unexpected error while joining CompletableFuture", e);
                }
            }
        } catch (Exception e){
            log.error("unhandled error in bulk baggage",e);
        }
        return responses;
    }
    private BaggageResponse handleBaggageRequestSafely(BaggageRequest baggageRequest) {
        try {
            return getBaggage(baggageRequest);
        } catch (ValidationException e) {
            throw new RuntimeException("Baggage request failed", e);
        }
    }
}

