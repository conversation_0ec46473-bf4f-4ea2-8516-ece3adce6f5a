package com.cleartrip.supplier.search.workflow.tasks.execution;

import com.cleartrip.air.sms.api.request.search.SingleSolutionSearchRequest;
import com.cleartrip.air.sms.api.singleSearch.SingleSolutionSearchResponse;
import com.cleartrip.air.sms.api.v2.Response;
import com.cleartrip.supplier.web.sms.SMSClientWrapper;
import com.cleartrip.utility.workflow.design.Task;
import com.fasterxml.jackson.databind.ObjectMapper;
import common.clone.ObjectMapperCloner;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PerformSingleSolutionSearchCallTask implements Task<SingleSolutionSearchRequest, Response<SingleSolutionSearchResponse>> {

    private final SMSClientWrapper smsClientWrapper;

    public PerformSingleSolutionSearchCallTask(SMSClientWrapper smsClientWrapper) {
        this.smsClientWrapper = smsClientWrapper;
    }

    @Override
    public Response<SingleSolutionSearchResponse> run(SingleSolutionSearchRequest singleSolutionSearchRequest) {
        ObjectMapper obj = new ObjectMapper();
        try {
            Response<SingleSolutionSearchResponse> response = smsClientWrapper.singleSolutionSearch(singleSolutionSearchRequest);
            return response;
        } catch (Exception e) {
            log.error("Not able to perform ss1 for supplier {}", singleSolutionSearchRequest.getAirSupplier().name());
            log.error("Exception occurred while calling sms for Single solution Search", e);
            throw new RuntimeException(e);
        }
    }

}
