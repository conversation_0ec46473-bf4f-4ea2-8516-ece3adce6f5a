package com.cleartrip.supplier.search.util;

import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.UnifiedFareCategory;
import com.cleartrip.supplier.inventory.protos.v1.CabinType;
import com.cleartrip.supplier.inventory.protos.v1.FareCategoryType;
import com.cleartrip.supplier.inventory.protos.v1.FareGroupInfo;
import com.cleartrip.supplier.inventory.protos.v1.SourceTypeOuterClass;
import com.cleartrip.supplier.search.config.suppliercodes.*;
import com.cleartrip.supplier.search.constant.SisConfigKeys;
import com.cleartrip.supplier.search.data.Credential;
import com.cleartrip.supplier.search.entity.SupplierFareGroupInfoConfig;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.exception.ConfigurationLoadException;
import com.cleartrip.supplier.search.exception.JsonParseRuntimeException;
import com.cleartrip.supplier.search.models.FlightSearchCriteriaDTO;
import com.cleartrip.supplier.search.models.idetification.ProductClassFareTypeIdentificationRequest;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import com.cleartrip.supplier.search.workflow.tasks.execution.TaskJourneyType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.scheduling.annotation.Scheduled;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cleartrip.supplier.search.constant.SisConfigKeys.CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS;



@Log4j2
public class SupplierFareCodesConfigUtilV2 {
    private static Map<Supplier, CabinToSectorConfigContainer> supplierFareCodesMap;
    private static Map<Supplier, List<SourceTypeOuterClass.SourceType>> promoEnabledChannels;
    private final SearchConfigContainer properties;
    private final ObjectMapper objectMapper;
    private static Map<Supplier, SupplierFareGroupInfoConfig> supplierEnabledFareGroupsConfigs;

    @Inject
    public SupplierFareCodesConfigUtilV2(SearchConfigContainer properties, ObjectMapper objectMapper) throws ConfigurationLoadException {
        this.properties = properties;
        this.objectMapper = objectMapper;
        supplierFareCodesMap = initialiseSupplierFareCodeMap();
        promoEnabledChannels = new HashMap<>();
        initializeSupplierEnabledFareGroupsMap();
    }

    private Map<Supplier, CabinToSectorConfigContainer> initialiseSupplierFareCodeMap() throws ConfigurationLoadException {
        log.info("scheduler for cabinToSectorConfig Refresh");
        String propertyValue = properties.getPropertyValue(SisConfigKeys.SMS_SUPPLIER_FARE_CODES);
        try {
            List<SupplierConfigFareCodes> supplierConfigFareCodes = objectMapper.readValue(propertyValue, new TypeReference<>() {
            });
            return supplierConfigFareCodes.stream().collect(Collectors.toMap(SupplierConfigFareCodes::getSupplier,
                    s -> new CabinToSectorConfigContainer(cabinTypeTripConfigContainerMap(s))));
        } catch (JsonProcessingException e) {
            log.error("Failed to read the config key: {} value: {} due to the {}",
                    SisConfigKeys.SUPPLIER_CATEGORY_CONFIG_KEY, propertyValue, e.getMessage(), e);
            throw new JsonParseRuntimeException(propertyValue, e);
        } catch (Exception e) {
            log.error("Error loading Configuration with key {} due to the {}",
                    SisConfigKeys.SMS_SUPPLIER_FARE_CODES, e);
            throw new ConfigurationLoadException( "Error loading Configuration with key : " + SisConfigKeys.SMS_SUPPLIER_FARE_CODES,e);
        }
    }

    private Map<Supplier, List<SourceTypeOuterClass.SourceType>> initialisePromoCodedMap() throws ConfigurationLoadException {
        log.info("scheduler for promoCodeMap Refresh");
        String propertyValue = properties.getPropertyValue(SisConfigKeys.SMS_SUPPLIER_PROMO_CODES);
        try {
            return objectMapper.readValue(propertyValue, new TypeReference<Map<Supplier, List<SourceTypeOuterClass.SourceType>>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Failed to read the config key: {} value: {} due to the {}",
                    SisConfigKeys.SMS_SUPPLIER_PROMO_CODES, propertyValue, e.getMessage(), e);
            throw new JsonParseRuntimeException(propertyValue, e);
        } catch (Exception e) {
            log.error("Error loading Configuration with key {} due to the {}",
                    SisConfigKeys.SMS_SUPPLIER_PROMO_CODES, e);
            throw new ConfigurationLoadException( "Error loading Configuration with key : " + SisConfigKeys.SMS_SUPPLIER_PROMO_CODES,e);
        }
    }

    public Optional<FareGroupInfo> getFareGroupInfo(Supplier supplier, String productClass) {
        //TODO: anyMatch has been implemented for now but needs to be changed later on as this might cause some problems when number of ff suppliers increase.
        return Optional.ofNullable(supplierFareCodesMap.get(supplier))
                .flatMap(fareCode -> fareCode.getCabinConfigMap().values().stream()
                        .flatMap(val -> val.getTripConfigMap().values().stream()
                                .flatMap(val1 -> val1.getJourneyTypeListMap().values().stream()
                                        .flatMap(val2 -> val2.stream()
                                                .flatMap(val3 -> val3.getProductClasses().stream()
                                                        .filter(val4 -> val4.getFareCodes().stream().anyMatch(productClass::contains))))))
                        .findFirst().map(FareSubTypeCodeConfig::getFareGroupInfo));
    }

    public List<String> getProductClasses(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        //TODO needs to for corp

        Optional<SourceFareCodeConfig> sourceConfig;
        List<FareGroupInfo> requiredFareFamiliesList = getRequiredFareFamiliesList(workFlowTaskAvailSearchRequest);

        List<SourceFareCodeConfig> sourceFareCodeConfigs = getSourceConfigs(workFlowTaskAvailSearchRequest);

        if(workFlowTaskAvailSearchRequest.getFareCategoryType() == FareCategoryType.SFF
            || workFlowTaskAvailSearchRequest.getUnifiedFareCategory() == UnifiedFareCategory.SFF)
        {
            sourceConfig = filterSFFFareType(sourceFareCodeConfigs);
        }
        //NOTE: We don't have any corporate fares so this is not in use
//        else if (isRetailOrCorpCreds(workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().getCredential())) {
//            sourceConfig = filterCredsFareTypes(workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().getCredential().getCredentialsCommission().get(),
//                    sourceFareCodeConfigs).map(c -> c);
//        }
        else {
            sourceConfig = filterChannelFareTypes(workFlowTaskAvailSearchRequest.getCompanyInfoDTO().getSourceType(),
                    sourceFareCodeConfigs).map(c -> c);
        }
        return filterByFareCodeType(sourceConfig.map(SourceFareCodeConfig::getProductClasses)
                .orElse(Collections.emptyList()), requiredFareFamiliesList);
    }

    public List<String> getProductClasses(ProductClassFareTypeIdentificationRequest request) {

        Optional<SourceFareCodeConfig> sourceConfig;
        List<FareGroupInfo> requiredFareFamiliesList = getRequiredFareFamiliesList(request.getFareGroupInfos(), request.getRequiredSectors(), request.getSupplier());

        List<SourceFareCodeConfig> sourceFareCodeConfigs = getSourceConfigs(request.getSupplier(), request.isIntl(), request.getTaskJourneyType(), request.getCabinType());

        if(request.getFareCategoryType() == FareCategoryType.SFF)
        {
            sourceConfig = filterSFFFareType(sourceFareCodeConfigs);
        }
        //NOTE: We don't have any corporate fares so this is not in use
//        else if (isRetailOrCorpCreds(request.getCredential())) {
//            sourceConfig = filterCredsFareTypes(request.getCredential().getCredentialsCommission().orElse(""), sourceFareCodeConfigs).map(c -> c);
         else {
            sourceConfig = filterChannelFareTypes(request.getSourceType(), sourceFareCodeConfigs).map(c -> c);
        }
        return filterByFareCodeType(sourceConfig.map(SourceFareCodeConfig::getProductClasses)
                .orElse(Collections.emptyList()), requiredFareFamiliesList);
    }

    @NotNull
    private List<FareGroupInfo> getRequiredFareFamiliesList(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        List<FareGroupInfo> requiredFareFamiliesList = new ArrayList<>(workFlowTaskAvailSearchRequest.getCustomerSearchCriteriaDTO().getSearchOptions().getFareGroupInfo());
        requiredFareFamiliesList.retainAll(getEnabledFareGroups(workFlowTaskAvailSearchRequest.getSupplierCarrierPair().getSupplier(), workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO()));
        if(CollectionUtils.isEmpty(requiredFareFamiliesList)){
            return Collections.singletonList(FareGroupInfo.REGULAR);
        }
        return requiredFareFamiliesList;
    }

    private List<FareGroupInfo> getRequiredFareFamiliesList(List<FareGroupInfo> fareGroupInfos, Set<String> requiredSectors, Supplier supplier) {
        List<FareGroupInfo> list = new ArrayList<>();
        list.addAll(fareGroupInfos);

        Set<FareGroupInfo> enabledFareGroups = getEnabledFareGroups(supplier,requiredSectors);

        //NOTE: This function is keeping the intersection of the data. We want the Union
//        list.retainAll(getEnabledFareGroups(supplier, requiredSectors));
        list = Stream.concat(list.stream(),enabledFareGroups.stream())
                .distinct()
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            return Collections.singletonList(FareGroupInfo.REGULAR);
        }
        return list;
    }

    public List<String> getFareTypes(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        Optional<SourceFareCodeConfig> sourceConfig;
        List<FareGroupInfo> requiredFareFamiliesList = getRequiredFareFamiliesList(workFlowTaskAvailSearchRequest);

        List<SourceFareCodeConfig> sourceFareCodeConfigs = getSourceConfigs(workFlowTaskAvailSearchRequest);
        if(workFlowTaskAvailSearchRequest.getFareCategoryType() == FareCategoryType.SFF
            || workFlowTaskAvailSearchRequest.getUnifiedFareCategory() == UnifiedFareCategory.SFF)
        {
            sourceConfig = filterSFFFareType(sourceFareCodeConfigs);
        }
        //NOTE: We don't have any corporate fares so this is not in use
//        else if (isRetailOrCorpCreds(workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().getCredential())) {
//            sourceConfig = filterCredsFareTypes(workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().getCredential().getCredentialsCommission().get(),
//                    sourceFareCodeConfigs).map(c -> c);
//        }
        else {
            sourceConfig = filterChannelFareTypes(workFlowTaskAvailSearchRequest.getCompanyInfoDTO().getSourceType(),
                    sourceFareCodeConfigs).map(c -> c);
        }

        return filterByFareCodeType(sourceConfig.map(SourceFareCodeConfig::getFareTypes)
                .orElse(Collections.emptyList()), requiredFareFamiliesList);
    }

    public List<String> getFareTypes(ProductClassFareTypeIdentificationRequest request) {
        Optional<SourceFareCodeConfig> sourceConfig;
        List<FareGroupInfo> requiredFareFamiliesList = getRequiredFareFamiliesList(request.getFareGroupInfos(), request.getRequiredSectors(), request.getSupplier());

        List<SourceFareCodeConfig> sourceFareCodeConfigs = getSourceConfigs(request.getSupplier(), request.isIntl(), request.getTaskJourneyType(), request.getCabinType());

        if(request.getFareCategoryType() == FareCategoryType.SFF)
        {
            sourceConfig = filterSFFFareType(sourceFareCodeConfigs);
        }

        //NOTE: We don't have any corporate fares so this is not in use
//        else if (isRetailOrCorpCreds(request.getCredential())) {
//            sourceConfig = filterCredsFareTypes(request.getCredential().getCredentialsCommission().orElse(""), sourceFareCodeConfigs).map(c -> c);
//        }
        else {
            sourceConfig = filterChannelFareTypes(request.getSourceType(), sourceFareCodeConfigs).map(c -> c);
        }

        return filterByFareCodeType(sourceConfig.map(SourceFareCodeConfig::getFareTypes)
                .orElse(Collections.emptyList()), requiredFareFamiliesList);
    }

    public Optional<String> getPromoCode(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        List<SourceTypeOuterClass.SourceType> enabledChannels = promoEnabledChannels.get(workFlowTaskAvailSearchRequest.getSupplierCarrierPair().getSupplier());
        if (CollectionUtils.isNotEmpty(enabledChannels)
                && enabledChannels.contains(workFlowTaskAvailSearchRequest.getCompanyInfoDTO().getSourceType())
                && isNonRetailAndCorpCreds(workFlowTaskAvailSearchRequest.getCredential())) {
            return Optional.of(workFlowTaskAvailSearchRequest.getCredential().getCredentialsCommission().get());
        }
        //TODO flow for CORP
        return Optional.empty();
    }


    @NotNull
    private List<String> filterByFareCodeType(List<FareSubTypeCodeConfig> fareSubTypeCodeConfigList, List<FareGroupInfo> fareCodeType) {
        return fareCodeType.stream().flatMap(fct -> fareSubTypeCodeConfigList.stream().filter(ft -> ft.getFareGroupInfo().equals(fct))).
                flatMap(fc -> fc.getFareCodes().stream()).collect(Collectors.toList());
    }


    private Map<String, SectorToJoruneyConfigContainer> cabinTypeTripConfigContainerMap(SupplierConfigFareCodes supplierConfigFareCodes) {
        return supplierConfigFareCodes.getCabinFareCodesConfig().stream().collect(Collectors.toMap(CabinFareCodesConfig::getCabinType,
                cc -> new SectorToJoruneyConfigContainer(tripTypeJourneyConfigMap(cc))));
    }

    private Map<SectorFareCodeConfig.SectorType, JourneyToFareConfigContainer> tripTypeJourneyConfigMap(CabinFareCodesConfig cabinFareCodesConfig) {
        return cabinFareCodesConfig.getSectorFareCodeConfig().stream().collect(Collectors.toMap(SectorFareCodeConfig::getSectorType,
                tc -> new JourneyToFareConfigContainer(tc.getJourneyConfig().stream()
                        .collect(Collectors.toMap(JourneyFareCodeConfig::getJourneyType,
                                JourneyFareCodeConfig::getChannelConfig)))));
    }

    private List<SourceFareCodeConfig> getSourceConfigs(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        CabinToSectorConfigContainer cabinToSectorConfigContainer = supplierFareCodesMap
                .get(workFlowTaskAvailSearchRequest.getSupplierCarrierPair().getSupplier());

        if (Objects.isNull(cabinToSectorConfigContainer)
                || MapUtils.isEmpty(cabinToSectorConfigContainer.getCabinConfigMap())) {
            return Collections.emptyList();
        }

        SectorFareCodeConfig.SectorType tripType = workFlowTaskAvailSearchRequest.getCustomerSearchCriteriaDTO().isIntl()
                ? SectorFareCodeConfig.SectorType.INTL
                : SectorFareCodeConfig.SectorType.DOM;
        JourneyFareCodeConfig.JourneyType journeyType = TaskJourneyType.ROUNDTRIP.equals(workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().getTaskJourneyType())
                ? JourneyFareCodeConfig.JourneyType.ROUNDTRIP
                : JourneyFareCodeConfig.JourneyType.ONEWAY;

        Map<String, SectorToJoruneyConfigContainer> cabinConfig = cabinToSectorConfigContainer
                .getCabinConfigMap();
        SectorToJoruneyConfigContainer sectorToJoruneyConfigContainer = cabinConfig
                .getOrDefault(workFlowTaskAvailSearchRequest.getCustomerSearchCriteriaDTO().getCabinType().name(), cabinConfig.get("DEFAULT"));
        if (Objects.isNull(sectorToJoruneyConfigContainer)
                || MapUtils.isEmpty(sectorToJoruneyConfigContainer.getTripConfigMap())) {
            return Collections.emptyList();
        }

        Map<SectorFareCodeConfig.SectorType, JourneyToFareConfigContainer> journeyToFareConfigContainerMap = sectorToJoruneyConfigContainer.getTripConfigMap();
        JourneyToFareConfigContainer journeyToFareConfigContainer = journeyToFareConfigContainerMap
                .getOrDefault(tripType,journeyToFareConfigContainerMap.get(SectorFareCodeConfig.SectorType.DEFAULT));
        if (Objects.isNull(journeyToFareConfigContainer)
                || MapUtils.isEmpty(journeyToFareConfigContainer.getJourneyTypeListMap())) {
            return Collections.emptyList();
        }

        Map<JourneyFareCodeConfig.JourneyType, List<SourceFareCodeConfig>> journeyTypeListMap = journeyToFareConfigContainer.getJourneyTypeListMap();
        List<SourceFareCodeConfig> sourceFareCodeConfigs = journeyTypeListMap.getOrDefault(journeyType, journeyTypeListMap.get(JourneyFareCodeConfig.JourneyType.DEFAULT));
        if (Objects.isNull(sourceFareCodeConfigs)
                || CollectionUtils.isEmpty(sourceFareCodeConfigs)) {
            return Collections.emptyList();
        }

        return sourceFareCodeConfigs;
    }

    private List<SourceFareCodeConfig> getSourceConfigs(Supplier supplier, boolean isIntl,
                                                        TaskJourneyType taskJourneyType, CabinType cabinType) {
        CabinToSectorConfigContainer cabinToSectorConfigContainer = supplierFareCodesMap
                .get(supplier);
        if (Objects.isNull(cabinToSectorConfigContainer)
                || MapUtils.isEmpty(cabinToSectorConfigContainer.getCabinConfigMap())) {
            return Collections.emptyList();
        }

        SectorFareCodeConfig.SectorType tripType = isIntl
                ? SectorFareCodeConfig.SectorType.INTL
                : SectorFareCodeConfig.SectorType.DOM;
        JourneyFareCodeConfig.JourneyType journeyType = TaskJourneyType.ROUNDTRIP.equals(taskJourneyType)
                ? JourneyFareCodeConfig.JourneyType.ROUNDTRIP
                : JourneyFareCodeConfig.JourneyType.ONEWAY;

        Map<String, SectorToJoruneyConfigContainer> cabinConfig = cabinToSectorConfigContainer
                .getCabinConfigMap();
        SectorToJoruneyConfigContainer sectorToJoruneyConfigContainer = cabinConfig
                .getOrDefault(cabinType.name(), cabinConfig.get("DEFAULT"));
        if (Objects.isNull(sectorToJoruneyConfigContainer)
                || MapUtils.isEmpty(sectorToJoruneyConfigContainer.getTripConfigMap())) {
            return Collections.emptyList();
        }

        Map<SectorFareCodeConfig.SectorType, JourneyToFareConfigContainer> journeyToFareConfigContainerMap = sectorToJoruneyConfigContainer.getTripConfigMap();
        JourneyToFareConfigContainer journeyToFareConfigContainer = journeyToFareConfigContainerMap
                .getOrDefault(tripType, journeyToFareConfigContainerMap.get(SectorFareCodeConfig.SectorType.DEFAULT));
        if (Objects.isNull(journeyToFareConfigContainer)
                || MapUtils.isEmpty(journeyToFareConfigContainer.getJourneyTypeListMap())) {
            return Collections.emptyList();
        }

        Map<JourneyFareCodeConfig.JourneyType, List<SourceFareCodeConfig>> journeyTypeListMap = journeyToFareConfigContainer.getJourneyTypeListMap();
        List<SourceFareCodeConfig> sourceFareCodeConfigs = journeyTypeListMap.getOrDefault(journeyType, journeyTypeListMap.get(JourneyFareCodeConfig.JourneyType.DEFAULT));
        if (Objects.isNull(sourceFareCodeConfigs)
                || CollectionUtils.isEmpty(sourceFareCodeConfigs)) {
            return Collections.emptyList();
        }

        return sourceFareCodeConfigs;
    }


    @NotNull
    private Optional<ChannelSourceFareCodeConfig> filterChannelFareTypes(SourceTypeOuterClass.SourceType sourceType,
                                                                         List<SourceFareCodeConfig> sourceFareCodeConfigs) {
        return sourceFareCodeConfigs.stream().filter(sourceConfig -> SourceFareCodeConfig.Type.CHANNEL.equals(sourceConfig.getType()))
                .map(sourceConfig -> (ChannelSourceFareCodeConfig) sourceConfig)
                .filter(channelSourceConfig -> channelSourceConfig.getChannel().equals(sourceType))
                .findFirst();
    }

    @NotNull
    private Optional<CredSourceFareCodeConfig> filterCredsFareTypes(String credSearchType,
                                                                    List<SourceFareCodeConfig> sourceFareCodeConfigs) {
        return sourceFareCodeConfigs.stream()
                .filter(sourceConfig -> SourceFareCodeConfig.Type.CRED_SEARCH_TYPE.equals(sourceConfig.getType()))
                .map(sourceConfig -> (CredSourceFareCodeConfig) sourceConfig)
                .filter(credSourceConfig -> credSourceConfig.getCredSearchType().getDisplayName().equalsIgnoreCase(credSearchType))
                .findFirst();
    }

    @NotNull
    private Optional<SourceFareCodeConfig> filterSFFFareType(List<SourceFareCodeConfig> sourceFareCodeConfigs) {
        return sourceFareCodeConfigs.stream()
                .filter(sourceConfig -> SourceFareCodeConfig.Type.SFF_SEARCH_TYPE.equals(sourceConfig.getType()))
                .findFirst();
    }

    //NOTE: We don't have any corporate fares so this is not in use
    private boolean isRetailOrCorpCreds(Credential credential) {
        return credential.getCredentialsCommission().isPresent()
                && (credential.isRetailCommission() || credential.isCorpCommission());
    }

    private boolean isNonRetailAndCorpCreds(Credential credential) {
        return credential.getCredentialsCommission().isPresent()
                && !credential.isCorpCommission()
                && !credential.isRetailCommission();
    }

    @Scheduled(cron = "0 0/15 * * * *")
    private void refreshInMemoryCache(){
        try {
            this.supplierFareCodesMap = initialiseSupplierFareCodeMap();
            this.promoEnabledChannels = initialisePromoCodedMap();
            initializeSupplierEnabledFareGroupsMap();
        }
        catch(Exception e){
            log.error("Failed to refresh json in SupplierFareCodeConfigUtil : {}",e.getMessage());
        }
    }

    public static Set<FareGroupInfo> getEnabledFareGroups(Supplier supplier,
                                                          FlightSearchCriteriaDTO flightSearchCriteriaDTO){

        SupplierFareGroupInfoConfig supplierFareGroupInfoConfig =
                supplierEnabledFareGroupsConfigs.getOrDefault(supplier, null);
        if(Objects.isNull(supplierFareGroupInfoConfig)){
            return Set.of(FareGroupInfo.REGULAR);
        }
        Set<String> allowedSectors = supplierFareGroupInfoConfig.getAllowedSectors();
        if (!allowedSectors.isEmpty() && allowedSectors.contains("*") || allowedSectors.containsAll(getRequestSectors(flightSearchCriteriaDTO))){
            return supplierFareGroupInfoConfig.getEnabledFareGroups();
        }
        return Set.of(FareGroupInfo.REGULAR);
    }

    public static Set<FareGroupInfo> getEnabledFareGroups(Supplier supplier, Set<String> requiredSectors){

        SupplierFareGroupInfoConfig supplierFareGroupInfoConfig =
                supplierEnabledFareGroupsConfigs.getOrDefault(supplier, null);
        if(Objects.isNull(supplierFareGroupInfoConfig)){
            return Set.of(FareGroupInfo.REGULAR);
        }
        Set<String> allowedSectors = supplierFareGroupInfoConfig.getAllowedSectors();
        if (!allowedSectors.isEmpty() && allowedSectors.contains("*") || allowedSectors.containsAll(requiredSectors)){
            return supplierFareGroupInfoConfig.getEnabledFareGroups();
        }
        return Set.of(FareGroupInfo.REGULAR);
    }

    private static Set<String> getRequestSectors(FlightSearchCriteriaDTO flightSearchCriteriaDTO){
        Set<String> requestedSectors = new HashSet<>();
        flightSearchCriteriaDTO.getBaseSearchCriterionDTOS().stream().forEach(
                baseCriteria -> {
                    requestedSectors.add(baseCriteria.getOriginDestinationInfoDTO().getFrom());
                    requestedSectors.add(baseCriteria.getOriginDestinationInfoDTO().getTo());
                }
        );
        return requestedSectors;
    }

    private void initializeSupplierEnabledFareGroupsMap() throws ConfigurationLoadException {
        try {
            String supplierEnabledFareGroupsString = properties.getPropertyValue(CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS);
            List<SupplierFareGroupInfoConfig> supplierFareGroupInfoConfigs =
                    objectMapper.readValue(supplierEnabledFareGroupsString,
                    new TypeReference<List<SupplierFareGroupInfoConfig>>() {});
            supplierEnabledFareGroupsConfigs =
                    supplierFareGroupInfoConfigs.stream().collect(Collectors.toMap(config -> config.getSupplier(),
                    Function.identity()));
        }catch (Exception e) {
            log.error("Error loading Configuration with key {} due to the {}",
                    CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS, e);
            throw new ConfigurationLoadException( "Error loading Configuration with key : " + CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS,e);
        }
    }
}
