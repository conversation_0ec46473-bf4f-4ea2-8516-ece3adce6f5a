package com.cleartrip.supplier.search.services.cachingService.keyGenerator;

import com.cleartrip.supplier.inventory.protos.v1.FareGroupInfo;
import com.cleartrip.supplier.search.data.AccountCode;
import com.cleartrip.supplier.search.models.BaseSearchCriteriaDTO;
import com.cleartrip.supplier.search.models.CustomerSearchCriteriaDTO;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.cleartrip.supplier.web.SISConstants.ALL_IDENTIFIER;
import static com.cleartrip.supplier.web.SISConstants.UNDERSCORE;

@Getter
public class SolutionIdFieldsV2 {
    private final List<SearchCriteriaFields> searchCriteriaFields;
    private final String adultCount;
    private final String childCount;
    private final String infantCount;
    private final String cabinType;
    private final String sellingCountry;
    private final String credentialKey;
    private final List<String> productClass;
    private final List<String> fareType;
    private final List<String> promoCode;
    private final String fareGroupInfo;

    //NOTE: THIS PROPERTY CAN'T BE CREATED IN DB OR FETCHED FROM CONFIG DUE TO DEEP DEPENDENCIES.
    private static final List<String> ffEnabledSuppliers  = new ArrayList<>(Arrays.asList("AIR_ARABIA","FLY_DUBAI"));

    /***
     * Increment the count everytime you add a new field
     ***/
    private static final int fieldsCount = 10;
    private static final String amendKey = "AMEND";
    private static final String V3_VERSION = "V3";

    public List<SearchCriteriaFields> getSearchCriteriaFields() {
        return searchCriteriaFields;
    }

    private final String delimiter;

    public SolutionIdFieldsV2(WorkFlowTaskAvailSearchRequest request, String delimiter,Map<String,List<String>> newCacheKeyFlowDomainToSectorMap) {
        CustomerSearchCriteriaDTO customerSearchCriteriaDTO = request.getCustomerSearchCriteriaDTO();
        List<BaseSearchCriteriaDTO> criteriaList = request.getFlightSearchCriteriaDTO().getBaseSearchCriterionDTOS();
        searchCriteriaFields = request.getFlightSearchCriteriaDTO().getBaseSearchCriterionDTOS().stream().map(criteria ->
                new SearchCriteriaFields(criteria, delimiter)).collect(Collectors.toList());

        adultCount = String.valueOf(customerSearchCriteriaDTO.getPaxCount().getAdults());
        childCount = String.valueOf(customerSearchCriteriaDTO.getPaxCount().getChildren());
        infantCount = String.valueOf(customerSearchCriteriaDTO.getPaxCount().getInfants());
        cabinType = customerSearchCriteriaDTO.getCabinType().name();
//        sellingCountry = newCacheKeyFlowDomains.contains(request.getCustomerSearchCriteriaDTO().getSellingCountry()) ? "DEFAULT":request.getCustomerSearchCriteriaDTO().getSellingCountry();
        sellingCountry = getNewCacheKeySellingCountry(criteriaList,request,newCacheKeyFlowDomainToSectorMap);
        credentialKey = request.getCredential().getKey();
        productClass = getList(request.getFlightSearchCriteriaDTO().getProductClass());
        fareType = getList(request.getFlightSearchCriteriaDTO().getFareType());
        promoCode = getPromoCodeFromPromoCodeAndAccountCode(request.getFlightSearchCriteriaDTO().getPromoCode(), request.getFlightSearchCriteriaDTO().getAccountCodes());
        fareGroupInfo = V3_VERSION.equalsIgnoreCase(request.getFlowVersion()) ? request.getUnifiedFareGroup().name() : request.getFareGroupInfo().name();
        this.delimiter = delimiter;
    }

    SolutionIdFieldsV2(List<SearchCriteriaFields> searchCriteriaFields, String adultCount, String childCount, String infantCount, String cabinType,
                       String sellingCountry, String fareGroupInfo, String credentialKey) {
        this.searchCriteriaFields = searchCriteriaFields;
        this.adultCount = adultCount;
        this.childCount = childCount;
        this.infantCount = infantCount;
        this.cabinType = cabinType;
        this.sellingCountry = sellingCountry;
        this.credentialKey = credentialKey;
        this.delimiter = "";
        this.productClass = Collections.emptyList();
        this.fareType = Collections.emptyList();
        this.promoCode = Collections.emptyList();
        this.fareGroupInfo = fareGroupInfo;
    }

    SolutionIdFieldsV2(List<SearchCriteriaFields> searchCriteriaFields, String adultCount, String childCount, String infantCount, String cabinType,
                       String sellingCountry, String fareGroupInfo, String credentialKey, String delimiter) {
        this.searchCriteriaFields = searchCriteriaFields;
        this.adultCount = adultCount;
        this.childCount = childCount;
        this.infantCount = infantCount;
        this.cabinType = cabinType;
        this.sellingCountry = sellingCountry;
        this.credentialKey = credentialKey;
        this.delimiter = delimiter;
        this.productClass = Collections.emptyList();
        this.fareType = Collections.emptyList();
        this.promoCode = Collections.emptyList();
        this.fareGroupInfo = fareGroupInfo;
    }


    public SolutionIdFieldsV2(List<SearchCriteriaFields> searchCriteriaFields,
                              String adultCount,
                              String childCount,
                              String infantCount,
                              String cabinType,
                              String sellingCountry,
                              String fareGroupInfo,
                              String credentialKey,
                              List<String> productClass,
                              List<String> fareType,
                              List<String> promoCode,
                              String delimiter) {
        this.searchCriteriaFields = searchCriteriaFields;
        this.adultCount = adultCount;
        this.childCount = childCount;
        this.infantCount = infantCount;
        this.cabinType = cabinType;
        this.sellingCountry = sellingCountry;
        this.credentialKey = credentialKey;
        this.delimiter = delimiter;
        this.productClass = productClass;
        this.fareType = fareType;
        this.promoCode = promoCode;
        this.fareGroupInfo = fareGroupInfo;
    }


    SolutionIdFieldsV2(List<SearchCriteriaFields> searchCriteriaFieldsList, String credentialKey) {
        this.searchCriteriaFields = searchCriteriaFieldsList;
        this.adultCount = "";
        this.childCount = "";
        this.infantCount = "";
        this.cabinType = "";
        this.sellingCountry = "";
        this.credentialKey = credentialKey;
        this.delimiter = "";
        this.productClass = Collections.emptyList();
        this.fareType = Collections.emptyList();
        this.promoCode = Collections.emptyList();
        this.fareGroupInfo = FareGroupInfo.REGULAR.name();
    }

    public String getCredentialKey() {
        return credentialKey;
    }

    public static class SearchCriteriaFields {
        private final String from;
        private final String to;
        private final String startDate;

        private final String UNDERSCORE = "_";

        public String getFrom() {
            return from;
        }

        public String getTo() {
            return to;
        }

        public String getStartDate() {
            return startDate;
        }

        /***
         * Increment the count everytime you add a new field
         ***/
        private static final int fieldsCount = 3;
        private final String delimiter;

        SearchCriteriaFields(BaseSearchCriteriaDTO criteria, String delimiter) {
            from = criteria.getOriginDestinationInfoDTO().getFrom();
            to = criteria.getOriginDestinationInfoDTO().getTo();
            startDate = getStartDayEpoch(criteria.getDate(), criteria.getDateZoneId());
            this.delimiter = delimiter;
        }

        SearchCriteriaFields(String from, String to, String startDate) {
            this.from = from;
            this.to = to;
            this.startDate = startDate;
            this.delimiter = "";
        }

        SearchCriteriaFields(String from, String to, String startDate, String delimiter) {
            this.from = from;
            this.to = to;
            this.startDate = startDate;
            this.delimiter = delimiter;
        }

        private String getStartDayEpoch(long epoch, String dateZoneId) {
            return String.valueOf(LocalDate.ofInstant(Instant.ofEpochMilli(epoch), ZoneId.of(dateZoneId))
                    .atStartOfDay()
                    .atZone(ZoneId.of(dateZoneId))
                    .toInstant()
                    .toEpochMilli());
        }

        public static int getFieldsCount() {
            return fieldsCount;
        }

        public static List<SearchCriteriaFields> getSearchCriteriaFields(String[] searchCriteriaFields) {
            List<SearchCriteriaFields> searchCriteriaFieldsList = new ArrayList<>();
            for (int i = 0; i < searchCriteriaFields.length; i = i + fieldsCount) {
                searchCriteriaFieldsList.add(new SearchCriteriaFields(searchCriteriaFields[i],
                        searchCriteriaFields[i + 1], searchCriteriaFields[i + 2]));
            }
            return searchCriteriaFieldsList;
        }

        public String getJourneyKey() {
            return this.getFrom() + UNDERSCORE + this.getTo();
        }

        @Override
        public String toString() {
            return from + delimiter + to + delimiter + startDate + delimiter;
        }
    }

    public static int getFieldsCount() {
        return fieldsCount;
    }

    /***
     * The order of fields in toString will impact the credential key Fetch logic.
     * Anyone changes this toString will have to change SolutionTransformer.getCredentialKeyFromSolutionId() as well
     */
    @Override
    public String toString() {
        return searchCriteriaFields.stream().map(SearchCriteriaFields::toString).collect(Collectors.joining())
                + adultCount + delimiter + childCount + delimiter + infantCount + delimiter
                + cabinType + delimiter + sellingCountry + delimiter + String.join("", productClass) + delimiter
                + String.join("", fareType) + delimiter + String.join("", promoCode) + delimiter
                + fareGroupInfo + delimiter + credentialKey;
    }

    public static SolutionIdFieldsV2 getSolutionIdField(String solutionId) {
        String[] splittedFields = getParentKeyFromSolutionId(solutionId).split(Pattern.quote("|"), -1);

        if ((splittedFields.length - SolutionIdFieldsV2.getFieldsCount()) % SearchCriteriaFields.getFieldsCount() != 0) {
            SolutionIdFields oldIdFields = SolutionIdFields.getSolutionIdField(solutionId);
            List<SearchCriteriaFields> newSearchCriteriaFields = new ArrayList<>();
            oldIdFields.getSearchCriteriaFields().forEach(obj -> newSearchCriteriaFields.add(new SearchCriteriaFields(obj.getFrom(), obj.getTo(), obj.getStartDate())));
            return new SolutionIdFieldsV2(newSearchCriteriaFields, oldIdFields.getCredentialKey());
        }
        List<SearchCriteriaFields> searchCriteriaFieldsList = SearchCriteriaFields.
                getSearchCriteriaFields(Arrays.copyOfRange(splittedFields, 0, splittedFields.length - fieldsCount));

        int index = splittedFields.length - fieldsCount;

        return new SolutionIdFieldsV2(searchCriteriaFieldsList, splittedFields[index], splittedFields[index + 1],
                splittedFields[index + 2], splittedFields[index + 3], splittedFields[index + 4], splittedFields[index + 8], splittedFields[index + 9]);
    }

    public static String getParentKeyFromSolutionId(String solutionId) {
        return solutionId.split(Pattern.quote("~"), -1)[0];
    }

    public static String getFlightKeyFromSolutionId(String solutionId) {
        return solutionId.split(Pattern.quote("~"), -1)[1];
    }

    private static List<String> getList(List<String> input) {
        if (CollectionUtils.isNotEmpty(input)) {
            List<String> list = new ArrayList<>(input);
            Collections.sort(list);
            return list;
        }
        return new ArrayList<>();
    }

    private static List<String> getPromoCodeFromPromoCodeAndAccountCode(String promoCode, List<AccountCode> accountCodes) {
        List<String> promoCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(promoCode)) {
            promoCodeList.add(promoCode);
        }
        if (CollectionUtils.isNotEmpty(accountCodes)) {
            List<String> accountCodeList = accountCodes.stream().map(AccountCode::getCode).sorted()
                    .collect(Collectors.toList());
            promoCodeList.addAll(accountCodeList);
        }
        return promoCodeList;
    }

    private static String createSolutionWithKeys(String parentKey, String childKey) {
        return parentKey + "~" + childKey;
    }

    public static boolean isAmendSolution(String solutionId) {
        return solutionId.contains(amendKey);
    }

    public static String convertAmendSolutionId(String amendSolutionId) {
        String amendParentKey = getParentKeyFromSolutionId(amendSolutionId);
        int lastPipeIndex = amendParentKey.lastIndexOf("|");
        String solutionParentKey = amendParentKey.substring(0, lastPipeIndex);
        return createSolutionWithKeys(solutionParentKey, getFlightKeyFromSolutionId(amendSolutionId));
    }

    public static String getNewCacheKeySellingCountry(List<BaseSearchCriteriaDTO> criteriaDTOList, WorkFlowTaskAvailSearchRequest request, Map<String, List<String>> newCacheKeyFlowDomainToSectorMap) {
        String sellingCountry = request.getCustomerSearchCriteriaDTO().getSellingCountry();
        List<String> allowedSectors = newCacheKeyFlowDomainToSectorMap
                .getOrDefault(sellingCountry, Collections.emptyList());

        boolean match = criteriaDTOList.stream().allMatch(criteria -> {
            String from = criteria.getOriginDestinationInfoDTO().getFrom();
            String to = criteria.getOriginDestinationInfoDTO().getTo();

            String oneToOne = org.apache.commons.lang3.StringUtils.join(from, UNDERSCORE, to);
            String oneToAll = org.apache.commons.lang3.StringUtils.join(from, UNDERSCORE, ALL_IDENTIFIER);
            String allToOne = org.apache.commons.lang3.StringUtils.join(ALL_IDENTIFIER, UNDERSCORE, to);
            String allToAll = org.apache.commons.lang3.StringUtils.join(ALL_IDENTIFIER, UNDERSCORE, ALL_IDENTIFIER);


            return allowedSectors.stream()
                    .anyMatch(s -> s.equalsIgnoreCase(oneToOne)
                            || s.equalsIgnoreCase(oneToAll)
                            || s.equalsIgnoreCase(allToOne)
                            || s.equalsIgnoreCase(allToAll));
        });

        return match ? "DEFAULT" : sellingCountry;
    }
}
