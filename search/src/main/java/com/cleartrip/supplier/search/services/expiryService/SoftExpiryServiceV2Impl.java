package com.cleartrip.supplier.search.services.expiryService;

import com.cleartrip.supplier.search.enums.ExpiryType;
import com.cleartrip.supplier.search.models.cache.avail.AvailabilityCacheOutput;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import com.cleartrip.supplier.search.repository.impls.TTL;
import com.cleartrip.supplier.search.services.cachingService.ttl.TTLService;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.time.Duration;


@Slf4j
public class SoftExpiryServiceV2Impl implements ExpiryServiceV2<Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest>> {

//    private final CacheExpiryRepository softExpiryTTlService;
    private final TTLService<WorkFlowTaskAvailSearchRequest> softExpiryTTlService;


//    @Inject
//    public SoftExpiryServiceV2Impl(CacheExpiryRepository ttlService) {
//        this.softExpiryTTlService = ttlService;
//    }

    @Inject
    public SoftExpiryServiceV2Impl(@Named("SoftExpiryTTLService") TTLService<WorkFlowTaskAvailSearchRequest> ttlService) {
        this.softExpiryTTlService = ttlService;
    }

    @Override
    public Boolean isExpired(Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest> request) {
        try {
            long putTimeStampOfCacheDataInMilli = request.getLeft().getCacheMetaInfo().getCachedAt();
            long cachedDurationInMill = System.currentTimeMillis() - putTimeStampOfCacheDataInMilli;
//            TTL ttl = softExpiryTTlService.getHotCacheSoftExpiryTTL(request.getRight());
            TTL ttl = softExpiryTTlService.getTTL(request.getRight());
            long convertCachedDuration = ttl.getTimeUnit().convert(Duration.ofMillis(cachedDurationInMill));
            return convertCachedDuration >= ttl.getExpiry();
        } catch (Exception e) {
            log.error("Failed to evaluate the soft expiry due to the {} returning default value as true", e.getMessage(), e);
            return Boolean.TRUE;
        }
    }

    @Override
    public ExpiryType getExpiryType() {
        return ExpiryType.SOFT_EXPIRED;
    }

}
