package com.cleartrip.supplier.search.enums;

import java.util.Arrays;
import java.util.Optional;

public enum Supplier {
    GALILEO(1),
    AMADEUS(2),
    SABRE(3),
    INDIGO(4),
    SPICEJET(5),
    RADIXX(6),
    AIE(7),
    AIR_ARABIA(8),
    TF(9),
    AIR_ASIA(10),
    TRUJET(11),
    FLY_DUBAI(12),
    FLYNAS(13),
    JAZEERA(14),
    GALILEO_LFS_INTERNATIONAL(15),
    AKASA_AIR(16),
    ALLIANCE_AIR(17),
    AMADEUS_INTERNATIONAL(18),
    AIR_ARABIA_MOROCCO(19),
    AIR_ARABIA_EGYPT(20),
    GAL_NDC(21),
    VERTEIL(22),
    FLYADEAL(23),
    STAR_AIR(24),
    FLY_91(25),
    T<PERSON>(26),
    TRIPJAC<PERSON>(27),
    AMADEUS_NDC(28);

    private final int supplierId;

    Supplier(int supplierId) {
        this.supplierId = supplierId;
    }

    public int getSupplierId() {
        return supplierId;
    }

    public static Optional<Supplier> getSupplier(int id) {
        return Arrays.stream(Supplier.values())
                .filter(
                        supp -> supp.getSupplierId() == id
                ).findAny();
    }

    public enum SupplierCategory {
        GDS,
        LCC
    }

    public static Supplier getSupplier(String supplier){
        return Arrays.stream(Supplier.values()).filter(supplier1 -> supplier1.name().equalsIgnoreCase(supplier)).findFirst().orElseThrow(() -> new RuntimeException("No Supplier enum for " + supplier));
    }

}
