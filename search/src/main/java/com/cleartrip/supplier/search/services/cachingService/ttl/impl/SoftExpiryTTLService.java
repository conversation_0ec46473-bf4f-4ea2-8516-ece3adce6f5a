package com.cleartrip.supplier.search.services.cachingService.ttl.impl;

import com.cleartrip.config.management.core.resources.cachePolicy.CachePolicyAttributes;
import com.cleartrip.config.management.core.resources.cachePolicy.CachePolicyResource;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.search.constant.SisConfigKeys;
import com.cleartrip.supplier.search.exception.JsonParseRuntimeException;
import com.cleartrip.supplier.search.models.BaseSearchCriteriaDTO;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import com.cleartrip.supplier.search.repository.impls.TTL;
import com.cleartrip.supplier.search.services.cachingService.model.DPlusXConfig;
import com.cleartrip.supplier.search.services.cachingService.model.TTLPolicyConfig;
import com.cleartrip.supplier.search.services.cachingService.ttl.TTLService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class SoftExpiryTTLService implements TTLService<WorkFlowTaskAvailSearchRequest> {
    private static final String FAILED_TO_READ_THE_CONFIG_KEY_VALUE_DUE_TO_THE = "Failed to read the config key: {} value: {} due to the {}";
    public static final String DEFAULT_KEY = "*";
    public static final String UNDER_SCORE = "_";
    public static final String DEFAULT = "DEFAULT";
    public static final String DOM = "DOM";
    public static final String INTL = "INTL";
    private final SearchConfigContainer properties;
    private final ObjectMapper mapper;
    private final Map<String, List<CachePolicyAttributes>> policyResource;
    private final TTLPolicyConfig ttlPolicyConfig;


    @Inject
    public SoftExpiryTTLService(SearchConfigContainer properties, ObjectMapper mapper, CachePolicyResource policyResource) {
        this.properties = properties;
        this.mapper = mapper;
        this.policyResource = policyResource.getResource();
        this.ttlPolicyConfig = initialiseTTLPolicyConfig();
    }

    private TTLPolicyConfig initialiseTTLPolicyConfig() {
        String ttlJson = properties.getPropertyValue(SisConfigKeys.TTL_POLICY_MAPPING_KEY);
        try {
            return mapper.readValue(ttlJson, TTLPolicyConfig.class);
        } catch (Exception e) {
            log.error(FAILED_TO_READ_THE_CONFIG_KEY_VALUE_DUE_TO_THE,
                    SisConfigKeys.TTL_POLICY_MAPPING_KEY, ttlJson, e.getMessage(), e);
            throw new JsonParseRuntimeException(ttlJson, e);
        }
    }

    @Override
    public TTL getTTL(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {

        String type = workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().isIntl()?INTL:DOM;
        String supplierKey = workFlowTaskAvailSearchRequest.getSupplierCarrierPair().getSupplier().name();
        return new TTL(calculateExpiry(workFlowTaskAvailSearchRequest, type, supplierKey), TimeUnit.SECONDS);
    }

    private long calculateExpiry(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest, String type, String supplierKey) {
        List<BaseSearchCriteriaDTO> baseSearchCriterionDTOS = workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().getBaseSearchCriterionDTOS();
        long departDate = baseSearchCriterionDTOS.get(0).getDate();
        long currentMillis = new Date().toInstant().toEpochMilli();
        long diffInMillis = departDate - currentMillis;
        int dPlusXDays = (int) TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
        String origin = baseSearchCriterionDTOS.get(0).getOriginDestinationInfoDTO().getFrom();
        String destination = baseSearchCriterionDTOS.get(baseSearchCriterionDTOS.size() - 1).getOriginDestinationInfoDTO().getTo();
        String sector = origin+UNDER_SCORE+destination;
        Map<String, DPlusXConfig> supplierConfigMap = ttlPolicyConfig.getTtlPolicyConfig().get(type).getSupplierConfig();
        Map<String,Integer> sectorConfigMap = supplierConfigMap
                .get(supplierConfigMap.containsKey(supplierKey)
                        ?supplierKey
                        :DEFAULT)
                .getDPlusXConfig().floorEntry(dPlusXDays).getValue().getSectorConfig();
        return sectorConfigMap.get(sectorConfigMap.containsKey(sector)?sector:DEFAULT);
    }
}
