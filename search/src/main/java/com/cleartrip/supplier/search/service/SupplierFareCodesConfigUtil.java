package com.cleartrip.supplier.search.service;

import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.v1.CustomerSearchCriteria;
import com.cleartrip.supplier.inventory.protos.v1.FareGroupInfo;
import com.cleartrip.supplier.inventory.protos.v1.SourceTypeOuterClass;
import com.cleartrip.supplier.search.config.suppliercodes.*;
import com.cleartrip.supplier.search.constant.SisConfigKeys;
import com.cleartrip.supplier.search.data.AvailabilityTaskRequest;
import com.cleartrip.supplier.search.data.Credential;
import com.cleartrip.supplier.search.entity.SupplierFareGroupInfoConfig;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.exception.ConfigurationLoadException;
import com.cleartrip.supplier.search.exception.JsonParseRuntimeException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import javax.validation.constraints.NotNull;

import org.springframework.scheduling.annotation.Scheduled;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cleartrip.supplier.search.constant.SisConfigKeys.CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS;


@Log4j2
public class SupplierFareCodesConfigUtil {
    public static final String DEFAULT = "DEFAULT";
    private static Map<Supplier, CabinToSectorConfigContainer> supplierFareCodesMap;
    private static Map<Supplier, List<SourceTypeOuterClass.SourceType>> promoEnabledChannels;
    private final SearchConfigContainer properties;
    private final ObjectMapper objectMapper;
    private static Map<Supplier, SupplierFareGroupInfoConfig> supplierEnabledFareGroupsConfigs;

    @Inject
    public SupplierFareCodesConfigUtil(SearchConfigContainer properties, ObjectMapper objectMapper) throws ConfigurationLoadException {
        this.properties = properties;
        this.objectMapper = objectMapper;
        this.supplierFareCodesMap = initialiseSupplierFareCodeMap();
        this.promoEnabledChannels = initialisePromoCodedMap();
        initializeSupplierEnabledFareGroupsMap();
    }

    private Map<Supplier, CabinToSectorConfigContainer> initialiseSupplierFareCodeMap() throws ConfigurationLoadException {
        log.info("scheduler for cabinToSectorConfig Refresh");
        String propertyValue = properties.getPropertyValue(SisConfigKeys.SMS_SUPPLIER_FARE_CODES);
        try {
            List<SupplierConfigFareCodes> supplierConfigFareCodes = objectMapper.readValue(propertyValue, new TypeReference<>() {
            });
            return supplierConfigFareCodes.stream().collect(Collectors.toMap(SupplierConfigFareCodes::getSupplier,
                    s -> new CabinToSectorConfigContainer(cabinTypeTripConfigContainerMap(s))));
        } catch (JsonProcessingException e) {
            log.error("Failed to read the config key: {} value: {} due to the {}",
                    SisConfigKeys.SUPPLIER_CATEGORY_CONFIG_KEY, propertyValue, e.getMessage(), e);
            throw new JsonParseRuntimeException(propertyValue, e);
        } catch (Exception e) {
            log.error("Error loading Configuration with key {} due to the {}",
                    SisConfigKeys.SMS_SUPPLIER_FARE_CODES, e);
            throw new ConfigurationLoadException( "Error loading Configuration with key : " + SisConfigKeys.SMS_SUPPLIER_FARE_CODES,e);
        }
    }

    private Map<Supplier, List<SourceTypeOuterClass.SourceType>> initialisePromoCodedMap() throws ConfigurationLoadException {
        log.info("scheduler for promoCodeMap Refresh");
        String propertyValue = properties.getPropertyValue(SisConfigKeys.SMS_SUPPLIER_PROMO_CODES);
        try {
            return objectMapper.readValue(propertyValue, new TypeReference<Map<Supplier, List<SourceTypeOuterClass.SourceType>>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Failed to read the config key: {} value: {} due to the {}",
                    SisConfigKeys.SMS_SUPPLIER_PROMO_CODES, propertyValue, e.getMessage(), e);
            throw new JsonParseRuntimeException(propertyValue, e);
        } catch (Exception e) {
            log.error("Error loading Configuration with key {} due to the {}",
                    SisConfigKeys.SMS_SUPPLIER_PROMO_CODES, e);
            throw new ConfigurationLoadException( "Error loading Configuration with key : " + SisConfigKeys.SMS_SUPPLIER_PROMO_CODES,e);
        }
    }

    public Optional<FareGroupInfo> getFareGroupInfo(Supplier supplier, String productClass) {
        return Optional.ofNullable(supplierFareCodesMap.get(supplier))
                .flatMap(fareCode -> fareCode.getCabinConfigMap().values().stream()
                        .flatMap(val -> val.getTripConfigMap().values().stream()
                                .flatMap(val1 -> val1.getJourneyTypeListMap().values().stream()
                                        .flatMap(val2 -> val2.stream()
                                                .flatMap(val3 -> val3.getProductClasses().stream()
                                                        .filter(val4 -> val4.getFareCodes().contains(productClass))))))
                        .findFirst().map(FareSubTypeCodeConfig::getFareGroupInfo));
    }

    public List<String> getProductClasses(AvailabilityTaskRequest availabilityTaskRequest) {
        //TODO needs to for corp

        Optional<SourceFareCodeConfig> sourceConfig;
        List<FareGroupInfo> requiredFareFamiliesList = getRequiredFareFamiliesList(availabilityTaskRequest);

        List<SourceFareCodeConfig> sourceFareCodeConfigs = getSourceConfigs(availabilityTaskRequest);

        if (isRetailOrCorpCreds(availabilityTaskRequest.getCredential())) {
            sourceConfig = filterCredsFareTypes(availabilityTaskRequest.getCredential().getCredentialsCommission().get(),
                    sourceFareCodeConfigs).map(c -> c);
        } else {
            sourceConfig = filterChannelFareTypes(availabilityTaskRequest.getRequest().getCustomerInfo().getSourceType(),
                    sourceFareCodeConfigs).map(c -> c);
        }
        return filterByFareCodeType(sourceConfig.map(SourceFareCodeConfig::getProductClasses)
                .orElse(Collections.emptyList()), requiredFareFamiliesList);
    }

    @NotNull
    private List<FareGroupInfo> getRequiredFareFamiliesList(AvailabilityTaskRequest availabilityTaskRequest) {
        List<FareGroupInfo> requiredFareFamiliesList = new ArrayList<>(availabilityTaskRequest.getRequest().getFlightSearchCriteria().getSearchOptions().getRequiredFareFamiliesList());
        requiredFareFamiliesList.retainAll(getEnabledFareGroups(availabilityTaskRequest.getSupplierCarriers().getSupplier(), availabilityTaskRequest.getCustomerSearchCriteria()));
        if(CollectionUtils.isEmpty(requiredFareFamiliesList)){
            return Collections.singletonList(FareGroupInfo.REGULAR);
        }
        return requiredFareFamiliesList;
    }

    public List<String> getFareTypes(AvailabilityTaskRequest availabilityTaskRequest) {
        Optional<SourceFareCodeConfig> sourceConfig;
        List<FareGroupInfo> requiredFareFamiliesList = getRequiredFareFamiliesList(availabilityTaskRequest);

        List<SourceFareCodeConfig> sourceFareCodeConfigs = getSourceConfigs(availabilityTaskRequest);
        if (isRetailOrCorpCreds(availabilityTaskRequest.getCredential())) {
            sourceConfig = filterCredsFareTypes(availabilityTaskRequest.getCredential().getCredentialsCommission().get(),
                    sourceFareCodeConfigs).map(c -> c);
        } else {
            sourceConfig = filterChannelFareTypes(availabilityTaskRequest.getRequest().getCustomerInfo().getSourceType(),
                    sourceFareCodeConfigs).map(c -> c);
        }

        return filterByFareCodeType(sourceConfig.map(SourceFareCodeConfig::getFareTypes)
                .orElse(Collections.emptyList()), requiredFareFamiliesList);
    }

    public Optional<String> getPromoCode(AvailabilityTaskRequest availabilityTaskRequest) {
        List<SourceTypeOuterClass.SourceType> enabledChannels = promoEnabledChannels.get(availabilityTaskRequest.getSupplierCarriers().getSupplier());
        if (CollectionUtils.isNotEmpty(enabledChannels)
                && enabledChannels.contains(availabilityTaskRequest.getRequest().getCustomerInfo().getSourceType())
                && isNonRetailAndCorpCreds(availabilityTaskRequest.getCredential())) {
            return Optional.of(availabilityTaskRequest.getCredential().getCredentialsCommission().get());
        }
        //TODO flow for CORP
        return Optional.empty();
    }


    @NotNull
    private List<String> filterByFareCodeType(List<FareSubTypeCodeConfig> fareSubTypeCodeConfigList, List<FareGroupInfo> fareCodeType) {
        return fareCodeType.stream().flatMap(fct -> fareSubTypeCodeConfigList.stream().filter(ft -> ft.getFareGroupInfo().equals(fct))).
                flatMap(fc -> fc.getFareCodes().stream()).collect(Collectors.toList());
    }


    private Map<String, SectorToJoruneyConfigContainer> cabinTypeTripConfigContainerMap(SupplierConfigFareCodes supplierConfigFareCodes) {
        return supplierConfigFareCodes.getCabinFareCodesConfig().stream().collect(Collectors.toMap(CabinFareCodesConfig::getCabinType,
                cc -> new SectorToJoruneyConfigContainer(tripTypeJourneyConfigMap(cc))));
    }

    private Map<SectorFareCodeConfig.SectorType, JourneyToFareConfigContainer> tripTypeJourneyConfigMap(CabinFareCodesConfig cabinFareCodesConfig) {
        return cabinFareCodesConfig.getSectorFareCodeConfig().stream().collect(Collectors.toMap(SectorFareCodeConfig::getSectorType,
                tc -> new JourneyToFareConfigContainer(tc.getJourneyConfig().stream()
                        .collect(Collectors.toMap(JourneyFareCodeConfig::getJourneyType,
                                JourneyFareCodeConfig::getChannelConfig)))));
    }

    private List<SourceFareCodeConfig> getSourceConfigs(AvailabilityTaskRequest availabilityTaskRequest) {
        CabinToSectorConfigContainer cabinToSectorConfigContainer = supplierFareCodesMap
                .get(availabilityTaskRequest.getSupplierCarriers().getSupplier());

        if (Objects.isNull(cabinToSectorConfigContainer)
                || MapUtils.isEmpty(cabinToSectorConfigContainer.getCabinConfigMap())) {
            return Collections.emptyList();
        }

        SectorFareCodeConfig.SectorType tripType = availabilityTaskRequest.getRequest().getFlightSearchCriteria().getIsIntl()
                ? SectorFareCodeConfig.SectorType.INTL
                : SectorFareCodeConfig.SectorType.DOM;
        JourneyFareCodeConfig.JourneyType journeyType = availabilityTaskRequest.isRoundTripTask()
                ? JourneyFareCodeConfig.JourneyType.ROUNDTRIP
                : JourneyFareCodeConfig.JourneyType.ONEWAY;

        Map<String, SectorToJoruneyConfigContainer> cabinConfig = cabinToSectorConfigContainer
                .getCabinConfigMap();
        Map<SectorFareCodeConfig.SectorType, JourneyToFareConfigContainer> tripConfig = cabinConfig
                .getOrDefault(availabilityTaskRequest.getCustomerSearchCriteria().getCabinType().name(),
                        cabinConfig.get(DEFAULT))
                .getTripConfigMap();
        Map<JourneyFareCodeConfig.JourneyType, List<SourceFareCodeConfig>> journeyTypeListMap = tripConfig
                .getOrDefault(tripType,
                        tripConfig.get(SectorFareCodeConfig.SectorType.DEFAULT))
                .getJourneyTypeListMap();

        return journeyTypeListMap
                .getOrDefault(journeyType, journeyTypeListMap
                        .get(JourneyFareCodeConfig.JourneyType.DEFAULT));
    }


    @NotNull
    private Optional<ChannelSourceFareCodeConfig> filterChannelFareTypes(SourceTypeOuterClass.SourceType sourceType,
                                                                         List<SourceFareCodeConfig> sourceFareCodeConfigs) {
        return sourceFareCodeConfigs.stream().filter(sourceConfig -> SourceFareCodeConfig.Type.CHANNEL.equals(sourceConfig.getType()))
                .map(sourceConfig -> (ChannelSourceFareCodeConfig) sourceConfig)
                .filter(channelSourceConfig -> channelSourceConfig.getChannel().equals(sourceType))
                .findFirst();
    }

    @NotNull
    private Optional<CredSourceFareCodeConfig> filterCredsFareTypes(String credSearchType,
                                                                    List<SourceFareCodeConfig> sourceFareCodeConfigs) {
        return sourceFareCodeConfigs.stream()
                .filter(sourceConfig -> SourceFareCodeConfig.Type.CRED_SEARCH_TYPE.equals(sourceConfig.getType()))
                .map(sourceConfig -> (CredSourceFareCodeConfig) sourceConfig)
                .filter(credSourceConfig -> credSourceConfig.getCredSearchType().getDisplayName().equalsIgnoreCase(credSearchType))
                .findFirst();
    }

    private boolean isRetailOrCorpCreds(Credential credential) {
        return credential.getCredentialsCommission().isPresent()
                && (credential.isRetailCommission() || credential.isCorpCommission());
    }

    private boolean isNonRetailAndCorpCreds(Credential credential) {
        return credential.getCredentialsCommission().isPresent()
                && !credential.isCorpCommission()
                && !credential.isRetailCommission();
    }

    @Scheduled(cron = "0 0/15 * * * *")
    private void refreshInMemoryCache(){
        try {
            this.supplierFareCodesMap = initialiseSupplierFareCodeMap();
            this.promoEnabledChannels = initialisePromoCodedMap();
            initializeSupplierEnabledFareGroupsMap();
        }
        catch(Exception e){
            log.error("Failed to refresh json in SupplierFareCodeConfigUtil : {}",e.getMessage());
        }
    }

    public static Set<FareGroupInfo> getEnabledFareGroups(Supplier supplier,
                                                       CustomerSearchCriteria customerSearchCriteria){

        SupplierFareGroupInfoConfig supplierFareGroupInfoConfig =
                supplierEnabledFareGroupsConfigs.getOrDefault(supplier, null);
        if(Objects.isNull(supplierFareGroupInfoConfig)){
            return Set.of(FareGroupInfo.REGULAR);
        }
        Set<String> allowedSectors = supplierFareGroupInfoConfig.getAllowedSectors();
        if (!allowedSectors.isEmpty() && allowedSectors.contains("*") || allowedSectors.containsAll(getRequestSectors(customerSearchCriteria))){
            return supplierFareGroupInfoConfig.getEnabledFareGroups();
        }
        return Set.of(FareGroupInfo.REGULAR);
    }

    private static Set<String> getRequestSectors(CustomerSearchCriteria customerSearchCriteria){
        Set<String> requestedSectors = new HashSet<>();
        customerSearchCriteria.getBaseCriteriaList().stream().forEach(
                baseCriteria -> {
                    requestedSectors.add(baseCriteria.getOriginDestInfo().getFrom());
                    requestedSectors.add(baseCriteria.getOriginDestInfo().getTo());
                }
        );
        return requestedSectors;
    }

    private void initializeSupplierEnabledFareGroupsMap() throws ConfigurationLoadException {
        try {
            String supplierEnabledFareGroupsString = properties.getPropertyValue(CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS);
            List<SupplierFareGroupInfoConfig> supplierFareGroupInfoConfigs =
                    objectMapper.readValue(supplierEnabledFareGroupsString,
                    new TypeReference<List<SupplierFareGroupInfoConfig>>() {});
            supplierEnabledFareGroupsConfigs =
                    supplierFareGroupInfoConfigs.stream().collect(Collectors.toMap(config -> config.getSupplier(),
                    Function.identity()));
        }catch (Exception e) {
            log.error("Error loading Configuration with key {} due to the {}",
                    CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS, e);
            throw new ConfigurationLoadException( "Error loading Configuration with key : " + CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS,e);
        }
    }
}
