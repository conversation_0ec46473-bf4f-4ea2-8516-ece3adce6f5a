package com.cleartrip.supplier.search.config;

import com.aerospike.client.AerospikeClient;
import com.aerospike.client.Host;
import com.aerospike.client.policy.ClientPolicy;
import com.aerospike.client.policy.Replica;
import com.air.sis.rule.*;
import com.cleartrip.air.application.ConfigManager;
import com.cleartrip.air.application.models.*;
import com.cleartrip.air.entity.decoders.factory.DecoderType;
import com.cleartrip.air.entity.models.BucketDataWrapper;
import com.cleartrip.air.listener.IListener;
import com.cleartrip.air.themis.client.exception.APIExecutionFailedException;
import com.cleartrip.air.themis.client.exception.ResourceNotFoundException;
import com.cleartrip.air.themis.client.service.ThemisClientService;
import com.cleartrip.logging.stats.metrics.CTMeterRegistry;
import com.cleartrip.logging.stats.metrics.CTMeterRegistryConfig;
import com.cleartrip.logging.stats.metrics.EventPublisher;
import com.cleartrip.logging.stats.metrics.newrelic.CtNewRelicConfig;
import com.cleartrip.logging.stats.metrics.newrelic.NewRelicEventPublisher;
import com.cleartrip.monitoring.NewRelicHelper;
import com.cleartrip.monitoring.StatsHelper;
import com.cleartrip.monitoring.publishers.Imps.CommonPublisher;
import com.cleartrip.starter.utility.BucketNameCreator;
import com.cleartrip.supplier.amendsearch.application.AmendFlightAvailApplication;
import com.cleartrip.supplier.amendsearch.application.AmendFlightAvailApplicationImpl;
import com.cleartrip.supplier.amendsearch.domain.AmendSearchDomainService;
import com.cleartrip.supplier.amendsearch.domain.AmendSearchDomainServiceImpl;
import com.cleartrip.supplier.amendsearch.monitoring.MonitoringHelper;
import com.cleartrip.supplier.amendsearch.repository.AmendSearchRepository;
import com.cleartrip.supplier.amendsearch.repository.GetCredentialRepository;
import com.cleartrip.supplier.amendsearch.repository.impl.AmendSearchRepositoryImpl;
import com.cleartrip.supplier.amendsearch.repository.impl.GetCredentialRepositoryImpl;
import com.cleartrip.supplier.amendsearch.workflow.AmendSearchOrchestrator;
import com.cleartrip.supplier.amendsearch.workflow.AmendSearchWorkflow;
import com.cleartrip.supplier.amendsearch.workflow.dto.AmendOrchestratorRequest;
import com.cleartrip.supplier.amendsearch.workflow.dto.AmendOrchestratorResponse;
import com.cleartrip.supplier.amendsearch.workflow.dto.AmendSearchRepositoryRequest;
import com.cleartrip.supplier.amendsearch.workflow.generator.AmendSearchFilteringWorkflowGenerator;
import com.cleartrip.supplier.amendsearch.workflow.generator.CancelRebookSearchWorkflowGenerator;
import com.cleartrip.supplier.amendsearch.workflow.generator.TrueAmendSearchWorkflowGenerator;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.datapublisher.SearchDataPublisher;
import com.cleartrip.supplier.datapublisher.kafka.KafkaSearchDataPublisher;
import com.cleartrip.supplier.db.daos.IRequestPersistenceMongoRepository;
import com.cleartrip.supplier.db.daos.ISearchMongoRepository;
import com.cleartrip.supplier.db.daos.RequestPersistenceMongoRepository;
import com.cleartrip.supplier.db.daos.SearchMongoRepository;
import com.cleartrip.supplier.flightPreviewPackage.application.FlightPreviewApplication;
import com.cleartrip.supplier.flightPreviewPackage.application.FlightPreviewApplicationImpl;
import com.cleartrip.supplier.flightPreviewPackage.domain.FlightPreviewDomainRequest;
import com.cleartrip.supplier.flightPreviewPackage.domain.FlightPreviewDomainService;
import com.cleartrip.supplier.flightPreviewPackage.domain.FlightPreviewDomainServiceImpl;
import com.cleartrip.supplier.flightPreviewPackage.models.StrategyType;
import com.cleartrip.supplier.flightPreviewPackage.orchestration.FlightPreviewOrchestrator;
import com.cleartrip.supplier.flightPreviewPackage.workflows.*;
import com.cleartrip.supplier.group.NodeGroup;
import com.cleartrip.supplier.group.PriceItineraryNodeGroup;
import com.cleartrip.supplier.group.SearchGroup;
import com.cleartrip.supplier.infrastructure.factory.RetrofitClientFactoryImpl;
import com.cleartrip.supplier.infrastructure.factory.RetrofitConverterFactoryGeneratorImpl;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.ConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JSONConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JacksonConverterFactoryHandler;
import com.cleartrip.supplier.infrastructure.factory.converterfactory.json.JsonSubTypeConverterFactoryHandler;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.newrelic.NewRelicAuthConfig;
import com.cleartrip.supplier.newrelic.NewRelicDataSender;
import com.cleartrip.supplier.newrelic.NewRelicEventTransmitter;
import com.cleartrip.supplier.newrelic.NewRelicUtil;
import com.cleartrip.supplier.rule.AirlineSpecificConfigRuleEngine;
import com.cleartrip.supplier.rule.CompanyConfigRuleEngine;
import com.cleartrip.supplier.search.AvailabilitySolutionService;
import com.cleartrip.supplier.search.AvailabilitySolutionServiceImpl;
import com.cleartrip.supplier.search.FlightAvailabilityOrchestrator;
import com.cleartrip.supplier.search.FlightAvailabilityService;
import com.cleartrip.supplier.search.adapters.FlightSolutionAvailResponseAdapter;
import com.cleartrip.supplier.search.adapters.fareBrand.EnrichFlightSolutionRepository;
import com.cleartrip.supplier.search.application.*;
import com.cleartrip.supplier.search.constant.SisConfigKeys;
import com.cleartrip.supplier.search.convertor.Convertor;
import com.cleartrip.supplier.search.convertor.cache.CacheRepositoryInputConvertor;
import com.cleartrip.supplier.search.convertor.cache.CacheRepositoryOutputConvertor;
import com.cleartrip.supplier.search.convertor.cache.fare.FareCacheInputConvertor;
import com.cleartrip.supplier.search.convertor.cache.fare.FareCacheOutputConvertor;
import com.cleartrip.supplier.search.convertor.cache.flight.FlightCacheInputConvertor;
import com.cleartrip.supplier.search.convertor.cache.flight.FlightCacheOutputConvertor;
import com.cleartrip.supplier.search.convertor.ss1.ResponseMetaConvertor;
import com.cleartrip.supplier.search.dedup.*;
import com.cleartrip.supplier.search.discounting.service.CtcCalculationServiceV2;
import com.cleartrip.supplier.search.enrichment.AdditionalSuppliersRepository;
import com.cleartrip.supplier.search.enrichment.AdditionalSuppliersRepositoryImpl;
import com.cleartrip.supplier.search.entity.baggage.BaggageCacheKeyCriteria;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.factory.*;
import com.cleartrip.supplier.search.listeners.PropertiesListener;
import com.cleartrip.supplier.search.listeners.impls.BaggageListenerV2;
import com.cleartrip.supplier.search.lock.Lock;
import com.cleartrip.supplier.search.lock.RedisLock;
import com.cleartrip.supplier.search.models.*;
import com.cleartrip.supplier.search.models.DTO.AvailWorkFlowOutput;
import com.cleartrip.supplier.search.models.DTO.FareFamilyDTO;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.DTO.TFAirlineBundlingDTO;
import com.cleartrip.supplier.search.models.application.enums.CabinType;
import com.cleartrip.supplier.search.models.cache.avail.AvailabilityCacheInput;
import com.cleartrip.supplier.search.models.cache.avail.AvailabilityCacheOutput;
import com.cleartrip.supplier.search.models.cache.avail.AvailabilityFlightCache;
import com.cleartrip.supplier.search.models.cache.avail.AvailabilityReqCacheInput;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import com.cleartrip.supplier.search.orchestrator.IOrchestrator;
import com.cleartrip.supplier.search.redis.SnappyRedisSerializer;
import com.cleartrip.supplier.search.redis.ZstdRedisSerializer;
import com.cleartrip.supplier.search.repository.caching.BulkCachingOperations;
import com.cleartrip.supplier.search.repository.caching.CachingService;
import com.cleartrip.supplier.search.repository.compression.ZstdCompressionImpl;
import com.cleartrip.supplier.search.repository.impls.aerospike.AerospikeCachingService;
import com.cleartrip.supplier.search.repository.impls.aerospike.AerospikeClusterConfiguration;
import com.cleartrip.supplier.search.repository.impls.aerospike.AerospikeHosts;
import com.cleartrip.supplier.search.repository.impls.redis.RedisBulkCachingServiceTemplate;
import com.cleartrip.supplier.search.repository.impls.redis.RedisCachingServiceTemplate;
import com.cleartrip.supplier.search.resource.CallbackPayloadDeSerializer;
import com.cleartrip.supplier.search.ruleEngine.RuleEngineResourceObserver;
import com.cleartrip.supplier.search.ruleEngine.RuleEngineWrapper;
import com.cleartrip.supplier.search.rules.coupon.*;
import com.cleartrip.supplier.search.rules.fareBrand.impl.FareBrandRepository;
import com.cleartrip.supplier.search.rules.fareBrand.impl.FareBrandRepositoryImpl;
import com.cleartrip.supplier.search.service.BaggageService;
import com.cleartrip.supplier.search.service.baggage.BaggageCachingService;
import com.cleartrip.supplier.search.service.baggage.BaggageServiceImpl;
import com.cleartrip.supplier.search.service.listeners.FareRuleListener;
import com.cleartrip.supplier.search.service.listeners.NewRelicListener;
import com.cleartrip.supplier.search.services.cachingService.SolutionsCachingService;
import com.cleartrip.supplier.search.services.cachingService.implementation.SolutionCachingServiceImpl;
import com.cleartrip.supplier.search.services.cachingService.keyGenerator.AmendSolutionKeyGenerator;
import com.cleartrip.supplier.search.services.cachingService.keyGenerator.AvailabilityCacheKeyGeneratorV2;
import com.cleartrip.supplier.search.services.cachingService.keyGenerator.BaggageCacheKeyGeneratorV2;
import com.cleartrip.supplier.search.services.cachingService.keyGenerator.CacheKeyGeneratorV2;
import com.cleartrip.supplier.search.services.cachingService.persist.CachePersistenceService;
import com.cleartrip.supplier.search.services.cachingService.persist.impl.CachePersistenceServiceImpl;
import com.cleartrip.supplier.search.services.cachingService.persist.impl.SearchRequestPersistenceService;
import com.cleartrip.supplier.search.services.cachingService.persist.impl.ZeroResultPersistenceService;
import com.cleartrip.supplier.search.services.cachingService.repositry.*;
import com.cleartrip.supplier.search.services.cachingService.repositry.implementation.*;
import com.cleartrip.supplier.search.services.cachingService.repositry.implementation.coldStore.AvailabilityColdCacheRepositoryImpl;
import com.cleartrip.supplier.search.services.cachingService.retrieve.CacheRetrieverService;
import com.cleartrip.supplier.search.services.cachingService.retrieve.impl.CacheRetrieverImpl;
import com.cleartrip.supplier.search.services.cachingService.ttl.TTLService;
import com.cleartrip.supplier.search.services.cachingService.ttl.impl.*;
import com.cleartrip.supplier.search.services.combination.SolutionCombinationServiceImpl;
import com.cleartrip.supplier.search.services.combination.SolutionsCombinationService;
import com.cleartrip.supplier.search.services.dataPublishingService.SearchDataPublishingService;
import com.cleartrip.supplier.search.services.dataPublishingService.SearchDataPublishingServiceImpl;
import com.cleartrip.supplier.search.services.deduplication.DeduplicationService;
import com.cleartrip.supplier.search.services.deduplication.DeduplicationServiceImpl;
import com.cleartrip.supplier.search.services.deduplication.comparitors.farefamily.FareBrandCredKeyComparator;
import com.cleartrip.supplier.search.services.deduplication.comparitors.farefamily.FareBrandPriceComparator;
import com.cleartrip.supplier.search.services.deduplication.fares.FareDeduplicationService;
import com.cleartrip.supplier.search.services.deduplication.fares.FareDeduplicationServiceImpl;
import com.cleartrip.supplier.search.services.execution.SearchExecutionService;
import com.cleartrip.supplier.search.services.execution.SearchExecutionServiceImpl;
import com.cleartrip.supplier.search.services.expiryService.*;
import com.cleartrip.supplier.search.services.filteration.FiltrationService;
import com.cleartrip.supplier.search.services.filteration.FiltrationServiceImpl;
import com.cleartrip.supplier.search.services.filteration.processors.*;
import com.cleartrip.supplier.search.services.identification.*;
import com.cleartrip.supplier.search.services.identification.impl.AirlineSpecificConfigResourceImpl;
import com.cleartrip.supplier.search.services.identification.impl.BaseCriteriaIdentificationServiceImpl;
import com.cleartrip.supplier.search.services.identification.impl.CompanyConfigResourceImpl;
import com.cleartrip.supplier.search.services.identification.impl.TaskIdentificationServiceImpl;
import com.cleartrip.supplier.search.services.identificationV3.SupplierIdnServiceV3;
import com.cleartrip.supplier.search.services.modification.ResponseModificationService;
import com.cleartrip.supplier.search.services.modification.ResponseModificationServiceImpl;
import com.cleartrip.supplier.search.services.supplierEligibilityEvaluation.SupplierEligibilityRepository;
import com.cleartrip.supplier.search.services.supplierEligibilityEvaluation.SupplierEligibilityRepositoryImpl;
import com.cleartrip.supplier.search.services.supplierEligibilityEvaluation.SupplierEligibilityRequestAdapter;
import com.cleartrip.supplier.search.services.supplierEligibilityEvaluation.SupplierEligibilityRequestAdapterImpl;
import com.cleartrip.supplier.search.workflow.*;
import com.cleartrip.supplier.search.workflow.contexts.AvailabilityProcessingContext;
import com.cleartrip.supplier.search.workflow.executors.AvailWorkFlowExecutor;
import com.cleartrip.supplier.search.workflow.executors.PersistenceWorkflowExecutor;
import com.cleartrip.supplier.search.workflow.executors.TaskIdentificationWorkFlowExecutor;
import com.cleartrip.supplier.search.workflow.executors.TaskIdentificationWorkFlowExecutorV2;
import com.cleartrip.supplier.search.workflow.generator.*;
import com.cleartrip.supplier.search.workflow.tasks.execution.SupplierTaskFactory;
import com.cleartrip.utility.workflow.design.WorkFlowGenerator;
import com.cleartrip.utility.workflow.design.WorkFlowsExecutor;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.cloud.ByteArray;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;
import com.google.inject.name.Names;
import com.mongodb.client.MongoCollection;
import common.clone.Cloner;
import common.clone.GsonObjectCloner;
import common.monitoring.bean.MonitoringBeanContext;
import common.openl.RuleEngineFactory;
import datapublisher.kafka.KafkaProducer;
import db.MongoClientFactory;
import db.MongoDBConnectionParam;
import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.support.ConnectionPoolSupport;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.bson.Document;
import org.eclipse.jetty.client.HttpClient;
import org.eclipse.jetty.util.ssl.SslContextFactory;
import org.jetbrains.annotations.NotNull;
import org.openl.rules.runtime.RulesEngineFactory;
import org.springframework.data.redis.connection.RedisConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStaticMasterReplicaConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import ru.vyarus.dropwizard.guice.module.support.DropwizardAwareModule;

import java.io.File;
import java.io.FileNotFoundException;
import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cleartrip.supplier.constants.GuiceConstants.AIRLINE_SPECIFIC_CONFIG_RESOURCE;
import static com.cleartrip.supplier.constants.GuiceConstants.AIRLINE_SPECIFIC_CONFIG_RULE_ENGINE;

@Slf4j
public class SearchModule extends DropwizardAwareModule<SearchConfiguration> {

    @Override
    protected void configure() {

        bind(FlightSearchApplication.class).to(FlightSearchApplicationImpl.class);
        bind(RuleEngineFactory.class).annotatedWith(Names.named("searchRuleEngineFactory")).to(RuleEngineFactory.class).in(Singleton.class);
        bind(SupplierEligibilityRepository.class).to(SupplierEligibilityRepositoryImpl.class).in(Singleton.class);
        bind(SupplierEligibilityEvaluationService.class).in(Singleton.class);
        bind(SupplierEligibilityRequestAdapter.class).to(SupplierEligibilityRequestAdapterImpl.class).in(Singleton.class);
        bind(PromoCodeRepository.class).to(PromoCodeRepositoryImpl.class).in(Singleton.class);
        bind(PromoCodeService.class).to(PromoCodeServiceImpl.class).in(Singleton.class);
        bind(ApplicableFareBrandPromoCodeRepository.class).to(ApplicableFareBrandPromoCodeRepositoryImpl.class).in(Singleton.class);
        bind(ApplicableFareBrandPromoCodeRetrieverAdapter.class).to(ApplicableFareBrandPromoCodeRetrieverAdapterImpl.class).in(Singleton.class);
        bind(FareBrandRepository.class).to(FareBrandRepositoryImpl.class).in(Singleton.class);

        bind(IRequestPersistenceMongoRepository.class).annotatedWith(Names.named("RequestPersistenceMongoRepository")).to(RequestPersistenceMongoRepository.class).in(Singleton.class);
        bind(ISearchMongoRepository.class).annotatedWith(Names.named("SearchMongoRepository")).to(SearchMongoRepository.class).in(Singleton.class);
        bind(AdditionalSuppliersRepository.class).annotatedWith(Names.named("AdditionalSuppliersRepositoryImpl")).to(AdditionalSuppliersRepositoryImpl.class).in(Singleton.class);
        bind(AvailFlightsCachingRepository.class).annotatedWith(Names.named("AvailFlightCachingRepositoryImpl")).to(AvailFlightCachingRepositoryImpl.class).in(Singleton.class);
        bind(AvailFaresCachingRepository.class).annotatedWith(Names.named("AvailFaresCachingRepositoryImpl")).to(AvailFaresCachingRepositoryImpl.class).in(Singleton.class);
        bind(FlightSearchApplication.class).annotatedWith(Names.named("FlightSearchApplicationImpl")).to(FlightSearchApplicationImpl.class).in(Singleton.class);
        bind(AvailabilitySolutionService.class).annotatedWith(Names.named("AvailabilitySolutionServiceImpl")).to(AvailabilitySolutionServiceImpl.class).in(Singleton.class);
        bind(FlightAvailApplication.class).annotatedWith(Names.named("FlightAvailApplicationImpl")).to(FlightAvailApplicationImpl.class).in(Singleton.class);
        bind(AmendFlightAvailApplication.class).annotatedWith(Names.named("AmendFlightAvailApplicationImpl")).to(AmendFlightAvailApplicationImpl.class).in(Singleton.class);
        bind(AmendSearchDomainService.class).annotatedWith(Names.named("AmendSearchDomainServiceImpl")).to(AmendSearchDomainServiceImpl.class).in(Singleton.class);
        bind(AmendSearchRepository.class).annotatedWith(Names.named("AmendSearchRepositoryImpl")).to(AmendSearchRepositoryImpl.class).in(Singleton.class);
        bind(GetCredentialRepository.class).annotatedWith(Names.named("GetCredentialRepositoryImpl")).to(GetCredentialRepositoryImpl.class).in(Singleton.class);
        bind(FlightSolutionsDedupUtilV2.class).annotatedWith(Names.named("DomDedupUtilV2")).to(DomDedupUtilV2.class).in(Singleton.class);
        bind(FlightSolutionsDedupUtilV2.class).annotatedWith(Names.named("IntlDedupUtilV2")).to(IntlDedupUtilV2.class).in(Singleton.class);
        bind(FlightSolutionsDedupUtilV2.class).annotatedWith(Names.named("SFFDomDedupUtilV2")).to(SFFDomDedupUtilV2.class).in(Singleton.class);
        bind(FlightSolutionsDedupUtilV2.class).annotatedWith(Names.named("SFFIntlDedupUtilV2")).to(SFFIntlDedupUtilV2.class).in(Singleton.class);
        bind(AvailabilityCachingRepository.class).annotatedWith(Names.named("AvailFlightFareCachingRepositoryImpl")).to(AvailFlightFareCachingRepositoryImpl.class).in(Singleton.class);
        bind(AvailabilityCachingRepository.class).annotatedWith(Names.named("AvailabilityColdCacheRepositoryImpl")).to(AvailabilityColdCacheRepositoryImpl.class).in(Singleton.class);
        bind(AvailabilityCachingRepository.class).annotatedWith(Names.named("AvailabilityHotColdCacheRepoImpl")).to(AvailabilityHotColdCacheRepoImpl.class).in(Singleton.class);
        bind(AvailabilityFareFamilyCachingRepository.class).annotatedWith(Names.named("AvailFlightFareCachingRepositoryImpl")).to(AvailFlightFareCachingRepositoryImpl.class).in(Singleton.class);
        bind(AvailSearchReqCachingRepository.class).annotatedWith(Names.named("AvailSearchReqCachingRepositoryImpl")).to(AvailSearchReqCachingRepositoryImpl.class).in(Singleton.class);
        bind(AvailSearchReqCachingRepository.class).annotatedWith(Names.named("MongoAvailSearchReqCachingRepositoryImpl")).to(MongoAvailSearchReqCachingRepositoryImpl.class).in(Singleton.class);
        bind(BaggageService.class).annotatedWith(Names.named("BaggageServiceImpl")).to(BaggageServiceImpl.class).in(Singleton.class);
        bind(ICtcCalculationService.class).annotatedWith(Names.named("CtcCalculationServiceV2")).to(CtcCalculationServiceV2.class).in(Singleton.class);
        bind(ICtcCalculationService.class).annotatedWith(Names.named("CtcCalculationService")).to(CtcCalculationService.class).in(Singleton.class);

        bind(TaskIdentificationService.class).annotatedWith(Names.named("TaskIdentificationService")).to(TaskIdentificationServiceImpl.class).in(Singleton.class);
        bind(SearchExecutionService.class).annotatedWith(Names.named("SearchExecutionService")).to(SearchExecutionServiceImpl.class).in(Singleton.class);
        bind(DeduplicationService.class).annotatedWith(Names.named("DeduplicationService")).to(DeduplicationServiceImpl.class).in(Singleton.class);
        bind(FiltrationService.class).annotatedWith(Names.named("FilterationService")).to(FiltrationServiceImpl.class).in(Singleton.class);
        bind(ResponseModificationService.class).annotatedWith(Names.named("ResponseModificationService")).to(ResponseModificationServiceImpl.class).in(Singleton.class);
        bind(SolutionsCombinationService.class).annotatedWith(Names.named("SolutionsCombinationService")).to(SolutionCombinationServiceImpl.class).in(Singleton.class);
        bind(SolutionsCachingService.class).annotatedWith(Names.named("SolutionsCachingService")).to(SolutionCachingServiceImpl.class).in(Singleton.class);
        bind(FiltrationService.class).annotatedWith(Names.named("FiltrationService")).to(FiltrationServiceImpl.class).in(Singleton.class);
        bind(FareDeduplicationService.class).annotatedWith(Names.named("FareDeduplicationService")).to(FareDeduplicationServiceImpl.class).in(Singleton.class);


        bind(new TypeLiteral<CacheKeyGeneratorV2<WorkFlowTaskAvailSearchRequest, FlightSolutionDTO>>() {
        })
                .annotatedWith(Names.named("AvailabilityCacheKeyGeneratorV2"))
                .to(AvailabilityCacheKeyGeneratorV2.class);
        bind(FlightPreviewApplication.class).to(FlightPreviewApplicationImpl.class).in(Singleton.class);
        bind(FlightPreviewDomainService.class).to(FlightPreviewDomainServiceImpl.class).in(Singleton.class);
        bind(new TypeLiteral<IOrchestrator<FlightPreviewDomainRequest, FlightSolutionDTO>>() {
        })
                .annotatedWith(Names.named("flightPreviewOrchestrator"))
                .to(FlightPreviewOrchestrator.class);
        bind(new TypeLiteral<IOrchestrator<AmendOrchestratorRequest, AmendOrchestratorResponse>>() {
        })
                .annotatedWith(Names.named("amendSearchOrchestrator"))
                .to(AmendSearchOrchestrator.class);
        bind(new TypeLiteral<CacheKeyGeneratorV2<AmendSearchRepositoryRequest, FlightSolutionDTO>>() {})
                .annotatedWith(Names.named("AmendSolutionKeyGenerator"))
                .to(AmendSolutionKeyGenerator.class);
        bind(new TypeLiteral<CacheKeyGeneratorV2<BaggageCacheKeyCriteria, String>>() {
        })
                .annotatedWith(Names.named("BaggageCacheKeyGeneratorV2"))
                .to(BaggageCacheKeyGeneratorV2.class);
        bind(new TypeLiteral<CachePersistenceService<AvailabilityCacheInput>>() {
        })
                .annotatedWith(Names.named("CachePersistenceServiceImpl"))
                .to(CachePersistenceServiceImpl.class);
        bind(new TypeLiteral<CachePersistenceService<AvailabilityReqCacheInput>>() {
        })
                .annotatedWith(Names.named("SearchRequestPersistenceService"))
                .to(SearchRequestPersistenceService.class);
        bind(new TypeLiteral<CachePersistenceService<AvailabilityCacheInput>>() {
        })
                .annotatedWith(Names.named("ZeroResultPersistenceService"))
                .to(ZeroResultPersistenceService.class);
        bind(new TypeLiteral<TTLService<WorkFlowTaskAvailSearchRequest>>() {
        })
                .annotatedWith(Names.named("HardExpiryTTLService"))
                .to(HardExpiryTTLService.class);
        bind(new TypeLiteral<TTLService<TTLRequest>>() {
        })
                .annotatedWith(Names.named("HardExpiryTTLServiceV2"))
                .to(HardExpiryTTLServiceV2.class);
        bind(new TypeLiteral<TTLService<TTLRequestV3>>() {
        })
                .annotatedWith(Names.named("HardExpiryTTLServiceV3"))
                .to(HardExpiryTTLServiceV3.class);
        bind(new TypeLiteral<TTLService<WorkFlowTaskAvailSearchRequest>>() {
        })
                .annotatedWith(Names.named("SoftExpiryTTLService"))
                .to(SoftExpiryTTLService.class);
        bind(new TypeLiteral<TTLService<WorkFlowTaskAvailSearchRequest>>() {
        })
                .annotatedWith(Names.named("ColdCacheTTLService"))
                .to(ColdCacheTTLService.class);
        bind(new TypeLiteral<TTLService<TTLRequest>>() {
        })
                .annotatedWith(Names.named("SoftExpiryTTLServiceV2"))
                .to(SoftExpiryTTLServiceV2.class);
        bind(new TypeLiteral<TTLService<WorkFlowTaskAvailSearchRequest>>() {
        })
                .annotatedWith(Names.named("ZeroResultPersistenceService"))
                .to(ZeroResultPersistenceService.class);

        bind(new TypeLiteral<ExpiryServiceV2<Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest>>>() {
        })
                .annotatedWith(Names.named("ColdHardExpiryService"))
                .to(ColdSourceHardExpiryServiceImpl.class);
        bind(new TypeLiteral<ExpiryServiceV2<Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest>>>() {
        })
                .annotatedWith(Names.named("ColdSoftExpiryService"))
                .to(ColdSourceSoftExpiryServiceImpl.class);
        bind(new TypeLiteral<ExpiryServiceV2<Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest>>>() {
        })
                .annotatedWith(Names.named("HardExpiryServiceV2Impl"))
                .to(HardExpiryServiceV2Impl.class);
        bind(new TypeLiteral<ExpiryServiceV2<Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest>>>() {
        })
                .annotatedWith(Names.named("SoftExpiryServiceV2Impl"))
                .to(SoftExpiryServiceV2Impl.class);
        bind(new TypeLiteral<ExpiryServiceV2<Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest>>>() {
        })
                .annotatedWith(Names.named("ZeroResultExpiryServiceImplV2"))
                .to(ZeroResultExpiryServiceImplV2.class);
        bind(new TypeLiteral<ExpiryServiceV2<Pair<AvailabilityCacheOutput, WorkFlowTaskAvailSearchRequest>>>() {
        })
                .annotatedWith(Names.named("CacheClearExpiryEvaluationServiceImpl"))
                .to(CacheClearExpiryEvaluationServiceImpl.class);
        bind(new TypeLiteral<CacheRetrieverService<WorkFlowTaskAvailSearchRequest, AvailabilityCacheOutput>>() {
        })
                .annotatedWith(Names.named("CacheRetrieverImpl"))
                .to(CacheRetrieverImpl.class);
        bind(new TypeLiteral<CacheRetrieverService<String, SearchRequest>>() {
        })
                .annotatedWith(Names.named("SearchRequestPersistenceService"))
                .to(SearchRequestPersistenceService.class);
        bind(new TypeLiteral<ChainNodeGenerator<AvailabilityRequest, AvailabilityProcessingContext>>() {
        })
                .annotatedWith(Names.named("CacheChainNodeGenerator"))
                .to(CacheChainNodeGenerator.class);
        bind(new TypeLiteral<ChainNodeGenerator<AvailabilityRequest, AvailabilityProcessingContext>>() {
        })
                .annotatedWith(Names.named("OptimisedChainNodeGenerator"))
                .to(OptimisedChainNodeGenerator.class);

        bind(new TypeLiteral<ChainNodeGenerator<AvailabilityRequest, AvailabilityProcessingContext>>() {
        })
                .annotatedWith(Names.named("SupplierSyncCallbackChainNodesGenerator"))
                .to(SupplierSyncCallbackChainNodesGenerator.class);

        bind(new TypeLiteral<ChainNodeGenerator<AvailabilityRequest, AvailabilityProcessingContext>>() {
        })
                .annotatedWith(Names.named("SupplierChainNodeGenerator"))
                .to(SupplierChainNodeGenerator.class);
        bind(new TypeLiteral<ChainNodeGenerator<AvailabilityRequest, AvailabilityProcessingContext>>() {
        })
                .annotatedWith(Names.named("SupplierRefreshChainNodeGenerator"))
                .to(SupplierRefreshChainNodeGenerator.class);
        bind(new TypeLiteral<ChainNodeGenerator<AvailabilityRequest, AvailabilityProcessingContext>>() {
        })
                .annotatedWith(Names.named("SupplierSyncChainNodeGenerator"))
                .to(SupplierSyncChainNodeGenerator.class);
        bind(new TypeLiteral<ChainNodeGenerator<AvailabilityRequest, AvailabilityProcessingContext>>() {
        })
                .annotatedWith(Names.named("SupplierSyncRefreshChainNodeGenerator"))
                .to(SupplierSyncRefreshChainNodeGenerator.class);
        bind(Lock.class).annotatedWith(Names.named("RedisLock")).to(RedisLock.class).in(Singleton.class);
        bind(BaseCriteriaIdnService.class).annotatedWith(Names.named("BaseCriteriaIdentificationServiceImpl")).to(BaseCriteriaIdentificationServiceImpl.class).in(Singleton.class);
        bind(SupplierIdnService.class).annotatedWith(Names.named("SupplierIdentificationServiceImpl")).to(com.cleartrip.supplier.search.services.identification.impl.SupplierIdentificationServiceImpl.class).in(Singleton.class);
        bind(SupplierIdnServiceV3.class).annotatedWith(Names.named("SupplierIdentificationServiceImplV3")).to(com.cleartrip.supplier.search.services.identificationV3.impl.SupplierIdentificationServiceImplV3.class).in(Singleton.class);

        bind(new TypeLiteral<Convertor<AvailabilityCacheInput, AvailabilityCacheData>>() {
        })
                .annotatedWith(Names.named("CacheRepositoryInputConvertor"))
                .to(CacheRepositoryInputConvertor.class);
        bind(new TypeLiteral<Convertor<AvailabilityCacheData, AvailabilityCacheOutput>>() {
        })
                .annotatedWith(Names.named("CacheRepositoryOutputConvertor"))
                .to(CacheRepositoryOutputConvertor.class);
        bind(new TypeLiteral<Convertor<AvailabilityCacheInput, AvailabilityFareCacheData>>() {
        })
                .annotatedWith(Names.named("FareCacheInputConvertor"))
                .to(FareCacheInputConvertor.class);
        bind(new TypeLiteral<Convertor<AvailabilityFareCacheData, AvailabilityCacheOutput>>() {
        })
                .annotatedWith(Names.named("FareCacheOutputConvertor"))
                .to(FareCacheOutputConvertor.class);
        bind(new TypeLiteral<Convertor<AvailabilityFlightCache, Map<String, AvailabilityFlightCacheData>>>() {
        })
                .annotatedWith(Names.named("FlightCacheInputConvertor"))
                .to(FlightCacheInputConvertor.class);
        bind(new TypeLiteral<Convertor<Map<String, AvailabilityFlightCacheData>, AvailabilityFlightCache>>() {
        })
                .annotatedWith(Names.named("FlightCacheOutputConvertor"))
                .to(FlightCacheOutputConvertor.class);
        bind(new TypeLiteral<Convertor<List<FlightSolutionDTO>, ResponseMeta>>() {
        })
                .annotatedWith(Names.named("ResponseMetaConvertor"))
                .to(ResponseMetaConvertor.class);
        bind(FlightAvailabilityService.class).annotatedWith(Names.named("FlightAvailabilityOrchestrator")).to(FlightAvailabilityOrchestrator.class).in(Singleton.class);
        bind(new TypeLiteral<WorkFlowGenerator<AmendOrchestratorRequest, AmendSearchWorkflow>>() {})
                .annotatedWith(Names.named("trueAmendSearchWorkflowGenerator"))
                .to(TrueAmendSearchWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<AmendOrchestratorRequest, AmendSearchWorkflow>>() {})
                .annotatedWith(Names.named("amendSearchFilteringWorkflowGenerator"))
                .to(AmendSearchFilteringWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<AmendOrchestratorRequest, AmendSearchWorkflow>>() {})
                .annotatedWith(Names.named("cancelRebookSearchWorkflowGenerator"))
                .to(CancelRebookSearchWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<AvailabilityRequestWrapper, AvailabilityWorkFlowImpl>>() {})
                .annotatedWith(Names.named("AvailabilityWorkflowGenerator"))
                .to(AvailabilityWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<AvailabilityRequestWrapper, AvailabilityWorkFlowImpl>>() {})
                .annotatedWith(Names.named("AvailabilityWorkflowGenerator"))
                .to(AvailabilityWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<WorkFlowTaskAvailSearchRequest, SyncPersistenceWorkFlowImpl>>() {
        })
                .annotatedWith(Names.named("PersistenceWorkflowGenerator"))
                .to(PersistenceWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<CallBackWorkflowRequest, AsyncPersistenceWorkFlowImpl>>() {
        })
                .annotatedWith(Names.named("AsyncPersistenceWorkflowGenerator"))
                .to(AsyncPersistenceWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<SelectedSearchWorkflowRequest, SelectedSearchWorkflowImpl>>() {
        })
                .annotatedWith(Names.named("SelectedSearchWorkflowGenerator"))
                .to(SelectedSearchWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<SearchRequest, TaskIdentificationWorkflow>>() {
        })
                .annotatedWith(Names.named("TaskIdentificationWorkflowGenerator"))
                .to(TaskIdentificationWorkflowGenerator.class);

        bind(new TypeLiteral<WorkFlowGenerator<SearchRequest, TaskIdentificationWorkflowV2>>() {
        })
                .annotatedWith(Names.named("TaskIdentificationWorkflowGeneratorV2"))
                .to(TaskIdentificationWorkflowGeneratorV2.class);
        bind(new TypeLiteral<WorkFlowGenerator<SearchRequest, TaskIdentificationWorkflowV3>>() {
        })
                .annotatedWith(Names.named("TaskIdentificationWorkflowGeneratorV3"))
                .to(TaskIdentificationWorkflowGeneratorV3.class);
        bind(new TypeLiteral<WorkFlowsExecutor<WorkFlowAvailSearchRequest, AvailWorkFlowOutput, AvailabilityWorkFlowImpl>>() {
        })
                .annotatedWith(Names.named("AvailWorkFlowExecutor"))
                .to(AvailWorkFlowExecutor.class);
        bind(new TypeLiteral<WorkFlowsExecutor<WorkFlowTaskAvailSearchRequest, Boolean, SyncPersistenceWorkFlowImpl>>() {
        })
                .annotatedWith(Names.named("PersistenceWorkflowExecutor"))
                .to(PersistenceWorkflowExecutor.class);
        bind(new TypeLiteral<WorkFlowsExecutor<SearchRequest, AvailabilityRequest, TaskIdentificationWorkflow>>() {
        })
                .annotatedWith(Names.named("TaskIdentificationWorkFlowExecutor"))
                .to(TaskIdentificationWorkFlowExecutor.class);

        bind(new TypeLiteral<WorkFlowsExecutor<SearchRequest, AvailabilityRequest, TaskIdentificationWorkflowV2>>() {
        })
                .annotatedWith(Names.named("TaskIdentificationWorkFlowExecutorV2"))
                .to(TaskIdentificationWorkFlowExecutorV2.class);

        bind(new TypeLiteral<WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow>>() {
        }).annotatedWith(Names.named("priceItineraryWorkflowGenerator"))
                .to(PriceItineraryWorkflowGenerator.class);

        bind(new TypeLiteral<WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow>>() {
        }).annotatedWith(Names.named("optimisedResearchWorkflowGenerator"))
                .to(OptimisedResearchWorkflowGenerator.class);
        bind(new TypeLiteral<WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow>>() {
        }).annotatedWith(Names.named("supplierResearchWorkflowGenerator"))
                .to(SupplierResearchWorkflowGenerator.class);
        bind(new TypeLiteral<NodeGroup<FlightPreviewWorkFlowContext>>() {
        }).annotatedWith(Names.named("priceItineraryNodeGroup")).to(PriceItineraryNodeGroup.class);
        bind(new TypeLiteral<NodeGroup<FlightPreviewWorkFlowContext>>() {
        }).annotatedWith(Names.named("searchGroup")).to(SearchGroup.class);
        bind(new TypeLiteral<WorkFlowGenerator<FlightPreviewDomainRequest,FlightPreviewWorkflow>>(){

        }).annotatedWith(Names.named("airPriceWorkflowGenerator"))
                        .to(AirPriceWorkflowGenerator.class);

        bind(MessageHandler.class).annotatedWith(Names.named("SearchResponseMessageHandlerApplication")).to(SearchResponseMessageHandlerApplication.class).in(Singleton.class);

        bind(SearchDataPublishingService.class).annotatedWith(Names.named("SearchDataPublishingServiceImpl")).to(SearchDataPublishingServiceImpl.class).in(Singleton.class);


        bind(new TypeLiteral<WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow>>() {
        })
                .annotatedWith(Names.named("optimisedWorkflowGenerator"))
                .to(OptimisedResearchWorkflowGenerator.class)
                .in(Singleton.class);

    }
    /** all the bindings and providers for the search module **/

    /**
     * search bindings and provides/beans
     *
     * @return
     **/

    @Singleton
    @Provides
    @Named("searchRuleEngine")
    public RuleEngine createRuleEngineForSearch(Provider<SearchConfiguration> searchConfigurationProvider) throws Exception {
        String filePath = searchConfigurationProvider.get().getCommonRulesFilePath();
        final URL rulesFile = getClass().getResource(filePath);
        final RulesEngineFactory<RuleEngine> ruleRulesEngineFactory = new RulesEngineFactory<>(rulesFile, RuleEngine.class);
        return ruleRulesEngineFactory.newEngineInstance();
    }

    @Singleton
    @Provides
    @Named("cheapestFareFamilyDTOComparator")
    public Comparator<FareFamilyDTO> cheapestFareFamilyDTOComparator(){
        FareBrandPriceComparator fareBrandPriceComparator = new FareBrandPriceComparator();
        FareBrandCredKeyComparator fareBrandCredKeyComparator = new FareBrandCredKeyComparator();

        return fareBrandPriceComparator
                .thenComparing(fareBrandCredKeyComparator);
    }


    @Singleton
    @Provides
    @Named("SolutionFilterProcessorMap")
    public Map<FilterBy, SolutionFilterProcessor> prepareSolutionFilterProcessorMap(AvailabilityCacheKeyGeneratorV2 keyGenerator){
        Map<FilterBy, SolutionFilterProcessor> filterProcessorMap = new HashMap<>();
        FlightFilterProcessor flightFilterProcessor = new FlightFilterProcessor(keyGenerator);
        filterProcessorMap.put(FilterBy.FLIGHT, flightFilterProcessor);
        return filterProcessorMap;
    }

    @Singleton
    @Provides
    @Named("FareFilterProcessorMap")
    public Map<FilterBy, FareFilterProcessor> prepareFareFilterProcessorMap(){
        Map<FilterBy, FareFilterProcessor> filterProcessorMap = new HashMap<>();
        FareSolutionIdFilterProcessor fareSolutionIdFilterProcessor = new FareSolutionIdFilterProcessor();
        ComboFBCFilterProcessor comboFBCFilterProcessor = new ComboFBCFilterProcessor();
        filterProcessorMap.put(FilterBy.FARE_SOLUTION_ID, fareSolutionIdFilterProcessor);
        filterProcessorMap.put(FilterBy.COMBO_FBC, comboFBCFilterProcessor);
        return filterProcessorMap;
    }



    @Singleton
    @Provides
    @Named("retrofitClientFactory")
    public com.cleartrip.supplier.infrastructure.factory.RetroClientFactory getRetrofitClientFactory() {
        List<JsonSubTypeConverterFactoryHandler> jsonSubTypeConverterFactoryHandlers = Collections.singletonList(new JacksonConverterFactoryHandler());
        JSONConverterFactoryHandler jsonConverterFactoryHandler = new JSONConverterFactoryHandler(jsonSubTypeConverterFactoryHandlers);
        List<ConverterFactoryHandler> converterFactoryHandlerList = Collections.singletonList(jsonConverterFactoryHandler);
        final RetrofitConverterFactoryGeneratorImpl retrofitConverterFactoryGenerator = new RetrofitConverterFactoryGeneratorImpl(converterFactoryHandlerList);
        return new RetrofitClientFactoryImpl(retrofitConverterFactoryGenerator);
    }

    @Singleton
    @Provides
    @Named("searchRetrofitClientFactory")
    public RetrofitClientFactory getRetroClientFactory(@Named("retrofitClientFactory") com.cleartrip.supplier.infrastructure.factory.RetroClientFactory retroClientFactory, ObjectMapper objectMapper) {
        return new RetrofitClientFactory(retroClientFactory, objectMapper);
    }

    @Singleton
    @Provides
    @Named("smsSearchRetrofitClientFactory")
    public SmsRetrofitClientFactory getSmsRetroClientFactory(SearchConfigContainer properties, @Named("retrofitClientFactory") com.cleartrip.supplier.infrastructure.factory.RetroClientFactory retroClientFactory, ObjectMapper objectMapper) {
        return new SmsRetrofitClientFactory(properties, retroClientFactory, objectMapper);
    }

    @Singleton
    @Provides
    @Named("smsSingleSearchRetrofitClientFactory")
    public SmsSS1RetrofitClientFactory getSmsRetroClientFactory(SearchConfigContainer properties, @Named("retrofitClientFactory") com.cleartrip.supplier.infrastructure.factory.RetroClientFactory retroClientFactory) {
        return new SmsSS1RetrofitClientFactory(properties, retroClientFactory);
    }

    @Singleton
    @Provides
    public String providerBookConfiguration(Provider<SearchConfiguration> provider) {
        return provider.get().getSearchConf();
    }

    @Named("redisConnectionFactory")
    @Singleton
    @Provides
    public RedisConnectionFactory redisConnectionFactory(RedisConfiguration redisConfiguration,
                                                         ClientOptions options, ClientResources dcr) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(80);
        genericObjectPoolConfig.setMinIdle(50);
        genericObjectPoolConfig.setMaxTotal(600);
        genericObjectPoolConfig.setTestWhileIdle(true);
        genericObjectPoolConfig.setMaxWait(Duration.ofSeconds(2));
        LettucePoolingClientConfiguration lettucePoolConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(options)
                .clientResources(dcr)
                .build();
        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(redisConfiguration, lettucePoolConfig);
        lettuceConnectionFactory.afterPropertiesSet();
        return lettuceConnectionFactory;
    }


    @Named("themisClientService")
    @Singleton
    @Provides
    public ThemisClientService themisClientService(SearchConfigContainer properties) {
        final String themisUrl = properties.getPropertyValue("ct.sos.themis.url", "https://themis.cleartripcorp.me/");
        final String themisBucket = properties.getPropertyValue("ct.sos.themis.gcs.bucket", "me-prod-themis-resource-data");
        log.info("Themis configs themis-url: {} themis-bucket:{}", themisBucket, themisUrl);
        return new ThemisClientService(themisUrl, themisBucket , 5);
    }

    @Named("promoCodeRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<PromoCodeRuleEngine> getPromoCodeRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<PromoCodeRuleEngine> wrapper = new RuleEngineWrapper<>(PromoCodeRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/PromoCodeRules-prod_69059c95d505767953644512f01aba74.xlsx"));
        themisClientService.registerResource("ct.sos.rule.PromoCodeRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("searchTaskRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<SearchTaskRuleEngine> getSearchTaskRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<SearchTaskRuleEngine> wrapper = new RuleEngineWrapper<>(SearchTaskRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/prod/searchTaskRule/searchTaskRule.xlsx"));
        themisClientService.registerResource("ct.sos.rule.SearchTaskRule", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("companyConfigRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<CompanyConfigRuleEngine> getCompanyConfigRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<CompanyConfigRuleEngine> wrapper = new RuleEngineWrapper<>(CompanyConfigRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/companyConfigRules-prod_5c710a3a29be513388563f1bc865d350.xlsx"));
        themisClientService.registerResource("ct.sos.rule.companyConfigRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("amendSearchRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<AmendSearchRuleEngine>  getAmendSearchRuleEngine(@Named("themisClientService") ThemisClientService themisClientService, Provider<SearchConfiguration> searchConfigurationProvider) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<AmendSearchRuleEngine> wrapper = new RuleEngineWrapper<>(AmendSearchRuleEngine.class);
        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/AmendSearchRuleEngine-prod_14f39180aab15acd0266119a10ab69ce.xlsx"));
//        themisClientService.registerResource("ct.sos.rule.amendSearch", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("additionalSupplierRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<AdditionalSupplierRuleEngine> getAdditionalSupplierRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<AdditionalSupplierRuleEngine> wrapper = new RuleEngineWrapper<>(AdditionalSupplierRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/additionalSupplierRulesV2-prod_a7995d63aaad4506e70b51072789e721.xlsx"));
        themisClientService.registerResource("ct.sos.rule.additionalSupplierRulesV2", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("fareBenefitRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FareBenefitRuleEngine> getFareBenefitRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FareBenefitRuleEngine> wrapper = new RuleEngineWrapper<>(FareBenefitRuleEngine.class);
        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/fareBenefitRule_V3.xlsx"));
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/fareBenefitRule_V2.xlsx"));
//        themisClientService.registerResource("ct.sos.rule.fareBenefitsRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("fareBrandProductClassUniverseRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FareBrandProductClassUniverseRuleEngine> getFareBrandProductClassUniverseRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FareBrandProductClassUniverseRuleEngine> wrapper = new RuleEngineWrapper<>(FareBrandProductClassUniverseRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/FareBrandProductClassUniverse-prod_fe200e8fdab6a870d4ceea2f85af533e.xlsx"));
        themisClientService.registerResource("ct.sos.rule.fareBrandProductClassUniverse", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("ctcConfigRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<CtcConfigRuleEngine> getCtcConfigRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<CtcConfigRuleEngine> wrapper = new RuleEngineWrapper<>(CtcConfigRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/CtcConfigRules-prod_b0913417c7314e23dec3f8fb8728219c.xlsx"));
        themisClientService.registerResource("ct.sos.rule.ctcConfigRuleEngine", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("revenueConfigRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<RevenueConfigRuleEngine> getRevenueConfigRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<RevenueConfigRuleEngine> wrapper = new RuleEngineWrapper<>(RevenueConfigRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/revenueConfigRules-prod_d714a6ca838a6b51528785d6ce7764ee.xlsx"));
        themisClientService.registerResource("ct.sos.rule.revenueConfigRuleEngine", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("fareLoggingRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FareLoggingRuleEngine> getFareLoggingRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FareLoggingRuleEngine> wrapper = new RuleEngineWrapper<>(FareLoggingRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/fareLoggingRules-prod_118620fa75304096f18ad7084bbdf1fc.xlsx"));
        themisClientService.registerResource("ct.sos.rule.fareLoggingRuleEngine", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }


    @Named("supplierEligibilityRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<SupplierEligibiltyRuleEngine> getSupplierEligibilityRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<SupplierEligibiltyRuleEngine> wrapper = new RuleEngineWrapper<>(SupplierEligibiltyRuleEngine.class);
        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/supplierEligibilityConfig-prod_d20d0042f87409db941841784898c1a8.xlsx"));
//        themisClientService.registerResource("ct.sos.rule.supplierEligibilityRuleEngineV2", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("cacheConfigRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<CacheConfigRuleEngine> getCacheConfigRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<CacheConfigRuleEngine> wrapper = new RuleEngineWrapper<>(CacheConfigRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/cacheConfig-prod_db65dcf8e96625fde1b655ea479a96b8.xlsx"));
        themisClientService.registerResource("ct.sos.rule.CacheConfigRuleEngine", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }


    @Named("cacheExpiryRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<CacheExpiryRuleEngine> getCacheExpiryRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<CacheExpiryRuleEngine> wrapper = new RuleEngineWrapper<>(CacheExpiryRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/cacheExpiryEngineRules-prod_dd80697ea514d32c9e837e1ff96cf94a.xlsx"));
        themisClientService.registerResource("ct.sos.ttl.cacheExpiryEngine", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }


    @Named("SffBookTaskIdentificationRules")
    @Singleton
    @Provides
    public RuleEngineWrapper<SupplierEligibiltySffBookRuleEngine> getSupplierEligibilityRuleEngineForSff(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<SupplierEligibiltySffBookRuleEngine> wrapper = new RuleEngineWrapper<>(SupplierEligibiltySffBookRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/SffBookTaskIdentificationRules-prod_80e3e5d814442dbd6065390785e8e1ae.xlsx"));
        themisClientService.registerResource("ct.sos.rule.SffBookTaskIdentificationRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("fareBrandRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FareBrandRuleEngine> getFareBrandRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FareBrandRuleEngine> wrapper = new RuleEngineWrapper<>(FareBrandRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/fareBrandRules-prod_bab3fcb6750d46b7a2e312c4c1b39b66.xlsx"));
        themisClientService.registerResource("ct.sos.rule.fareBrandRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("productClassFareTypeRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<ProductClassFareTypeRuleEngine> getFareBrandDetailsRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<ProductClassFareTypeRuleEngine> wrapper = new RuleEngineWrapper<>(ProductClassFareTypeRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/fareBrandDetailsRules-prod_f86f43caab270b8e79728385ef3d29a6.xlsx"));
        themisClientService.registerResource("ct.sos.rule.fareBrandDetailsRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }
    @Named("applicableFareBrandPromoCodeRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<ApplicableFareBrandPromoCodeRuleEngine> getApplicableFareBrandPromoCodeRuleEngineRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<ApplicableFareBrandPromoCodeRuleEngine> wrapper = new RuleEngineWrapper<>(ApplicableFareBrandPromoCodeRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/ApplicableFareBrandPromoCodeDetails-prod_6ce0a9f3f6a9e4b63940bbf50b091cff.xlsx"));
        themisClientService.registerResource("ct.sos.rule.applicableFareBrandPromoCodeDetails", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named("CompanyConfigResource")
    @Singleton
    @Provides
    public CompanyConfigResource getCompanyConfigResource(@Named("companyConfigRuleEngine") RuleEngineWrapper<CompanyConfigRuleEngine> ruleEngine) {
        return new CompanyConfigResourceImpl(ruleEngine);
    }

    @Named("EnrichFlightSolutionRepository")
    @Singleton
    @Provides
    public EnrichFlightSolutionRepository getEnrichFlightSolutionRepository(){
        return new EnrichFlightSolutionRepository();
    }

    @Named("KafkaSearchDataPublisher")
    @Singleton
    @Provides
    public SearchDataPublisher getSearchDataPublisher(@Named("SearchKafkaProducer") KafkaProducer kafkaProducer,
                                                      SearchConfigContainer searchConfigContainer) {
        String topicName = searchConfigContainer.getPropertyValue("ct.air.sos.search.supplierdata.publish.topicname", StringUtils.EMPTY);
        return new KafkaSearchDataPublisher(kafkaProducer, topicName);
    }

    @Named("SearchKafkaProducer")
    @Singleton
    @Provides
    public KafkaProducer getSearchKafkaProducer(Provider<SearchConfiguration> searchConfigurationProvider) {
        SearchConfiguration searchConfiguration = searchConfigurationProvider.get();
        KafkaConf kafkaConf = searchConfiguration.getKafkaConf();
        return new KafkaProducer(kafkaConf.getBrokerList(),
                kafkaConf.getRequestTimeout(),
                kafkaConf.getTransactionTimeout(),
                kafkaConf.getMessageTimeout(),
                kafkaConf.getSocketTimeout(),
                kafkaConf.getMaxMessageSize());
    }


   /* @Singleton
    @Providesaero
    @Named("redisZeroConnectionFactory")
    public RedisConnectionFactory redisZeroConnectionFactory(RedisConfiguration redisConfiguration,
                                                             ClientOptions options, ClientResources dcr) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(80);
        genericObjectPoolConfig.setMinIdle(50);
        genericObjectPoolConfig.setMaxTotal(400);
        LettucePoolingClientConfiguration lettucePoolConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(options)
                .clientResources(dcr)
                .build();
        return new LettuceConnectionFactory(redisConfiguration, lettucePoolConfig);
    }

    @Singleton
    @Provides
    @Named("redisBaggageConnectionFactory")
    public RedisConnectionFactory redisBaggageConnectionFactory(RedisConfiguration redisConfiguration,
                                                                ClientOptions options, ClientResources dcr) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(80);
        genericObjectPoolConfig.setMinIdle(50);
        genericObjectPoolConfig.setMaxTotal(400);
        LettucePoolingClientConfiguration lettucePoolConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(options)
                .clientResources(dcr)
                .build();
        return new LettuceConnectionFactory(redisConfiguration, lettucePoolConfig);
    }

    @Singleton
    @Provides
    @Named("redisMetaConnectionFactory")
    public RedisConnectionFactory redisMetaConnectionFactory(RedisConfiguration redisConfiguration,
                                                             ClientOptions options, ClientResources dcr) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(80);
        genericObjectPoolConfig.setMinIdle(50);
        genericObjectPoolConfig.setMaxTotal(600);
        LettucePoolingClientConfiguration lettucePoolConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(genericObjectPoolConfig)
                .clientOptions(options)
                .clientResources(dcr)
                .build();
        return new LettuceConnectionFactory(redisConfiguration, lettucePoolConfig);
    }*/

    @Singleton
    @Provides
    public RedisConfiguration redisStandaloneConfiguration(SearchConfigContainer properties) {
        //return new RedisStandaloneConfiguration(env.getProperty("redis.host"), Integer.parseInt(env.getProperty("redis.port")));
        return new RedisStaticMasterReplicaConfiguration(properties.getPropertyValue("redis.host"), properties.getIntPropertyValue("redis.port", 6379));
    }

    @Singleton
    @Provides
    public SearchConfigContainer getIProperties(Provider<SearchConfiguration> searchConfigurationProvider,
                                                ObjectMapper objectMapper,
                                                ConfigManager configManager) {
        BucketRegistrationCriteria<Map<String, Object>> bucketRegistrationCriteria = getMapBucketRegistrationCriteria(searchConfigurationProvider);
        configManager.registerBucket(bucketRegistrationCriteria);

        String bucketName = BucketNameCreator.getBucketName("search");
        BucketDataWrapper<Map<String, String>> initialData = configManager.getBucketData(bucketName);
        log.info("Fetching data for bucket: {}", bucketName);

        SearchConfigContainer searchConfigContainer = new SearchConfigContainer(objectMapper);
        Map<String, String> currMap = initialData.getData().get();
        searchConfigContainer.update(currMap);
        IListener<Map<String, String>> listener = new PropertiesListener(searchConfigContainer, objectMapper);
        configManager.addListener(listener, BucketNameCreator.getBucketName("search"));

        return searchConfigContainer;
    }


    private BucketRegistrationCriteria<Map<String, Object>> getMapBucketRegistrationCriteria(Provider<SearchConfiguration> searchConfigurationProvider) {
        MySqlConf mySqlConf = searchConfigurationProvider.get().getMySqlConf();
        RepositoryConfig repositoryConfig = new MySQLRepositoryConfig(new MySQLConnectionParams(mySqlConf.getUrl(),
                mySqlConf.getUserName(),
                mySqlConf.getPassword()));
        DecoderConfig<Map<String, Object>> decoderConfig = new DecoderConfig<>(DecoderType.OBJECT_MAPPER, new Datatype<>(new TypeReference<>() {
        }));
        String bucketName = BucketNameCreator.getBucketName("search");
        log.info("registering bucket : {}", bucketName);
        return new BucketRegistrationCriteria<>(bucketName, repositoryConfig, decoderConfig);
    }

    @Singleton
    @Provides
    ClientResources clientResources() {
        ClientResources.builder()
                .ioThreadPoolSize(10)
                .computationThreadPoolSize(10)
                .build();
        return DefaultClientResources.create();
    }

    @Singleton
    @Provides
    public ClientOptions clientOptions() {
        return ClientOptions.builder()
                .disconnectedBehavior(ClientOptions.DisconnectedBehavior.REJECT_COMMANDS)
                .autoReconnect(true)
                .build();
    }

    @Singleton
    @Provides
    public RedisTemplate<String, FlightSolution> redisTemplate(@Named("redisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, FlightSolution> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.setHashValueSerializer(new SnappyRedisSerializer<>());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    @Singleton
    @Provides
    @Named("FareBenefitInputConverterFactory")
    public FareBenefitInputConverterFactory getFareBenefitInputConverterFactory() {
        return new FareBenefitInputConverterFactory();
    }

    @Singleton
    @Provides
    @Named("FlightSolutionAvailResponseAdapter")
    public FlightSolutionAvailResponseAdapter getFlightSolutionAvailResponseAdapter(@Named("FareBenefitInputConverterFactory") FareBenefitInputConverterFactory fareBenefitInputConverterFactory) {
        return new FlightSolutionAvailResponseAdapter(fareBenefitInputConverterFactory);
    }

    @Singleton
    @Provides
    public RedisTemplate<String, AvailabilityCacheData> redisFltTemplate(@Named("redisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, AvailabilityCacheData> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.setValueSerializer(new ZstdRedisSerializer<>());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    @Singleton
    @Provides
    @Named("AerospikeClient")
    public AerospikeClient aerospikeClient(SearchConfigContainer properties, ObjectMapper mapper) {
        AerospikeHosts aerospikeHosts = getAerospikeHosts(properties, mapper);
        Host[] hosts = aerospikeHosts.getHosts().stream()
                .map(host -> new Host(host.getConnectionUrl(), host.getPort()))
                .toArray(Host[]::new);
        return new AerospikeClient(getClientPolicy(), hosts);
    }

    @NotNull
    private ClientPolicy getClientPolicy() {
        ClientPolicy policy = new ClientPolicy();
        policy.readPolicyDefault.replica = Replica.MASTER_PROLES;
        return policy;
    }

    private AerospikeHosts getAerospikeHosts(SearchConfigContainer properties, ObjectMapper mapper) {
        try {
            String hostName = properties.getPropertyValue("ct.air.sc.aerospike.cluster.hosts.details", "{\"hosts\":[{\"connectionUrl\":\"aerospike-node-1.gcp-cltp.me\",\"port\":3000},{\"connectionUrl\":\"aerospike-node-2.gcp-cltp.me\",\"port\":3000},{\"connectionUrl\":\"aerospike-node-3.gcp-cltp.me\",\"port\":3000}]}");
            return mapper.readValue(hostName, AerospikeHosts.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


    @Singleton
    @Provides
    @Named("AerospikeClusterConfiguration")
    public AerospikeClusterConfiguration aerospikeClusterConfiguration(SearchConfigContainer properties) {
        String nameSpace = properties.getPropertyValue("ct.air.sc.aerospike.cluster.host.namespace", "availStore");
        String binName = properties.getPropertyValue("ct.air.sc.aerospike.cluster.host.binName", "search-data");
        return new AerospikeClusterConfiguration(nameSpace, null, binName);
    }



    @Singleton
    @Provides
    public RedisTemplate<String, SearchRequest> redisSearchReqTemplate(@Named("redisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, SearchRequest> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.setValueSerializer(new ZstdRedisSerializer<>());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    @Singleton
    @Provides
    public SearchMongoRepository getDataBagRepository(SearchConfigContainer properties) {
        MongoDBConnectionParam mongoDBConnectionParam = new MongoDBConnectionParam(
                properties.getIntPropertyValue("ct.air.sis.databag.connectTimeout", 5000),
                properties.getIntPropertyValue("ct.air.sis.databag.serverSelectionTimeout", 5000),
                properties.getPropertyValue("ct.air.sis.databag.uri", "mongodb://qa2-mongo-1.cltp.com,qa2-mongo-2.cltp.com"),
                properties.getPropertyValue("ct.air.sis.databag.database", "supplyCoreDB"),
                properties.getPropertyValue("ct.air.sis.databag.collection", "SupplyCoreDataBag"),
                properties.getIntPropertyValue("ct.air.sis.databag.collection.maxConnectionIdleTime", 10000),
                properties.getIntPropertyValue("ct.air.sis.databag.collection.minConnectionPerHost", 40),
                properties.getIntPropertyValue("ct.air.sis.databag.collection.maxConnectionPerHost", 250));
       /* final MongoClientFactory mongoClientFactory = new MongoClientFactory(mongoDBConnectionParam);
        final MongoCollection<Document> searchMongoCollection = mongoClientFactory.getClient()
                .getDatabase(mongoDBConnectionParam.getDatabase())
                .getCollection(mongoDBConnectionParam.getCollectionName());*/
        return new SearchMongoRepository(null);
    }

    @Singleton
    @Provides
    public RequestPersistenceMongoRepository getSISRequestPersistenceRepository(SearchConfigContainer properties) throws JsonProcessingException {
        MongoDBConnectionParam mongoDBConnectionParam = new MongoDBConnectionParam(
                properties.getIntPropertyValue("ct.air.sis.databag.connectTimeout", 12000),
                properties.getIntPropertyValue("ct.air.sis.databag.serverSelectionTimeout", 12000),
                properties.getPropertyValue("ct.air.sis.databag.uri", "mongodb://qa2-mongo-1.cltp.com,qa2-mongo-2.cltp.com"),
                properties.getPropertyValue("ct.air.sis.databag.database", "supplyCoreDB"),
                properties.getPropertyValue("ct.air.sis.databag.collection", "SupplyCoreSearchReq"),
                properties.getIntPropertyValue("ct.air.sis.databag.collection.maxConnectionIdleTime", 6000),
                properties.getIntPropertyValue("ct.air.sis.databag.collection.minConnectionPerHost", 5),
                properties.getIntPropertyValue("ct.air.sis.databag.collection.maxConnectionPerHost", 20));
        log.error("Mongo Connection Properties: {}", (new ObjectMapper()).writeValueAsString(mongoDBConnectionParam));
        final MongoClientFactory mongoClientFactory = new MongoClientFactory(mongoDBConnectionParam);
        final MongoCollection<Document> searchMongoCollection = mongoClientFactory.getClient()
                .getDatabase(mongoDBConnectionParam.getDatabase())
                .getCollection(mongoDBConnectionParam.getCollectionName());
        return new RequestPersistenceMongoRepository(searchMongoCollection);
    }

    @Singleton
    @Provides
    public RedisTemplate<String, AvailabilityFareCacheData> redisAvailFareTemplate(@Named("redisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, AvailabilityFareCacheData> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.setValueSerializer(new ZstdRedisSerializer<>());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    @Singleton
    @Provides
    public RedisTemplate<String, AvailabilityFlightCacheData> redisAvailFlightTemplate(@Named("redisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, AvailabilityFlightCacheData> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.setValueSerializer(new ZstdRedisSerializer<>());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    @Singleton
    @Provides
    public RedisTemplate<String, SisSearchResult> redisSisTemplate(@Named("redisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, SisSearchResult> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.setHashValueSerializer(new SnappyRedisSerializer<>());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    /*@Singleton
    @Provides
    public RedisTemplate<String, Integer> redisZeroResultTemplate(@Named("redisZeroConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, Integer> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    @Singleton
    @Provides
    public RedisTemplate<String, CacheMetaData> availMetaRedisTemplate(@Named("redisMetaConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, CacheMetaData> flightSolutionRedisTemplate = new RedisTemplate<>();
        flightSolutionRedisTemplate.setConnectionFactory(cf);
        flightSolutionRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flightSolutionRedisTemplate.afterPropertiesSet();
        return flightSolutionRedisTemplate;
    }

    @Singleton
    @Provides
    public RedisTemplate<String, SolutionBaggageResponse> redisFreeBaggageTemplate(@Named("redisBaggageConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, SolutionBaggageResponse> freeBaggageRedisTemplate = new RedisTemplate<>();
        freeBaggageRedisTemplate.setConnectionFactory(cf);
        freeBaggageRedisTemplate.setKeySerializer(new StringRedisSerializer());
        freeBaggageRedisTemplate.setValueSerializer(new SnappyRedisSerializer<>());
        freeBaggageRedisTemplate.afterPropertiesSet();
        return freeBaggageRedisTemplate;
    }*/

    @Singleton
    @Provides
    public RedisTemplate<String, FlightSolutionDTO> fareFamilyRedisTemplate(@Named("redisConnectionFactory") RedisConnectionFactory cf) {
        RedisTemplate<String, FlightSolutionDTO> fareFamilyRedisTemplate = new RedisTemplate<>();
        fareFamilyRedisTemplate.setConnectionFactory(cf);
        fareFamilyRedisTemplate.setKeySerializer(new StringRedisSerializer());
        fareFamilyRedisTemplate.setValueSerializer(new ZstdRedisSerializer<>());
        fareFamilyRedisTemplate.afterPropertiesSet();
        return fareFamilyRedisTemplate;
    }

    @Singleton
    @Provides
    public RedisClient redisClient(SearchConfigContainer properties) {
        RedisURI redisURI = RedisURI.builder()
                .withHost(properties.getPropertyValue("redis.host"))
                .withPort(properties.getIntPropertyValue("redis.port", 6379))
                .build();
        RedisClient client = RedisClient.create(redisURI);
        return client;
    }

    @Singleton
    @Provides
    public GenericObjectPool<StatefulRedisConnection<String, String>> statefulRedisConnection(RedisClient redisClient,
                                                                                              SearchConfigContainer properties) {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(properties.getIntPropertyValue("ct.air.sis.redis.pool.max.idle.connections", 20));
        genericObjectPoolConfig.setMinIdle(properties.getIntPropertyValue("ct.air.sis.redis.pool.min.idle.connections", 10));
        genericObjectPoolConfig.setMaxTotal(properties.getIntPropertyValue("ct.air.sis.redis.pool.max.total.connections", 300));
        return ConnectionPoolSupport
                .createGenericObjectPool(() -> redisClient.connect(), genericObjectPoolConfig);
    }

    @Singleton
    @Provides
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new GuavaModule());
        mapper.registerModule(new Jdk8Module());
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.setVisibility(mapper
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .getSerializationConfig()
                .getDefaultVisibilityChecker()
                .withFieldVisibility(JsonAutoDetect.Visibility.ANY)
                .withGetterVisibility(JsonAutoDetect.Visibility.NONE)
                .withSetterVisibility(JsonAutoDetect.Visibility.NONE)
                .withCreatorVisibility(JsonAutoDetect.Visibility.ANY));
        return mapper;
    }


    @Singleton
    @Provides
    @Named("BaggageCacheEventBusV2")
    public EventBus getBaggageCacheEventBusV2(BaggageCachingService baggageCachingService,
                                              HardExpiryTTLServiceV3 hardExpiryTTLService,
                                              SearchConfigContainer properties) {
        EventBus eventBus = new AsyncEventBus(Executors.newFixedThreadPool(properties.getIntPropertyValue("ct.air.sis.baggage.executor.thread.pool.count", 50)));
        eventBus.register(new BaggageListenerV2(baggageCachingService, hardExpiryTTLService));
        return eventBus;
    }

    @Singleton
    @Provides
    @Named("FareRuleCacheEventBus")
    public EventBus getFareRuleCacheEventBus(BaggageCachingService baggageCachingService,
                                             SearchConfigContainer properties) {
        EventBus eventBus = new AsyncEventBus(Executors.newFixedThreadPool(properties.getIntPropertyValue("ct.air.sis.farerules.executor.thread.pool.count", 10)));
        eventBus.register(new FareRuleListener());
        return eventBus;
    }

    @Singleton
    @Provides
    @Named("SMSSupplierExecutorMap")
    public Map<Supplier, Executor> getSMSSupplierExecutorMap(SearchConfigContainer properties) {
        return Arrays.stream(Supplier.values()).collect(Collectors.toMap(Function.identity(), supplier ->
                getSMSExecutor(properties.getIntPropertyValue(SisConfigKeys.SUPPLIER_CONFIG_KEY_PREFIX + supplier + SisConfigKeys.SMS_THREAD_COUNT_SUFFIX, 2))));
    }

    /**
     * Used in order to push data to new-relic on production env. Don't change the bean name as it looks for exact name.
     *
     * @param
     * @return
     */
//    @Profile(StatsConfig.PRODUCTION_PROFILE)
    @Singleton
    @Provides
    @Named("CTMeterRegistry")
    public MeterRegistry prodMeterRegistry(SearchConfigContainer properties, EventPublisher eventPublisher) {
        return new CTMeterRegistry(new CTMeterRegistryConfig(properties::getPropertyValue), eventPublisher);
    }

    /**
     * Used for dev/qa profiles when data is not pushed to new-relic. Don't change the bean name as it looks for exact name.
     *
     * @return
     */
//    @Profile(NON_PRODUCTION_PROFILE)
    @Singleton
    @Provides
    @Named("SimpleMeterRegistry")
    public MeterRegistry nonProdMeterRegistry() {
        return new SimpleMeterRegistry();
    }

    @Singleton
    @Provides
    public CtNewRelicConfig ctNewRelicConfig(SearchConfigContainer properties) {
        return new CtNewRelicConfig(properties::getPropertyValue);
    }

    @Singleton
    @Provides
    public EventPublisher newRelicEventPublisher(CtNewRelicConfig ctNewRelicConfig) {
        return new NewRelicEventPublisher(ctNewRelicConfig);
    }

    private Executor getSMSExecutor(Integer threadCount) {
        return Executors.newFixedThreadPool(threadCount);
    }

    @Singleton
    @Provides
    public HttpClient getHttpClient(SearchConfigContainer commonCachedProperties) {
        SslContextFactory.Client sslContextFactory = new SslContextFactory.Client();
        HttpClient httpClient = new HttpClient(sslContextFactory);
        httpClient.setMaxConnectionsPerDestination(commonCachedProperties.getIntPropertyValue("ct.air.sis.newrelic.max.connection", 200));
        httpClient.setIdleTimeout(commonCachedProperties.getIntPropertyValue("ct.air.sis.newrelic.idle.timeout", 60000));
        httpClient.setFollowRedirects(false);
        httpClient.setConnectTimeout(commonCachedProperties.getIntPropertyValue("ct.air.sis.newrelic.connection.timeout", 20000));
        try {
            httpClient.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return httpClient;
    }

    @Singleton
    @Provides
    @Named("searchStatsHelper")
    public StatsHelper getStatsHelper(Provider<SearchConfiguration> searchConfiguration, MonitoringBeanContext monitoringBeanContext) {
        final MonitoringParam monitoringParam = searchConfiguration.get().getMonitoringParam();
        final String topicName = monitoringParam.getStats().getTopicName();
        return new StatsHelper(monitoringBeanContext.getCommonPublisher(), topicName);
    }

    @Singleton
    @Provides
    @Named("searchNewRelicHelper")
    public NewRelicHelper getNewRelicHelper(Provider<SearchConfiguration> searchConfigurationProvider, MonitoringBeanContext monitoringBeanContext) {
        final MonitoringParam monitoringParam = searchConfigurationProvider.get().getMonitoringParam();
        final String tableName = monitoringParam.getNewRelic().getTableName(); // This table name is not used for any search flows. Correct table name gets overwritten when publishing from the NewRelicUtil
        CommonPublisher commonPublisher = monitoringBeanContext.getCommonPublisher();
        return new NewRelicHelper(commonPublisher, tableName);

    }

    @Singleton
    @Provides
    @Named("newRelicEventTransmitter")
    public NewRelicEventTransmitter getNewRelicEventTransmitter(NewRelicDataSender newRelicDataSender, NewRelicAuthConfig newRelicAuthConfig) {
        return new NewRelicEventTransmitter(newRelicDataSender, newRelicAuthConfig);
    }

    @Singleton
    @Provides
    @Named("NewRelicEventBus")
    public EventBus getNewRelicEventBus(NewRelicEventTransmitter newRelicEventTransmitter) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(1, 1, 120,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(150000));
        EventBus eventBus = new AsyncEventBus(executor);
        eventBus.register(new NewRelicListener(newRelicEventTransmitter));
        return eventBus;
    }

    @Singleton
    @Provides
    @Named("newRelicUtil")
    public NewRelicUtil getNewRelicUtil(NewRelicEventTransmitter newRelicEventTransmitter, SearchConfigContainer properties, @Named("NewRelicEventBus") EventBus eventBus) {
        return new NewRelicUtil(newRelicEventTransmitter, properties, eventBus);
    }

    @Singleton
    @Provides
    @Named("flightCacheService")
    CachingService<AvailabilityCacheData> flightCacheService(RedisTemplate<String, AvailabilityCacheData> redisTemplate) {
        return new RedisCachingServiceTemplate<>(redisTemplate);
    }

    @Singleton
    @Provides
    @Named("availSearchReqCacheService")
    CachingService<SearchRequest> availSearchReqCacheService(RedisTemplate<String, SearchRequest> redisTemplate) {
        return new RedisCachingServiceTemplate<>(redisTemplate);
    }

    @Singleton
    @Provides
    @Named("availFareCacheService")
    CachingService<AvailabilityFareCacheData> availFareCacheService(RedisTemplate<String, AvailabilityFareCacheData> redisTemplate) {
        return new RedisCachingServiceTemplate<>(redisTemplate);
    }

    @Singleton
    @Provides
    @Named("availFlightCacheService")
    BulkCachingOperations<AvailabilityFlightCacheData> availFlightCacheService(RedisTemplate<String, AvailabilityFlightCacheData> redisTemplate) {
        return new RedisBulkCachingServiceTemplate<>(redisTemplate);
    }

    @Singleton
    @Provides
    @Named("fareFamilyCacheService")
    CachingService<FlightSolution> fareFamilyCacheService(RedisTemplate<String, FlightSolution> redisTemplate) {
        return new RedisBulkCachingServiceTemplate<>(redisTemplate);
    }

    @Singleton
    @Provides
    @Named("AerospikeCachingService")
    CachingService<ByteArray> aerospikeStore(@Named("AerospikeClient") Provider<AerospikeClient> aerospikeClientProvider,
                                             @Named("AerospikeClusterConfiguration") Provider<AerospikeClusterConfiguration> configurationProvider) {
        return new AerospikeCachingService(aerospikeClientProvider.get(),
                configurationProvider.get(),
                new ZstdCompressionImpl());
    }


    @Singleton
    @Provides
    @Named("availWorkFlowExecutorService")
    ExecutorService availWorkFlowExecutorService() {
        //return new ThreadPoolExecutor(500, 2500, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(2));
        return Executors.newFixedThreadPool(1200);
    }

    @Singleton
    @Provides
    @Named("taskIdentificationWorkFlowExecutorService")
    ExecutorService TaskIdentificationWorkFlowExecutorService() {
        return new ThreadPoolExecutor(1, 2, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(2));
    }

    @Singleton
    @Provides
    @Named("persistenceWorkflowExecutorService")
    ExecutorService PersistenceWorkflowExecutorService() {
        return new ThreadPoolExecutor(1, 2, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(2));
    }

    @Singleton
    @Provides
    @Named("SelectedSearchEventBus")
    public EventBus getSelectedSearchEventBus(
            SelectedSearchEventSubscriber selectedSearchEventSubscriber
    ) {
        EventBus eventBus = new AsyncEventBus(Executors.newFixedThreadPool(20));
        eventBus.register(selectedSearchEventSubscriber);
        return eventBus;
    }


    @Singleton
    @Provides
    @Named("SupplierTaskFactory")
    @SneakyThrows
    public SupplierTaskFactory getSupplierTaskFactory(AvailabilityCacheKeyGeneratorV2 keyGenerator,
                                                      @Named("RedisLock") Lock lock,
                                                      @Named("PersistenceWorkflowGenerator") WorkFlowGenerator<WorkFlowTaskAvailSearchRequest, SyncPersistenceWorkFlowImpl> syncDataPersistenceWorkflowGenerator,
                                                      @Named("AsyncSuppliers") Set<Supplier> asyncSuppliers,
                                                      SMSService smsService) {
        return SupplierTaskFactory.builder()
                .keyGenerator(keyGenerator)
                .lock(lock)
                .syncDataPersistenceWorkflowGenerator(syncDataPersistenceWorkflowGenerator)
                .asyncSuppliers(asyncSuppliers)
                .smsService(smsService)
                .build();
    }

    @Singleton
    @Provides
    @Named("AsyncSuppliers")
    @SneakyThrows
    public Set<Supplier> asyncSuppliers(SearchConfigContainer properties,
                                        @Named("PlainObjectMapper") ObjectMapper objectMapper) {
        String asyncSupplierString = properties.getPropertyValue("ct.sis.async.suppliers.v2", "[]");
        return objectMapper.readValue(asyncSupplierString, new TypeReference<Set<Supplier>>() {
        });
    }


    @Singleton
    @Provides
    @Named("SearchResponseMessageHandlerApplication")
    @SneakyThrows
    public SearchResponseMessageHandlerApplication getSearchResponseMessageHandler(@Named("PlainObjectMapper") ObjectMapper objectMapper,
                                                                                   @Named("AsyncPersistenceWorkflowGenerator") WorkFlowGenerator<CallBackWorkflowRequest, AsyncPersistenceWorkFlowImpl> asyncPersistenceWorkflowGenerator,
                                                                                   @Named("CustomGsonMapper") Gson customGsonMapper,
                                                                                   AvailabilitySMSAdapterFactoryV2 availabilitySMSAdapterFactoryV2,
                                                                                   @Named("RedisLock") Lock lock) {
        return new SearchResponseMessageHandlerApplication(objectMapper, customGsonMapper,
                asyncPersistenceWorkflowGenerator, availabilitySMSAdapterFactoryV2, lock);
    }

    @Singleton
    @Provides
    @Named("CustomGsonMapper")
    public Gson getCustomGsonMapper(CallbackPayloadDeSerializer callbackPayloadDeSerializer) {
        return new GsonBuilder()
                .registerTypeAdapter(CallBackPayloadDto.class, callbackPayloadDeSerializer)
                .create();
    }

    @Singleton
    @Provides
    @Named("PlainObjectMapper")
    public ObjectMapper getPlainObjectMapper() {
        return new ObjectMapper();
    }

    @Singleton
    @Provides
    @Named("TravelFusionSupportedSuppliers")
    @SneakyThrows
    public Map<SourceTypeOuterClass.SourceType, List<String>> getTravelFusionSupportedSuppliers(ObjectMapper objectMapper,
                                                                                                SearchConfigContainer properties) {
        String supportedSuppliers = properties.getPropertyValue("ct.air.travelfusion.supported.suppliers", "{}");
        return objectMapper.readValue(supportedSuppliers, new TypeReference<Map<SourceTypeOuterClass.SourceType, List<String>>>() {
        });
    }

    @Singleton
    @Provides
    @Named("TfBundleInfoMap")
    @SneakyThrows
    public Map<CabinType, TFAirlineBundlingDTO> getTravelFusionBundleInfo(@Named("PlainObjectMapper") ObjectMapper objectMapper,
                                                                          SearchConfigContainer properties) {
        String tfBundleInfo = properties.getPropertyValue("ct.air.travelfusion.bundling.info", "{\"ECONOMY\":{\"OV\":{\"Light\":{\"lite\":true,\"hbag\":true},\"Friendly\":{\"lite\":true,\"hbag\":false}},\"PA\":{\"Discount (No Bags)\":{\"lite\":false,\"hbag\":true},\"Standard (1 Bag)\":{\"lite\":false,\"hbag\":false}},\"J9\":{\"Light\":{\"lite\":true,\"hbag\":true}}}}");
        return objectMapper.readValue(tfBundleInfo, new TypeReference<Map<CabinType, TFAirlineBundlingDTO>>() {
        });
    }


    @Singleton
    @Provides
    @Named("brandBenefitRuleEngine")
    public BrandBenefitRuleEngine getBrandBenefitRuleEngine(Provider<SearchConfiguration> searchConfigurationProvider) {
        String filePath = searchConfigurationProvider.get().getBrandBenefitRulesFilePath();
        final URL rulesFile = getClass().getResource(filePath);
        final RulesEngineFactory<BrandBenefitRuleEngine> ruleRulesEngineFactory = new RulesEngineFactory<>(rulesFile, BrandBenefitRuleEngine.class);
        return ruleRulesEngineFactory.newEngineInstance();

    }

    @Singleton
    @Provides
    @Named("baggageSourceAndConfigRuleEngine")
    public RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> getBaggageSourceAndConfigRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> wrapper = new RuleEngineWrapper<>(BaggageSourceAndConfigRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/baggageSourceAndConfigRules-prod_c4d565ca859c6d8ccd9f43f290852720.xlsx"));
        themisClientService.registerResource("ct.sos.rule.baggage.rules", new RuleEngineResourceObserver(wrapper));
        return wrapper;

    }

    @Singleton
    @Provides
    @Named("productClassCombinabilityRuleEngine")
    public RuleEngineWrapper<ProductClassCombinabilityRuleEngine> getProductClassCombinabilityRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<ProductClassCombinabilityRuleEngine> wrapper = new RuleEngineWrapper<>(ProductClassCombinabilityRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/productClassCombinabilityRules-prod_3f02f9933e6c5e2aa40475d98a006a4d.xlsx"));
        themisClientService.registerResource("ct.sos.rule.product.class.combinability.rules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Singleton
    @Provides
    @Named("cloner")
    public Cloner getCloner() {
        Gson gson = new Gson();
        return new GsonObjectCloner(gson);
    }

    @Singleton
    @Provides
    @Named("ss1ExecutorService")
    ExecutorService getFFAsyncPersistenceExecutorService(SearchConfigContainer properties) {
        final int corePoolSize = properties.getIntPropertyValue("ct.air.sis.ss1.executor.service.core.thread.pool.size", 250);
        final int maxPoolSize = properties.getIntPropertyValue("ct.air.sis.ss1.executor.service.max.thread.pool.size", 500);
        final long keepAliveTime = properties.getIntPropertyValue("ct.air.sis.ss1.executor.service.keep.alive.time", 10);
        final int queueSize = properties.getIntPropertyValue("ct.air.sis.ss1.executor.service.queue.size", 5);
        return new ThreadPoolExecutor(corePoolSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, new ArrayBlockingQueue<>(queueSize));
    }

    @Named(AIRLINE_SPECIFIC_CONFIG_RULE_ENGINE)
    @Singleton
    @Provides
    public RuleEngineWrapper<AirlineSpecificConfigRuleEngine> getAirlineSpecificConfigRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<AirlineSpecificConfigRuleEngine> wrapper = new RuleEngineWrapper<>(AirlineSpecificConfigRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/airlineSpecificConfigRules-prod_3062dea0ab3196202542f261052bf532.xlsx"));
        themisClientService.registerResource("ct.sos.rule.airlineSpecificConfigRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Named(AIRLINE_SPECIFIC_CONFIG_RESOURCE)
    @Singleton
    @Provides
    public AirlineSpecificConfigResource getAirlineSpecificConfigResource(
        @Named(AIRLINE_SPECIFIC_CONFIG_RULE_ENGINE) RuleEngineWrapper<AirlineSpecificConfigRuleEngine> ruleEngineWrapper) {
        return new AirlineSpecificConfigResourceImpl(ruleEngineWrapper);
    }

    @Singleton
    @Provides
    @Named("ctcLoggingRateLimiter")
    Bucket getCtcLoggingRateLimiter(SearchConfigContainer properties) {
        final long duration = properties.getLongPropertyValue("ct.air.sos.ctc.newrelic.publishing.rate.limit.in.milliseconds", 15 * 60 * 1000);
        // define the limit 1 time per 15 minute
        Bandwidth limit = Bandwidth.simple(1, Duration.ofMillis(duration));
        // construct the bucket
        return Bucket.builder().addLimit(limit).build();
    }

    @Singleton
    @Provides
    @Named("revenueLoggingRateLimiter")
    Bucket getRevenueLoggingRateLimiter(SearchConfigContainer properties) {
        final long duration = properties.getLongPropertyValue("ct.air.sos.revenue.newrelic.publishing.rate.limit.in.milliseconds", 60 * 60 * 1000);
        // define the limit 1 time per 1 hour
        Bandwidth limit = Bandwidth.simple(1, Duration.ofMillis(duration));
        // construct the bucket
        return Bucket.builder().addLimit(limit).build();
    }

    @Singleton
    @Provides
    @Named("flightPreviewWorkflowGeneratorFactory")
    Map<StrategyType, WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow>> getWorkflowFactory(@Named("optimisedResearchWorkflowGenerator") WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow> optimisedResearchWorkflowGenerator,
                                                                                                               @Named("supplierResearchWorkflowGenerator") WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow> supplierResearchWorkflowGenerator,
                                                                                                               @Named("priceItineraryWorkflowGenerator") WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow> priceItineraryWorkflowGenerator,
                                                                                                               @Named("airPriceWorkflowGenerator")WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow> airPriceWorkflowGenerator) {
        Map<StrategyType, WorkFlowGenerator<FlightPreviewDomainRequest, FlightPreviewWorkflow>> workflowFactory = new HashMap<>();
        workflowFactory.put(StrategyType.OPTIMISED_RESEARCH, optimisedResearchWorkflowGenerator);
        workflowFactory.put(StrategyType.SUPPLIER_RESEARCH, supplierResearchWorkflowGenerator);
        workflowFactory.put(StrategyType.PRICE_ITINERARY, priceItineraryWorkflowGenerator);
        workflowFactory.put(StrategyType.AIR_PRICE,airPriceWorkflowGenerator);
        return workflowFactory;
    }

    @Named("flightPreviewWorkflowStrategyEvaluatorRuleEngine")
    @Singleton
    @Provides
    public RuleEngineWrapper<FlightPreviewWorkflowStrategyEvaluatorRuleEngine> getFlightPreviewWorkflowRuleEngine(@Named("themisClientService") ThemisClientService themisClientService) throws APIExecutionFailedException, FileNotFoundException, ResourceNotFoundException {
        RuleEngineWrapper<FlightPreviewWorkflowStrategyEvaluatorRuleEngine> wrapper = new RuleEngineWrapper<>(FlightPreviewWorkflowStrategyEvaluatorRuleEngine.class);
//        wrapper.refreshResource(new File("search/src/main/resources/openl/qa/FlightPreviewStrategyRules-prod_fb03e122498f5c56aaee5611dfabc898.xlsx"));
        themisClientService.registerResource("ct.sos.rule.FlightPreviewStrategyRules", new RuleEngineResourceObserver(wrapper));
        return wrapper;
    }

    @Singleton
    @Provides
    @Named("baggageExecutorService")
    public ExecutorService getBookingExecutorService(Provider<SearchConfiguration> searchConfigurationProvider) {
        BaggageThreadPoolConfiguration bookingThreadPoolConfiguration = searchConfigurationProvider.get().getBaggageThreadPoolConfiguration();
        int corePoolSize = bookingThreadPoolConfiguration.getCorePoolSize();
        int maxPoolSize = bookingThreadPoolConfiguration.getMaxPoolSize();
        int workerQueueSize = bookingThreadPoolConfiguration.getWorkerQueueSize();
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(corePoolSize, maxPoolSize,
                0L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(workerQueueSize), r -> new Thread(r, "bookingThread"));
        executorService.prestartAllCoreThreads();
        return executorService;
    }

    @Singleton
    @Provides
    @Named("searchMonitoringHelper")
    public MonitoringHelper getMonitoringHelper(@Named("searchStatsHelper") StatsHelper statsHelper,
            ObjectMapper objectMapper) {
        return new MonitoringHelper(statsHelper, null, objectMapper);
    }

}
