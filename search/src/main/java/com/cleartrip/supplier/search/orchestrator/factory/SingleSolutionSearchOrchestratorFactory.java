package com.cleartrip.supplier.search.orchestrator.factory;

import com.cleartrip.supplier.search.config.SearchConfiguration;
import com.cleartrip.supplier.search.enums.TripType;
import com.cleartrip.supplier.search.models.SelectedSolution;
import com.cleartrip.supplier.search.models.SingleSolutionSearchRequest;
import com.cleartrip.supplier.search.models.SingleSolutionSearchWorkflowRequest;
import com.cleartrip.supplier.search.models.SingleSolutionWorkflowResponse;
import com.cleartrip.supplier.search.orchestrator.FareFamilySingleSolutionSearchOrchestrator;
import com.cleartrip.supplier.search.orchestrator.IOrchestrator;
import com.cleartrip.supplier.search.orchestrator.SingleSolutionSearchOrchestrator;
import com.cleartrip.supplier.search.util.AvailabilityUtil;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.cleartrip.supplier.web.SISConstants.ALL_IDENTIFIER;
import static com.cleartrip.supplier.web.SISConstants.UNDERSCORE;

@Slf4j
public class SingleSolutionSearchOrchestratorFactory {
    private final SingleSolutionSearchOrchestrator singleSolutionSearchOrchestrator;
    private final FareFamilySingleSolutionSearchOrchestrator fareFamilySingleSolutionSearchOrchestrator;
    private final SearchConfiguration searchConfiguration;
    @Inject
    public SingleSolutionSearchOrchestratorFactory(SingleSolutionSearchOrchestrator singleSolutionSearchOrchestrator, FareFamilySingleSolutionSearchOrchestrator fareFamilySingleSolutionSearchOrchestrator,SearchConfiguration searchConfiguration) {
        this.singleSolutionSearchOrchestrator = singleSolutionSearchOrchestrator;
        this.fareFamilySingleSolutionSearchOrchestrator = fareFamilySingleSolutionSearchOrchestrator;
        this.searchConfiguration = searchConfiguration;
    }
    public IOrchestrator<SingleSolutionSearchRequest, List<SingleSolutionWorkflowResponse>> getOrchestrator(SingleSolutionSearchRequest singleSolutionSearchRequest, Map<String, Map<String, List<String>>> fareFamilyEnabledSupplierAndSectorMap) {
        if (singleSolutionSearchRequest.getSingleSolutionSearchWorkflowRequestList().stream()
                    .allMatch(singleSolutionSearchWorkflowRequest ->
                            isTripEnabled(singleSolutionSearchWorkflowRequest,fareFamilyEnabledSupplierAndSectorMap)
                    )
            ) {
                return fareFamilySingleSolutionSearchOrchestrator;
            }
        return singleSolutionSearchOrchestrator;

    }

    private boolean isTripEnabled(SingleSolutionSearchWorkflowRequest request, Map<String, Map<String, List<String>>> supplierSectorConfig) {
        if (request == null || request.getSelectedSolutionInformation() == null) {
            log.warn("Invalid request or missing selectedSolutionInformation");
            return false;
        }
        try{
            String supplier = request.getSelectedSolutionInformation().getAirSupplier().toString();
            com.cleartrip.supplier.inventory.protos.v1.TripType tripType = request.getSelectedSolutionInformation().getTripType();
            String solutionId = request.getSelectedSolutionInformation().getSolutionId();

            String lookupKey = AvailabilityUtil.getLookupKey(tripType,request.getSelectedSolutionInformation().isIntl());
            if (lookupKey == null){
                return false;
            }
            boolean isSupplierEnabled = supplierSectorConfig.containsKey(supplier);
            if (!isSupplierEnabled) return false;

            boolean isTripTypeEnabled = supplierSectorConfig.get(supplier).containsKey(lookupKey);
            if (!isTripTypeEnabled) return false;

            List<String> enabledSectors = supplierSectorConfig.get(supplier).get(lookupKey);
            return isSectorEnabled(solutionId, enabledSectors);
        } catch (Exception e) {
            log.error("Error occurred while checking if the trip is enabled for Fare family ", e);
            return false;
        }

    }

    //This function is using comboFbc to identify the trip type i.e., Oneway or ReturnTrip.
    //For oneway if the comboFbc is Value then for return trip it will be Value,Value
    //This will also be used to enable and disable the Fare Family enabled suppliers
    private boolean isTripTypeEnabled(SelectedSolution selectedSolutionInformation,Map<String,List<String>> ffEnabledComboFbcMap) {
        try{
            String supplier = selectedSolutionInformation.getAirSupplier().toString();
            if(ffEnabledComboFbcMap.containsKey(supplier)){
                String selectedComboFbc = selectedSolutionInformation.getComboFareBasisCode().toLowerCase();
                return ffEnabledComboFbcMap.get(supplier).contains(selectedComboFbc);
            }
            else{
                log.error("{} is not enabled for fare family",supplier);
            }
        } catch (Exception e) {
            log.error("Exception occurred while deciding orchestrator in isTripTypeEnabled{}",e.getMessage());
            return false;
        }
        return false;
    }

    private boolean isSectorEnabled(String solutionId,List<String> ffEnabledSectors) {
        try{
            String from = StringUtils.split(solutionId,"\\|")[0];
            String to = StringUtils.split(solutionId,"\\|")[1];
            String oneToOne = org.apache.commons.lang3.StringUtils.join(from, UNDERSCORE, to);
            String oneToAll = org.apache.commons.lang3.StringUtils.join(from, UNDERSCORE, ALL_IDENTIFIER);
            String allToOne = org.apache.commons.lang3.StringUtils.join(ALL_IDENTIFIER, UNDERSCORE, to);
            String allToAll = org.apache.commons.lang3.StringUtils.join(ALL_IDENTIFIER, UNDERSCORE, ALL_IDENTIFIER);

            return ffEnabledSectors.stream()
                    .anyMatch(s -> s.equalsIgnoreCase(oneToOne)
                            || s.equalsIgnoreCase(oneToAll)
                            || s.equalsIgnoreCase(allToOne)
                            || s.equalsIgnoreCase(allToAll));
        }catch (Exception e){
            log.error("Exception occurred while deciding orchestrator in isSectorEnabled {}",e.getMessage());
            return false;
        }
    }


    private boolean isSectorEnabledV2(String solutionId, Map<String, List<String>> tripTypeToSectorMap,
                                      SelectedSolution selectedSolutionInformation,
                                      Map<String, List<String>> ffEnabledComboFbcMap) {
        try {
            TripType tripType = getTripType(selectedSolutionInformation, ffEnabledComboFbcMap);
            if (tripType == null) return false;
            String from = StringUtils.split(solutionId, "\\|")[0];
            String to = StringUtils.split(solutionId, "\\|")[1];

            String oneToOne = org.apache.commons.lang3.StringUtils.join(from, UNDERSCORE, to);
            String oneToAll = org.apache.commons.lang3.StringUtils.join(from, UNDERSCORE, ALL_IDENTIFIER);
            String allToOne = org.apache.commons.lang3.StringUtils.join(ALL_IDENTIFIER, UNDERSCORE, to);
            String allToAll = org.apache.commons.lang3.StringUtils.join(ALL_IDENTIFIER, UNDERSCORE, ALL_IDENTIFIER);

            List<String> ffEnabledSectors = tripTypeToSectorMap.getOrDefault(tripType.name(), Collections.emptyList());
            return ffEnabledSectors.stream()
                    .anyMatch(s -> s.equalsIgnoreCase(oneToOne)
                            || s.equalsIgnoreCase(oneToAll)
                            || s.equalsIgnoreCase(allToOne)
                            || s.equalsIgnoreCase(allToAll));
        } catch (Exception e) {
            log.error("Exception occurred while deciding orchestrator in isSectorEnabledV2 {}", e.getMessage());
            return false;
        }
    }

    //Helper method to determine trip type based on selected solution information
    private TripType getTripType(SelectedSolution selectedSolutionInformation, Map<String, List<String>> ffEnabledComboFbcMap) {
        try {
            String supplier = selectedSolutionInformation.getAirSupplier().toString();
            if (ffEnabledComboFbcMap.containsKey(supplier)) {
                String selectedComboFbc = selectedSolutionInformation.getComboFareBasisCode().toLowerCase();
                if (ffEnabledComboFbcMap.get(supplier).contains(selectedComboFbc)) {
                    return TripType.ONEWAY;
                } else {
                    return TripType.ROUNDTRIP;
                }
            } else {
                log.error("{} is not enabled for fare family", supplier);
            }
        } catch (Exception e) {
            log.error("Exception occurred while deciding orchestrator in isTripTypeEnabled{}", e.getMessage());
            return null;
        }
        return null;
    }
}
