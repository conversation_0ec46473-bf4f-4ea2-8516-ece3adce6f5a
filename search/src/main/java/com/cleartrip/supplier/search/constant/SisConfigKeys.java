package com.cleartrip.supplier.search.constant;

public class SisConfigKeys {
    public static final String SUPPLIER_CONFIG_KEY_PREFIX = "ct.air.sis.supplier";
    public static final String SUPPLIER_CATEGORY_CONFIG_KEY = SUPPLIER_CONFIG_KEY_PREFIX + ".category.json";
    public static final String CREDENTIAL_RESOURCE_REFRESH_TIME_KEY = "ct.sis.services.credential.refreshTimeInMillis";
    public static final String CREDENTIAL_RULE_RESOURCE_REFRESH_TIME_KEY = "ct.sis.services.credentialRules.refreshTimeInMillis";

    /*SMS Connector Adaptors Keys*/
    public static final String CT_AIR_KM_DISTANCE_RADIAL_SEARCH_FOR_ZERO_RESULTS = "ct.air.km.distance.radial.search.for.zero.results";
    public static final String CT_AIR_AMADEUS_INCLUDE_AIRLINES_SEARCH = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.include.airlines.search";
    public static final String CT_AIR_AMADEUS_INCLUDE_AIRLINES_CARRIERS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.include.airlines.carriers";
    public static final String CT_AIR_AMADEUS_DOMESTIC_EXCLUDED_CARRIERS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.domestic.excluded.carriers";
    public static final String CT_AIR_AMADEUS_INTERNATIONAL_EXCLUDED_CARRIERS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.international.excluded.carriers";
    public static final String CT_AIR_AMADUES_INTL_EXCLUDE_TICKETLESS_CARRIERS_NEWFLOW = SUPPLIER_CONFIG_KEY_PREFIX + ".amadues.intl.exclude.ticketless.carriers.newflow";
    public static final String CT_AIR_AMADUES_INTL_EXCLUDE_TICKETLESS_CARRIERS_V_2 = SUPPLIER_CONFIG_KEY_PREFIX + ".amadues.intl.exclude.ticketless.carriers.v2";
    public static final String CT_AIR_AMADEUS_SECTORPREFERENCE_ENABLE = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.sectorpreference.enable";
    public static final String CT_AIR_AMADEUS_INCLUDE_SECTORS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.include.sectors";
    public static final String CT_AIR_AMADEUS_AIRLINE_PREFERRED = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.airline.preferred";
    public static final String AMADEUS_DOMESTIC_MAX_NUM_FLTS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.domestic.maxnumflts";
    public static final String AMADEUS_INTL_MAX_NUM_FLTS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.intl.maxnumflts";
    public static final String AMADEUS_NEAR_BY_MAX_NUM_FLTS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.intl.near.by.maxnumflts";
    public static final String AMADEUS_DOMESTIC_SPECIAL_RT_MAX_NUM_FLTS = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.domestic.special.rt.maxnumflts";
    public static final String AMADEUS_DEFAULT_MAX_FLIGHT_COUNT = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.default.maxnumflts";
    public static final String FARE_DISPLAY_NAME_PROP = "ct.air.sis.fareGroup.display.name.map";
    public static final String DEFAULT_DISPLAY_NAME_PROP = "ct.air.sis.fareGroup.display.name.default.value";
    public static final String INCLUDE_ONE_STOP_FLT = SUPPLIER_CONFIG_KEY_PREFIX + ".amadeus.include.onestop.flights";
    public static final String ALL_CLASS_SEARCH_SUFFIX = ".search.allclass";
    public static final String GAL_DOM_PREFERRED_AIRLINES = SUPPLIER_CONFIG_KEY_PREFIX + ".galileo.domestic.enabled.airlines";
    public static final String GAL_INTL_DISABLE_PREFERRED_AIRLINES = SUPPLIER_CONFIG_KEY_PREFIX + ".galileo.intl.preferred.airlines.to.be.disabled";
    public static final String GAL_EXCLUDE_AIRLINES = SUPPLIER_CONFIG_KEY_PREFIX + ".galileo.excluded.airlines";
    public static final String GAL_DOM_EXCLUDE_AIRLINES = SUPPLIER_CONFIG_KEY_PREFIX + ".dom.galileo.excluded.airlines";
    public static final String GAL_INTL_EXCLUDE_AIRLINES = SUPPLIER_CONFIG_KEY_PREFIX + ".intl.galileo.excluded.airlines";

    public static final String SMS_THREAD_COUNT_SUFFIX = ".sms.thread.count";
    public static final String SMS_SUPPLIER_FARE_CODES = SUPPLIER_CONFIG_KEY_PREFIX + ".fare.codes";
    public static final String SMS_SUPPLIER_PROMO_CODES = SUPPLIER_CONFIG_KEY_PREFIX + ".promo.code.channels";
    @Deprecated
    public static final String CHILD_SUPPLIERS_CONFIG_KEY = "ct.air.sis.child.suppliers.json";
    //Task creation related keys
    public static final String CARRIER_TO_SUPPLIER_CONFIG_KEY = "ct.air.sis.dom.carrier.supplier.config";
    public static final String GDS_CARRIERS_FOR_STOP_ROUTES_KEY = "ct.air.sis.stop.routes.gds.carriers";
    public static final String NUMBER_OF_FLIGHT_COUNTS_ENABLED = "ct.air.sis.dom.number.of.flight.counts.enabled";
    public static final String CT_AIR_IMS_BAGGAGE_JSON_INFO = "ct.air.baggage.info.json";
    public static final String CT_AIR_IMS_BAGGAGE_SOURCE_JSON = "ct.air.baggage.source.json";
    // TODO supplier baggage source
    public static final String CT_AIR_SIS_BAGGAGE_SUPPLIER_PREFIX = "ct.air.sis.supplier.baggage.";
    public static final String SOURCE_SUFFIX = ".source";
    public static final String CREDENTIAL_CHANNEL_TYPE_MAPPING = "ct.air.sis.credential.channel.type.mapping";
    public static final String AIR_SIS_HYSTRIX_PREFIX = SUPPLIER_CONFIG_KEY_PREFIX + ".hystrix.";
    public static final String AIR_SIS_HYSTRIX_ERROR_THRESHOLD_PERCENTAGE = ".errorThresholdPercentage";
    public static final String AIR_SIS_HYSTRIX_SLEEP_WINDOW_IN_MILLISECONDS = ".sleepWindowInMilliseconds";
    public static final String AIR_SIS_HYSTRIX_REQUEST_VOLUME_THRESHOLD = ".requestVolumeThreshold";
    public static final String AIR_SIS_HYSTRIX_SEMAPHORE_COUNT = ".semaphoreCount";
    public static final String AIR_SIS_HYSTRIX_EXECUTION_TIMEOUT_ENABLED = ".executionTimeoutEnabled";
    public static final String AIR_SIS_HYSTRIX_CIRCUIT_BREAKER_ENABLED = ".circuitBreakerEnabled";
    public static final String AIR_SIS_HYSTRIX_EXECUTION_TIMEOUT_MILLIS = ".executionTimeoutMillis";
    public static final String CT_AIR_SIS_SUPPLIER_ENABLED_FARE_GROUPS = SUPPLIER_CONFIG_KEY_PREFIX + ".enabled.fare.groups";
    public static final String CT_AIR_SIS_SMS_CLIENT_SUPPLIER_HOST_PORT = "ct.air.sis.sms.client.supplier.host.port";
    public static final String CT_AIR_SIS_SMS_CLIENT_HOST = "ct.air.sis.sms.client.host";
    public static final String CT_AIR_SIS_SMS_CLIENT_PORT = "ct.air.sis.sms.client.port";
    public static final String CARRIER_TO_SUPPLIER_MAPPING_KEY = "ct.air.sis.carrier.supplier.mapping.configV2";
    public static final String SFF_FARE_CATEGORY_PROPERTY = "ct.air.sis.sff.fare.category";
    public static final String SFF_FARE_DEDUPLICATION_ENABLED = "ct.air.sis.sff.dedup.enabled";
    public static final String SUPPLIER_TO_FARE_GROUP_INFO_MAPPING = "ct.air.sis.dom.supplier.faregroupinfo.mapping.configV3";
    public static final String SUPPLIER_TO_FARE_GROUP_INFO_MAPPING_V2 = "ct.air.sis.dom.supplier.faregroupinfo.mapping.configV2";
    public static final String SEARCH_ENABLED_SECTOR_MAPPING = "ct.air.sis.carrier.sector.mapping.config";
    public static final String NEW_SEARCH_ENABLED_SUPPLIERS = "ct.air.sis.new.search.dom.carrier.supplier.configV2";
    public static final String FARE_BREAKUP_ENABLED_SUPPLIERS = "ct.air.sis.fare.breakup.enabled.supplier";
    public static final String GDS_EXCLUDED_SECTORS_KEY = "ct.air.sis.gds.excluded.sectors";

    public static final String CORP_SEARCH_ENABLED_SUPPLIERS = "ct.air.sis.new.search.dom.corp.carrier.supplier.config";
    public static final String TTL_POLICY_MAPPING_KEY = "ct.air.sis.ttl.mapping.config";
    public static final String OND_ENABLED_SUPPLIERS_KEY = "ct.air.sis.ond.enabled.supplier.config";
    public static final String OND_FALLBACK_ENABLED_KEY = "ct.air.sis.ond.fallback.enabled";
    public static final String INTL_ROUTING_CARRIERS_KEY = "ct.air.sis.intl.routing.carriers";
    public static final String AMEND_CREDENTIAL_KEY_MAPPING = "ct.air.sis.amend.credential.mapping";
    public static final String INFANT_SUPPLIER_SEARCH_MAPPING = "ct.air.sis.infant.supplier.search.mapping";
    public static final String IS_ROUTING_MERGED_KEY = "ct.air.sis.ond.routing.config.merge.status";
}
