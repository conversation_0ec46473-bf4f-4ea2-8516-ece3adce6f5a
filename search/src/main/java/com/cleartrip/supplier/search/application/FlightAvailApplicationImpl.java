package com.cleartrip.supplier.search.application;

import com.cleartrip.monitoring.dtos.ApiStatsDto;
import com.cleartrip.monitoring.models.Context;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.monitoring.StatsUtil;
import com.cleartrip.supplier.newrelic.NewRelicUtil;
import com.cleartrip.supplier.search.adapters.FlightSolutionAvailResponseAdapter;
import com.cleartrip.supplier.search.adapters.SingleSolutionSearchWorkflowRequestAdapter;
import com.cleartrip.supplier.search.convertor.Convertor;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.SingleSolutionSearchRequest;
import com.cleartrip.supplier.search.models.SingleSolutionSearchWorkflowRequest;
import com.cleartrip.supplier.search.models.SingleSolutionWorkflowResponse;
import com.cleartrip.supplier.search.models.application.SelectedSearchRequest;
import com.cleartrip.supplier.search.models.application.SelectedSearchResponse;
import com.cleartrip.supplier.search.orchestrator.IOrchestrator;
import com.cleartrip.supplier.search.orchestrator.factory.SelectedSearchOrchestratorFactory;
import com.cleartrip.supplier.search.orchestrator.factory.SingleSolutionSearchOrchestratorFactory;
import com.cleartrip.supplier.search.services.cachingService.keyGenerator.AvailabilityCacheKeyGeneratorV2;
import com.cleartrip.supplier.search.services.cachingService.repositry.AvailSearchReqCachingRepository;
import com.cleartrip.supplier.search.util.AvailabilityUtil;
import com.cleartrip.supplier.stats.StatsPublisher;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
public class FlightAvailApplicationImpl implements FlightAvailApplication {

    private static final String EXCEPTION = "EXCEPTION: ";
    private static final String SINGLE_SOLUTION_TAG = "ss1_getFlightSolutionV2";
    private static final String SINGLE_SOLUTION_SEARCH = "SUPPLY_CORE";
    private static final String SOS_SINGLE_SOLUTION_SEARCH = "SINGLE_SOLUTION_SEARCH";
    private final Convertor<List<FlightSolutionDTO>, ResponseMeta> responseMetaConvertor;
    private final SelectedSearchOrchestratorFactory selectedSearchOrchestratorFactory;
    private final SingleSolutionSearchOrchestratorFactory singleSolutionSearchOrchestratorFactory;
    private final StatsUtil statsUtil;
    private final StatsPublisher statsPublisher;
    private final SingleSolutionSearchWorkflowRequestAdapter singleSolutionSearchWorkflowRequestAdapter;
    private final FlightSolutionAvailResponseAdapter flightSolutionAvailResponseAdapter;
    private final AvailabilityCacheKeyGeneratorV2 availabilityCacheKeyGenerator;
    private final AvailSearchReqCachingRepository searchReqCachingRepository;
    private final SearchConfigContainer searchConfigContainer;
    private final ObjectMapper objectMapper;
    Map<String, Map<String, List<String>>> fareFamilyEnabledSupplierAndSectorMap;


    @Inject
    public FlightAvailApplicationImpl(@Named("ResponseMetaConvertor") Convertor<List<FlightSolutionDTO>, ResponseMeta> responseMetaConvertor,
                                      SelectedSearchOrchestratorFactory selectedSearchOrchestratorFactory,
                                      SingleSolutionSearchOrchestratorFactory singleSolutionSearchOrchestratorFactory,
                                      final StatsUtil statsUtil,
                                      final StatsPublisher statsPublisher,
                                      SingleSolutionSearchWorkflowRequestAdapter singleSolutionSearchWorkflowRequestAdapter,
                                      FlightSolutionAvailResponseAdapter flightSolutionAvailResponseAdapter,
                                      AvailabilityCacheKeyGeneratorV2 availabilityCacheKeyGenerator,
                                      @Named("MongoAvailSearchReqCachingRepositoryImpl") AvailSearchReqCachingRepository searchReqCachingRepository, SearchConfigContainer searchConfigContainer, ObjectMapper objectMapper) {
        this.responseMetaConvertor = responseMetaConvertor;
        this.selectedSearchOrchestratorFactory = selectedSearchOrchestratorFactory;
        this.singleSolutionSearchOrchestratorFactory = singleSolutionSearchOrchestratorFactory;
        this.statsUtil = statsUtil;
        this.statsPublisher = statsPublisher;
        this.singleSolutionSearchWorkflowRequestAdapter = singleSolutionSearchWorkflowRequestAdapter;
        this.flightSolutionAvailResponseAdapter = flightSolutionAvailResponseAdapter;
        this.availabilityCacheKeyGenerator = availabilityCacheKeyGenerator;
        this.searchReqCachingRepository = searchReqCachingRepository;
        this.searchConfigContainer = searchConfigContainer;
        this.objectMapper = objectMapper;
        this.fareFamilyEnabledSupplierAndSectorMap = AvailabilityUtil.getFareFamilyEnabledSupplierAndSectorMap(searchConfigContainer);
    }

    private Map<String, List<String>> getFareFamilyComboFbcMap() {
            try {
                String fareNameMapString = this.searchConfigContainer.getPropertyValue("ct.air.sis.fare.family.enabled.comboFbc", "{}");
                return objectMapper.readValue(fareNameMapString, new TypeReference<Map<String, List<String>>>() {
                });
            } catch (Exception e) {
                log.error("Unable to process fare family enabled comboFbcs due to {}",e.getMessage());
            }
            return Collections.emptyMap();
    }

    private List<String> getFareFamilyEnabledSectors(){
        try {
            String enabledSectorsString = this.searchConfigContainer.getPropertyValue("ct.air.sis.fare.family.enabled.sectors", "[]");
            return objectMapper.readValue(enabledSectorsString, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            log.error("Unable to process fare family enabled sectors due to {}",e.getMessage());
        }
        return Collections.emptyList();
    }

    @Override
    public SelectedSearchResponse getSelectedSearch(SelectedSearchRequest selectedSearchRequest) {
        long startTime = System.currentTimeMillis();
        try {
            IOrchestrator<SelectedSearchRequest, List<FlightSolutionDTO>> selectedSearchOrchestrator = selectedSearchOrchestratorFactory.getOrchestrator(selectedSearchRequest);
            List<FlightSolutionDTO> flightSolutionDTOS = selectedSearchOrchestrator.orchestrate(selectedSearchRequest);
            SelectedSearchResponse selectedSearchResponse = SelectedSearchResponse.builder()
                    .solutionListV2(flightSolutionDTOS)
                    .metaData(Collections.emptyMap())
                    .dataBag(com.cleartrip.supplier.search.models.application.SupplierDataBag.builder().build())
                    .build();
            NewRelicUtil.sendSelectedSearchEvent(selectedSearchRequest, startTime, System.currentTimeMillis(), Optional.empty());
            return selectedSearchResponse;
        } catch (Exception e) {
            NewRelicUtil.sendSelectedSearchEvent(selectedSearchRequest, startTime, System.currentTimeMillis(), Optional.ofNullable(e.getMessage()));
            throw e;
        }
    }

    @Override
    public GetFlightSolutionsResponse getFlightSolutionV2(GetFlightSolutionRequestV2 getFlightSolutionsRequest) {
        long startTime = System.currentTimeMillis();
        Context statsContext = Context.builder().itineraryId(getFlightSolutionsRequest.getItineraryId()).build();
        try {
            SingleSolutionSearchRequest singleSolutionSearchRequest = convertSS1Request(getFlightSolutionsRequest);
            List<FlightSolutionDTO> solutionDTOS = getFlightSolutionDTOS(singleSolutionSearchRequest);
            List<FlightSolution> flightSolutionProto = solutionDTOS.stream().map(flightSolutionAvailResponseAdapter::convertFlightSolution).collect(Collectors.toList());
            GetFlightSolutionsResponse getFlightSolutionsResponse = GetFlightSolutionsResponse.newBuilder()
                    .addAllFlightSolutions(flightSolutionProto)
                    .setResponseMeta(responseMetaConvertor.convert(solutionDTOS))
                    .build();

            statsPublisher.pushToStats(startTime, System.currentTimeMillis(), statsUtil.getObjectInByte(solutionDTOS), 200,
                    SINGLE_SOLUTION_TAG, statsContext, statsUtil.getObjectInByte(singleSolutionSearchRequest), SOS_SINGLE_SOLUTION_SEARCH, SINGLE_SOLUTION_SEARCH, ApiStatsDto.HttpMethod.POST);
            NewRelicUtil.sendSingleSearchEvent(getFlightSolutionsRequest, startTime, System.currentTimeMillis(), Optional.empty());
            return getFlightSolutionsResponse;
        } catch (Exception e) {
            NewRelicUtil.sendSingleSearchEvent(getFlightSolutionsRequest, startTime, System.currentTimeMillis(), Optional.ofNullable(getErrorMsg(e)));
            statsPublisher.pushToStats(startTime, System.currentTimeMillis(), statsUtil.getObjectInByte(EXCEPTION + e.getMessage()), 500,
                    SINGLE_SOLUTION_TAG, statsContext, statsUtil.getObjectInByte(getFlightSolutionsRequest.toString()), SOS_SINGLE_SOLUTION_SEARCH, SINGLE_SOLUTION_SEARCH, ApiStatsDto.HttpMethod.POST);
            throw e;
        }
    }

    @NotNull
    @Override
    public List<FlightSolutionDTO> getFlightSolutionDTOS(SingleSolutionSearchRequest singleSolutionSearchRequest) {
        IOrchestrator<SingleSolutionSearchRequest, List<SingleSolutionWorkflowResponse>> singleSolutionOrchestrator = singleSolutionSearchOrchestratorFactory.getOrchestrator(singleSolutionSearchRequest,fareFamilyEnabledSupplierAndSectorMap);
        List<SingleSolutionWorkflowResponse> singleSolutionWorkflowResponses = singleSolutionOrchestrator.orchestrate(singleSolutionSearchRequest);
        return singleSolutionWorkflowResponses.stream().map(SingleSolutionWorkflowResponse::getFlightSolutionDTO).collect(Collectors.toList());
    }

    private SingleSolutionSearchRequest convertSS1Request(GetFlightSolutionRequestV2 getFlightSolutionsRequest) {
        List<SingleSolutionSearchWorkflowRequest> singleSolutionSearchWorkflowRequestList = new ArrayList<>();
        getFlightSolutionsRequest.getSelectedSolutionInformationList().forEach(selectedSolutionInformation -> singleSolutionSearchWorkflowRequestList
                .add(singleSolutionSearchWorkflowRequestAdapter.adapt(selectedSolutionInformation, getFlightSolutionsRequest.getItineraryId(), "ss1")));
        return SingleSolutionSearchRequest.builder()
                .singleSolutionSearchWorkflowRequestList(singleSolutionSearchWorkflowRequestList)
                .build();
    }

    @Override
    public SearchRequest retrieveSearchRequest(String solutionId) {
        Pair<String, String> parentChildKey = availabilityCacheKeyGenerator.getSearchCriteriaFlightKey(solutionId);
        return searchReqCachingRepository.getSearchRequest(parentChildKey.getKey());
    }

    public String getErrorMsg(Exception error) {
        return error.getClass().getName() + ":" + error.getMessage() + ":" + Arrays.stream(error.getStackTrace())
                .limit(3)
                .map(StackTraceElement::toString)
                .collect(Collectors.joining("\n"));
    }

}
