package com.cleartrip.supplier.search.service.baggage;

import com.air.sis.rule.BaggageSourceAndConfigRuleEngine;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.v1.*;
import com.cleartrip.supplier.newrelic.NewRelicUtil;
import com.cleartrip.supplier.search.enums.BaggageSource;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.ruleEngine.RuleEngineWrapper;
import com.cleartrip.supplier.search.service.SourceBasedBaggageService;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.openl.generated.beans.BaggageInfoDto;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Log4j2
public class ConfigBaggageServiceImpl implements SourceBasedBaggageService {

    public static final String DEFAULT = "DEFAULT";
    public static final String DOMESTIC = "DOM";
    public static final String INTERNATIONAL = "INTL";
    private final SearchConfigContainer properties;

    private final Map<BaggageType, String> baggageTypeMap = Map.of(BaggageType.BAGGAGE_CABIN, "cab", BaggageType.BAGGAGE_CHECK_IN, "cib");

    private final Map<PaxTypeOuterClass.PaxType, String> paxTypeMap = Map.of(PaxTypeOuterClass.PaxType.ADULT, "ADT",
            PaxTypeOuterClass.PaxType.CHILD, "CHD",
            PaxTypeOuterClass.PaxType.INFANT, "INF");

    private final RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> baggageRuleEngine;

    @Inject
    public ConfigBaggageServiceImpl(SearchConfigContainer properties,
                                    @Named("baggageSourceAndConfigRuleEngine") RuleEngineWrapper<BaggageSourceAndConfigRuleEngine> baggageRuleEngine) {
        this.properties = properties;
        this.baggageRuleEngine = baggageRuleEngine;

    }

    @Override
    public List<SolutionBaggageResponse> getBaggage(String requestId, BaggageCriteria criteria, List<SolutionInfo> solutionInfoList) {
        log.debug("get baggage for baggage criteria {} solution count {}", criteria, solutionInfoList.size());
        return solutionInfoList.stream().map(solutionInfo -> {
            long getStart = System.currentTimeMillis();
            try {
                SolutionBaggageResponse solutionBaggageResponse = getFreeBaggage(criteria, solutionInfo);
                NewRelicUtil.sendBaggageEvent(requestId, BaggageSource.CONFIG, criteria,
                        solutionInfo, "", "", solutionBaggageResponse, System.currentTimeMillis() - getStart, null, getStart);

                return solutionBaggageResponse;
            } catch (Exception e) {
                NewRelicUtil.sendBaggageEvent(requestId, BaggageSource.CONFIG, criteria,
                        solutionInfo, "", "", null, System.currentTimeMillis() - getStart, e.getMessage(), getStart);
            }
            return SolutionBaggageResponse.getDefaultInstance();
        }).collect(Collectors.toList());
    }

    private SolutionBaggageResponse getFreeBaggage(BaggageCriteria criteria,
                                                   SolutionInfo solutionInfo) {
        log.debug("getFreeBaggage BaggageCriteria {} SolutionInfo {}", criteria, solutionInfo);
        SolutionBaggageResponse.Builder builder = SolutionBaggageResponse.newBuilder();

        solutionInfo.getFlightsList().forEach(flightsInfo ->
                builder.addFltBaggageDetails(prepareFlightBaggage(criteria, solutionInfo, flightsInfo)));

        return builder.build();
    }

    private FlightBaggage prepareFlightBaggage(BaggageCriteria criteria,
                                               SolutionInfo solutionInfo, FlightsInfo flightsInfo) {
        FlightBaggage.Builder builder = FlightBaggage.newBuilder();
        List<FlightSegment> segmentList = flightsInfo.getSegmentsList();
        segmentList.forEach(segment -> builder.addBaggageInfo(prepareSegmentBaggage(criteria, solutionInfo, segment)));

        String departureAirport = segmentList.get(0).getDepartFromStop().getAirport();
        String arrivalAirport = segmentList.get(segmentList.size() - 1).getArriveAtStop().getAirport();
        return builder
                .setOriginDestinationInfo(OriginDestinationInfoOuterClass.OriginDestinationInfo.newBuilder()
                        .setFrom(departureAirport)
                        .setTo(arrivalAirport)
                        .build())
                .build();
    }

    private SegmentBaggage prepareSegmentBaggage(BaggageCriteria criteria,
                                                 SolutionInfo solutionInfo,
                                                 FlightSegment segment) {

        SegmentBaggage.Builder builder = SegmentBaggage.newBuilder();
        segment.getSegmentFaresCodesList().forEach(segmentFareCodes -> builder.addSegFareFamilyBaggage(
                prepareSegmentFareFamilyBaggage(criteria, solutionInfo, segment, segmentFareCodes)));
        return builder
                .setArriveAtStop(segment.getArriveAtStop())
                .setDepartFromStop(segment.getDepartFromStop())
                .build();
    }

    private SegmentFareFamilyBaggage prepareSegmentFareFamilyBaggage(BaggageCriteria criteria, SolutionInfo solutionInfo, FlightSegment segment, SegmentFareCodes segmentFareCodes) {
        SegmentFareFamilyBaggage.Builder builder = SegmentFareFamilyBaggage.newBuilder()
                .setSegmentFareCodes(segmentFareCodes);
        criteria.getPaxTypeList()
                .forEach(paxType -> {
                    PaxBaggage paxBaggage = getPaxBaggage(
                            solutionInfo.getSupplierInfo().getSupplier(),
                            segment,
                            paxType,
                            criteria,
                            solutionInfo.getFareGroupInfo().name(),
                            segmentFareCodes);
                    builder.addPaxBaggage(paxBaggage);
                });
        return builder.build();
    }

    private PaxBaggage getPaxBaggage(String supplier, FlightSegment segment, PaxTypeOuterClass.PaxType paxType, BaggageCriteria criteria, String fareGroupInfo, SegmentFareCodes segmentFareCodes) {

        BaggageInfoDto paxBaggageInfo = getPaxBaggageInfo(supplier, segment, paxType, criteria, fareGroupInfo, segmentFareCodes);
        if (Objects.isNull(paxBaggageInfo)) {
            return PaxBaggage.newBuilder()
                    .setPaxType(paxType).build();
        }
        return PaxBaggage.newBuilder().setFreeText(Objects.isNull(paxBaggageInfo.getFreetext()) ? "" : paxBaggageInfo.getFreetext())
                .addBaggage(getBaggage(paxBaggageInfo, BaggageType.BAGGAGE_CHECK_IN))
                .addBaggage(getBaggage(paxBaggageInfo, BaggageType.BAGGAGE_CABIN))
                .setPaxType(paxType).build();
    }

    private BaggageInfoDto getPaxBaggageInfo(String supplier, FlightSegment segment, PaxTypeOuterClass.PaxType paxType, BaggageCriteria criteria, String fareGroupInfo, SegmentFareCodes segmentFareCodes) {
        return baggageRuleEngine.getRuleEngine()
                .getBaggageConfig(criteria.getIsInternational() ? INTERNATIONAL : DOMESTIC,
                        supplier.toUpperCase(),
                        fareGroupInfo,
                        segment.getDepartFromStop().getAirport(),
                        segment.getArriveAtStop().getAirport(),
                        getDx(segment.getDepartFromStop().getTime()),
                        getProductClass(supplier, segmentFareCodes),
                        criteria.getCabinType().toString(),
                        segment.getFlightDetails().getAirline(),
                        paxTypeMap.get(paxType));
    }

    private String getProductClass(String supplier, SegmentFareCodes segmentFareCodes) {
        List<Supplier> gdsSuppliers = getSupplierList();
        if (gdsSuppliers.contains(Supplier.valueOf(supplier))) {
            return segmentFareCodes.getBookingClass();
        }
        return segmentFareCodes.getProductClass();
    }

    @NotNull
    private List<Supplier> getSupplierList() {
        String suppliers = properties.getPropertyValue("ct.air.sc.baggage.config.gds.product.class", "GALILEO,AMADEUS,GALILEO_LFS_INTERNATIONAL,AIE,INDIGO");
        return Arrays.stream(suppliers.split(",")).map(Supplier::valueOf).collect(Collectors.toList());
    }

    private int getDx(long time) {
        return (int) ChronoUnit.DAYS.between(Instant.ofEpochMilli(System.currentTimeMillis()), Instant.ofEpochMilli(time));
    }

    private Baggage getBaggage(BaggageInfoDto paxBaggageInfo, BaggageType baggageType) {
        if (!this.baggageTypeMap.containsKey(baggageType)) {
            return Baggage.getDefaultInstance();
        }

        String baggageInfoString = baggageType.equals(BaggageType.BAGGAGE_CHECK_IN) ? paxBaggageInfo.getCib() : paxBaggageInfo.getCab();
        String baggageInfoUnit = baggageType.equals(BaggageType.BAGGAGE_CHECK_IN) ? paxBaggageInfo.getCibUnit() : paxBaggageInfo.getCabUnit();
        if (StringUtils.isEmpty(baggageInfoString)) {
            return Baggage.getDefaultInstance();
        }
        BaggageAllowance baggageAllowance = BaggageAllowance.newBuilder()
                .setQty(Float.parseFloat(baggageInfoString))
                .setUnit(BaggageUnit.valueOf(baggageInfoUnit))
                .build();

        return Baggage.newBuilder().addBaggageAllowances(baggageAllowance).setBaggageType(baggageType).build();
    }
}
