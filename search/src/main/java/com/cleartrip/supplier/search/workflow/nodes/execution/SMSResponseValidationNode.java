package com.cleartrip.supplier.search.workflow.nodes.execution;

import com.cleartrip.supplier.search.workflow.contexts.FlightSolutionPersistenceContext;
import com.cleartrip.supplier.search.workflow.tasks.execution.SMSResponseValidationTask;
import com.cleartrip.utility.workflow.impl.SequentialConditionNodeImpl;

import java.util.List;

/*
* TRUE -- validation succeeded
* FALSE -- validation failed
* */
public class SMSResponseValidationNode extends SequentialConditionNodeImpl<List<String>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FlightSolutionPersistenceContext, SMSResponseValidationTask> {

    private final SMSResponseValidationTask task;

    public SMSResponseValidationNode(SMSResponseValidationTask task) {
        this.task = task;
    }

    @Override
    public Boolean evaluateDecisionCase(FlightSolutionPersistenceContext flightSolutionPersistenceContext) {
        return task.run(flightSolutionPersistenceContext.getSmsResponseErrors());
    }
}
