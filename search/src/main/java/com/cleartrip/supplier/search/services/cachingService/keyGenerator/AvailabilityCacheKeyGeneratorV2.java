package com.cleartrip.supplier.search.services.cachingService.keyGenerator;

import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import com.cleartrip.supplier.search.util.AvailabilityUtil;
import com.google.inject.Inject;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class AvailabilityCacheKeyGeneratorV2 implements CacheKeyGeneratorV2<WorkFlowTaskAvailSearchRequest, FlightSolutionDTO> {
    private final SearchConfigContainer properties;

    @Inject
    public AvailabilityCacheKeyGeneratorV2(SearchConfigContainer searchConfigContainer) {
        this.properties = searchConfigContainer;
    }

    @Override
    public String getSearchCriteriaKey(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        Map<String, List<String>> newCacheKeyFlowDomainToSectorMap = AvailabilityUtil.getNewCacheKeyFlowDomainToSectorMap(properties);
        return new SolutionIdFieldsV2(workFlowTaskAvailSearchRequest, getAttrDelim(),newCacheKeyFlowDomainToSectorMap).toString();
    }

    @Override
    public String getFlightKey(FlightSolutionDTO flightSolutionDTO) {
        return flightSolutionDTO.getFlightDTO().stream().map(flight ->
                        flight.getFlightSegmentDTOList().stream().map(flightSegment ->
                                        new StringBuilder()
                                                .append(flightSegment.getDepartFromStop().getAirport())
                                                .append(getSegmentAttrDelim())
                                                .append(flightSegment.getArrivalToStop().getAirport())
                                                .append(getSegmentAttrDelim())
                                                .append(flightSegment.getSegmentFlightDetailsDTO().getAirline())
                                                .append(getSegmentAttrDelim())
                                                .append(flightSegment.getSegmentFlightDetailsDTO().getFlightNumber())
                                                .toString())
                                .collect(Collectors.joining(getSegmentDelim())))
                .collect(Collectors.joining(getFlightDelim()));
    }

    @Override
    public String getSolutionId(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest, FlightSolutionDTO flightSolutionDTO) {
        return getSearchCriteriaKey(workFlowTaskAvailSearchRequest) + getParentChildKeyDelim() + getFlightKey(flightSolutionDTO);
    }

    @Override
    public String getKey(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        throw new UnsupportedOperationException("getKey");
    }
}
