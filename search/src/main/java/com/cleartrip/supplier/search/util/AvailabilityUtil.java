package com.cleartrip.supplier.search.util;

import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.v1.TripType;
import com.cleartrip.supplier.search.models.BaseSearchCriteriaDTO;
import com.cleartrip.supplier.search.models.OriginDestinationInfoDTO;
import com.cleartrip.supplier.search.models.idetification.WorkFlowTaskAvailSearchRequest;
import com.cleartrip.supplier.search.services.cachingService.ttl.impl.TTLRequest;
import com.cleartrip.supplier.search.services.cachingService.ttl.impl.TTLRequestV3;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class AvailabilityUtil {
    static ObjectMapper objectMapper = new ObjectMapper();

    public static boolean isRoundTrip(List<BaseSearchCriteriaDTO> baseCriteriaList) {
        return baseCriteriaList.size() == 2 &&
                baseCriteriaList.get(0).getOriginDestinationInfoDTO().getFrom().equals(
                        baseCriteriaList.get(1).getOriginDestinationInfoDTO().getTo()) &&

                baseCriteriaList.get(0).getOriginDestinationInfoDTO().getTo().equals(
                        baseCriteriaList.get(1).getOriginDestinationInfoDTO().getFrom()) &&

                baseCriteriaList.get(0).getDate() <=
                        baseCriteriaList.get(1).getDate();
    }
    public static Map<String, Map<String, List<String>>> getFareFamilyEnabledSupplierAndSectorMap(SearchConfigContainer searchConfigContainer) {
        try {
            String fareNameMapString = searchConfigContainer.getPropertyValue("fareFamilyEnabledSupplierAndSector", "{}");
            return objectMapper.readValue(fareNameMapString, new TypeReference<Map<String, Map<String, List<String>>>>() {});
        } catch (Exception e) {
            log.error("Unable to process fare family enabled comboFbcNested due to {}", e.getMessage());
        }
        return Collections.emptyMap();
    }
    public static String getOrigin(List<BaseSearchCriteriaDTO> baseSearchCriteria) {
        return baseSearchCriteria.stream()
                .map(BaseSearchCriteriaDTO::getOriginDestinationInfoDTO)
                .filter(Objects::nonNull)
                .map(OriginDestinationInfoDTO::getFrom)
                .findFirst()
                .orElse("");
    }

    public static String getDestination(List<BaseSearchCriteriaDTO> baseSearchCriteria) {
        return baseSearchCriteria.stream()
                .map(BaseSearchCriteriaDTO::getOriginDestinationInfoDTO)
                .filter(Objects::nonNull)
                .map(OriginDestinationInfoDTO::getTo)
                .findFirst()
                .orElse("");
    }

    public static Integer getDx(List<BaseSearchCriteriaDTO> baseSearchCriteria) {
        Instant givenInstant = Instant.ofEpochMilli(baseSearchCriteria.stream()
                .map(BaseSearchCriteriaDTO::getDate)
                .findFirst()
                .orElse(0L));
        Instant currentInstant = Instant.now();

        Duration duration = Duration.between(currentInstant, givenInstant);
        return Math.toIntExact(duration.toDays());
    }

    public static TTLRequestV3 getTtlRequestV2FromWorkFlowTaskAvailSearchRequest(WorkFlowTaskAvailSearchRequest request) {
        return TTLRequestV3.builder()
                .companyId(request.getCompanyInfoDTO().getCompanyId())
                .isIntl(request.getFlightSearchCriteriaDTO().isIntl())
                .supplier(request.getSupplierCarrierPair().getSupplier().name())
                .isRoundTrip(request.getFlightSearchCriteriaDTO().isRoundTrip())
                .origin(AvailabilityUtil.getOrigin(request.getBaseSearchCriteria()))
                .destination(AvailabilityUtil.getDestination(request.getBaseSearchCriteria()))
                .dX(getDxFromWorkFlowTaskAvailSearchRequest(request))
                .credentialKey(request.getFlightSearchCriteriaDTO().getCredential().getKey())
                .build();
    }
    public static TTLRequestV3 getTtlRequestV2FromTtlRequest(TTLRequest request, String companyId) {
        return getTtlRequestV2FromTtlRequest(request, companyId, false, "");
    }
    public static TTLRequestV3 getTtlRequestV2FromTtlRequest(TTLRequest request, String companyId, boolean isRoundtrip) {
        return getTtlRequestV2FromTtlRequest(request, companyId, isRoundtrip, "");
    }
    public static TTLRequestV3 getTtlRequestV2FromTtlRequest(TTLRequest request, String companyId, Boolean isRoundtrip, String credentialKey){
        return TTLRequestV3.builder()
                .companyId(companyId)
                .isIntl(request.isIntl())
                .supplier(request.getSupplier())
                .isRoundTrip(isRoundtrip)
                .origin(request.getFrom())
                .destination(request.getTo())
                .dX(getDxFromTtlRequest(request))
                .credentialKey(credentialKey)
                .build();
    }
    public static Integer getDxFromTtlRequest(TTLRequest ttlRequest){
        long departDate = ttlRequest.getDepartDate();
        long currentMillis = new Date().toInstant().toEpochMilli();
        long diffInMillis = departDate - currentMillis;
        return (int) TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
    }

    public static Integer getDxFromWorkFlowTaskAvailSearchRequest(WorkFlowTaskAvailSearchRequest workFlowTaskAvailSearchRequest) {
        List<BaseSearchCriteriaDTO> baseSearchCriterionDTOS = workFlowTaskAvailSearchRequest.getFlightSearchCriteriaDTO().getBaseSearchCriterionDTOS();
        long departDate = baseSearchCriterionDTOS.get(0).getDate();
        long currentMillis = new Date().toInstant().toEpochMilli();
        long diffInMillis = departDate - currentMillis;
        return (int) TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
    }

    public static Map<String,List<String>> getNewCacheKeyFlowDomainToSectorMap(SearchConfigContainer properties) {
        try{
            String newCacheKeyFlowDomains = properties.getPropertyValue("newCacheKeyFlowDomainToSector","{}");
            return objectMapper.readValue(newCacheKeyFlowDomains, new TypeReference<Map<String,List<String>>>() {
            });
        } catch (Exception e) {
            log.error("Unable to process new cache key flow domain to sector map due to {}",e.getMessage());
        }
        return Collections.emptyMap();
    }

    public static String getLookupKey(TripType tripType, boolean intl) {
        if (tripType == null) {
            return null;
        }
        switch (tripType) {
            case ONE_WAY:
                return intl ? "INTL_OW" : "DOM_OW";
            case ROUND_TRIP:
                return intl ? "INTL_RT" : "DOM_RT";
            case MULTI_CITY:
                return intl ? "INTL_MULTI" : "DOM_MULTI";
            default:
                return null;
        }
    }
}
