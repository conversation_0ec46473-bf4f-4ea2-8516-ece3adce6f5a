package com.cleartrip.supplier.search.adapters;

import com.cleartrip.supplier.inventory.protos.v1.SelectedSolutionInformation;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.models.SelectedSolution;
import com.cleartrip.supplier.search.models.SingleSolutionSearchWorkflowRequest;
import com.cleartrip.supplier.search.models.application.enums.FareCategoryType;
import com.cleartrip.supplier.search.models.application.enums.FareMatchStrategy;
import java.util.Objects;

public class SingleSolutionSearchWorkflowRequestAdapter {

    public SingleSolutionSearchWorkflowRequest adapt(SelectedSolutionInformation selectedSolutionInformation, String itineraryId, String flowName) {
        return SingleSolutionSearchWorkflowRequest.builder()
                .selectedSolutionInformation(createSelectedSearchSolutionInformation(selectedSolutionInformation))
                .itineraryId(itineraryId)
                .flowName(flowName)// this is only called from ss1 flow so
                .companyId(selectedSolutionInformation.getCompanyId())
                .fareMatchStrategy(prepareFareMatchingStrategy(selectedSolutionInformation.getFareMatchStrategy()))
                .build();
    }

    private FareMatchStrategy prepareFareMatchingStrategy(com.cleartrip.supplier.inventory.protos.FareMatchStrategy fareMatchStrategy) {
        return Objects.isNull(fareMatchStrategy) ||
            com.cleartrip.supplier.inventory.protos.FareMatchStrategy.UNRECOGNIZED.equals(fareMatchStrategy) ||
            com.cleartrip.supplier.inventory.protos.FareMatchStrategy.UNKNOWN_FARE_MATCH_STRATEGY.equals(fareMatchStrategy) ?
            FareMatchStrategy.CHEAPEST : FareMatchStrategy.valueOf(fareMatchStrategy.name());
    }

    private SelectedSolution createSelectedSearchSolutionInformation(SelectedSolutionInformation selectedSolutionInformation) {
        return SelectedSolution.builder()
                .solutionId(selectedSolutionInformation.getSolutionId())
                .intl(selectedSolutionInformation.getIntl())
                .airSupplier(Supplier.valueOf(selectedSolutionInformation.getAirSupplier()))
                .comboFareBasisCode(selectedSolutionInformation.getComboFareBasisCode())
                .isRefundable(selectedSolutionInformation.getIsRefundable())
                .fareDisplayName(selectedSolutionInformation.getFareDisplayName())
                .fareCategoryType(FareCategoryType.valueOf(selectedSolutionInformation.getFareCategoryType().name()))
                .fareGroupInfo(selectedSolutionInformation.getFareGroupInfo())
                .tripType(selectedSolutionInformation.getTripType())
                .build();
    }
}
