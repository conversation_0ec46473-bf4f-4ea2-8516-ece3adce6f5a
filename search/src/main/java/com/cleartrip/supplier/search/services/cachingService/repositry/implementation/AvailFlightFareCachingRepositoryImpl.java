package com.cleartrip.supplier.search.services.cachingService.repositry.implementation;

import com.cleartrip.supplier.amendsearch.models.dto.JourneyDetailDto;
import com.cleartrip.supplier.amendsearch.models.dto.JourneyMetaInfoDto;
import com.cleartrip.supplier.config_manager.SearchConfigContainer;
import com.cleartrip.supplier.inventory.protos.v1.FareCategoryType;
import com.cleartrip.supplier.search.enums.Supplier;
import com.cleartrip.supplier.search.models.DTO.FlightSegmentDTO;
import com.cleartrip.supplier.search.models.DTO.FlightSolutionDTO;
import com.cleartrip.supplier.search.models.DTO.SolutionMetaInfoDTO;
import com.cleartrip.supplier.search.models.FlightSegmentIdDTO;
import com.cleartrip.supplier.search.models.cache.avail.*;
import com.cleartrip.supplier.search.repository.impls.TTL;
import com.cleartrip.supplier.search.services.cachingService.AvailabilityFareCacheDataConverterInput;
import com.cleartrip.supplier.search.services.cachingService.repositry.AvailFaresCachingRepository;
import com.cleartrip.supplier.search.services.cachingService.repositry.AvailFlightsCachingRepository;
import com.cleartrip.supplier.search.services.cachingService.repositry.AvailabilityCachingRepository;
import com.cleartrip.supplier.search.services.cachingService.repositry.AvailabilityFareFamilyCachingRepository;
import com.cleartrip.supplier.search.services.cachingService.ttl.TTLService;
import com.cleartrip.supplier.search.services.cachingService.ttl.impl.TTLRequest;
import com.cleartrip.supplier.search.services.cachingService.ttl.impl.TTLRequestV3;
import com.cleartrip.supplier.search.services.dataPublishingService.SearchDataPublishingService;
import com.cleartrip.supplier.search.util.AvailabilityUtil;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class AvailFlightFareCachingRepositoryImpl implements
        AvailabilityCachingRepository,
        AvailabilityFareFamilyCachingRepository {
    private final AvailFaresCachingRepository fareCachingRepository;
    private final AvailFlightsCachingRepository flightsCachingRepository;
    private final SearchConfigContainer properties;
    private final TTLService<TTLRequestV3> hardExpiryService;
    private final SearchDataPublishingService searchDataPublishingService;
    private final List<String> SFF_SUPPLIER_LIST;


    @Inject
    public AvailFlightFareCachingRepositoryImpl(
            @Named("AvailFaresCachingRepositoryImpl") AvailFaresCachingRepository fareCachingRepository,
            @Named("AvailFlightCachingRepositoryImpl") AvailFlightsCachingRepository flightsCachingRepository,
            SearchConfigContainer properties,
            @Named("HardExpiryTTLServiceV3") TTLService<TTLRequestV3> hardExpiryService,
            @Named("SearchDataPublishingServiceImpl") SearchDataPublishingService searchDataPublishingService) {

        this.fareCachingRepository = fareCachingRepository;
        this.flightsCachingRepository = flightsCachingRepository;
        this.properties = properties;
        this.hardExpiryService = hardExpiryService;
        this.searchDataPublishingService = searchDataPublishingService;
        this.SFF_SUPPLIER_LIST = initializeSffSupplierList();
    }

    private List<String> initializeSffSupplierList() {
        return List.of( "TRIPJACK"); //TODO GET FROM CONFIG
    }

    @Override
    public AvailabilityCacheOutput getAvailableFlightSolutions(String searchCriteriaId) {
        return fareCachingRepository.getAvailabilitySolutionFares(searchCriteriaId);
    }

    private AvailabilityFlightCache prepareFlightCache(Map<String, FlightSolutionDTO> flightSolutionDTOMap) {
        Map<String, FlightSegmentDTO> flightSegmentDTOMap = flightSolutionDTOMap.values().stream()
                .filter(flightSolutionDTO -> Optional.ofNullable(flightSolutionDTO)
                        .map(FlightSolutionDTO::getSolutionMetaInfoDTO)
                        .map(SolutionMetaInfoDTO::getSupplierInfoDTO)
                        .map(infoDTO -> !SFF_SUPPLIER_LIST.contains(infoDTO.getSupplier())).orElse(true))
                .flatMap(sol -> sol.getFlightDTO().stream()
                        .flatMap(flt -> flt.getFlightSegmentDTOList().stream()))
                .collect(Collectors.toMap(FlightSegmentIdDTO::getSegmentId,
                        FlightSegmentIdDTO::getFlightSegmentDTO,
                        (seg1, seg2) -> seg1));
        return AvailabilityFlightCache.builder()
                .segmentDTOMap(flightSegmentDTOMap)
                .build();
    }

    @Override
    public void storeFlightSolutions(String searchCriteriaId, AvailabilityCacheInput availabilityCacheInput, AvailCacheTTL availCacheTTL) {
        try {
            AvailabilityFlightCache availabilityFlightCache = prepareFlightCache(availabilityCacheInput.getFlightSolutionDTOS());
            availabilityFlightCache.getSegmentDTOMap().forEach((key, value) -> {
                TTLRequest ttlRequest = TTLRequest.builder()
                        .carrier(value.getSegmentFlightDetailsDTO().getAirline())
                        .to(value.getArrivalToStop().getAirport())
                        .from(value.getDepartFromStop().getAirport())
                        .departDate(value.getDepartFromStop().getTime())
                        .intl(availabilityCacheInput.getTaskAvailSearchRequest().getFlightSearchCriteriaDTO().isIntl())
                        .supplier(availabilityCacheInput.getFlightSolutionDTOS().values().stream()
                                .findFirst().get().getSolutionMetaInfoDTO().getSupplierInfoDTO()
                                .getSupplier())
                        .build();
                TTLRequestV3 ttlRequestV3 = AvailabilityUtil.getTtlRequestV2FromTtlRequest(ttlRequest,availabilityCacheInput.getTaskAvailSearchRequest().getCompanyInfoDTO().getCompanyId(),availabilityCacheInput.getTaskAvailSearchRequest().getFlightSearchCriteriaDTO().isRoundTrip());
                TTL fltTTL = hardExpiryService.getHardTTL(ttlRequestV3);
                if (Supplier.INDIGO.equals(availabilityCacheInput.getTaskAvailSearchRequest().getSupplierCarrierPair().getSupplier())) {
                    fltTTL = new TTL(3, TimeUnit.DAYS);
                }
                flightsCachingRepository.storeAvailabilityFlightSegment(key, value, fltTTL);
            });
            fareCachingRepository.storeAvailabilitySolutionFares(searchCriteriaId, availabilityCacheInput, availCacheTTL.getHotCacheTTL());
        } finally {
            searchDataPublishingService.publishSearchData(availabilityCacheInput);
        }

    }

    @Override
    public void storeFlightSolutions(final FareFamilyAvailabilityCacheInput input) {
        AvailabilityFlightCache availabilityFlightCache = prepareFlightCache(input.getFlightSolutionDTO());
        availabilityFlightCache.getSegmentDTOMap().forEach((key, value) -> {
            TTLRequest ttlRequest = TTLRequest.builder()
                    .carrier(value.getSegmentFlightDetailsDTO().getAirline())
                    .to(value.getArrivalToStop().getAirport())
                    .from(value.getDepartFromStop().getAirport())
                    .departDate(value.getDepartFromStop().getTime())
                    .intl(input.isIntl())
                    .supplier(input.getSupplier())
                    .build();
            TTLRequestV3 ttlRequestV3 = AvailabilityUtil.getTtlRequestV2FromTtlRequest(ttlRequest,input.getCompanyId());
            TTL fltTTL = hardExpiryService.getHardTTL(ttlRequestV3); //TODO CHECK
            flightsCachingRepository.storeAvailabilityFlightSegment(key, value, fltTTL);
        });
        fareCachingRepository.storeFareFamilyFares(getFFRequest(input.getFlightSolutionDTO().values().stream().findFirst().get(), input.getFareCategoryType()));
    }

    @Override
    public void storeFlightSolutions(AmendAvailabilityCacheInput input) {
        AvailabilityFlightCache availabilityFlightCache = prepareFlightCache(input.getFlightSolutionDTOS());
        availabilityFlightCache.getSegmentDTOMap().forEach((key, value) -> {
            TTLRequest ttlRequest = TTLRequest.builder()
                    .carrier(value.getSegmentFlightDetailsDTO().getAirline())
                    .to(value.getArrivalToStop().getAirport())
                    .from(value.getDepartFromStop().getAirport())
                    .departDate(value.getDepartFromStop().getTime())
                    .intl(input.getAmendSearchRepositoryRequest().isIntl())
                    .supplier(input.getFlightSolutionDTOS().values().stream()
                            .findFirst().get().getSolutionMetaInfoDTO().getSupplierInfoDTO()
                            .getSupplier())
                    .build();
            TTLRequestV3 ttlRequestV3 = AvailabilityUtil.getTtlRequestV2FromTtlRequest(ttlRequest,"",input.getAmendSearchRepositoryRequest().getTripIdentifier().isSplRt());
            TTL fltTTL = hardExpiryService.getHardTTL(ttlRequestV3);
            String supplier = input.getAmendSearchRepositoryRequest().getJourneyDetailsList().stream().map(JourneyDetailDto::getJourneyMetaInfo)
                    .findFirst().map(JourneyMetaInfoDto::getSupplier).orElse(StringUtils.EMPTY);

            if (Supplier.INDIGO.name().equals(supplier)) {
                fltTTL = new TTL(3, TimeUnit.DAYS);
            }
            flightsCachingRepository.storeAvailabilityFlightSegment(key, value, fltTTL);
        });
        final TTL ttl = new TTL(60, TimeUnit.MINUTES);
        fareCachingRepository.storeAmendSolutionFares(input.getAmendSearchCriteriaId(), input, ttl);
    }

    private AvailabilityFareCacheDataConverterInput getFFRequest(FlightSolutionDTO fareFamilySolution, FareCategoryType fareCategoryType) {
        Map<String, FlightSolutionDTO> flightSolutionDTOMap = new HashMap<>() {{
            put(fareFamilySolution.getSolutionId(), fareFamilySolution);
        }};
        String supplier = fareFamilySolution.getSolutionMetaInfoDTO().getSupplierInfoDTO().getSupplier();
        return AvailabilityFareCacheDataConverterInput.builder()
                .fareFamilySolution(flightSolutionDTOMap)
                .fareFamilySolutionId(fareFamilySolution.getSolutionId())
                .supplierId(Supplier.valueOf(supplier).getSupplierId())
                .fareCategoryType(fareCategoryType)
                .build();
    }

    private int getDx(Supplier supplier) {
        if (Supplier.INDIGO.equals(supplier)) {
            return 3;
        }
        return 2;
    }

    public void storeFlightSolutionsFare(String searchCriteriaId, AvailabilityCacheInput availabilityCacheInput, TTL ttl) {

        fareCachingRepository.storeAvailabilitySolutionFares(searchCriteriaId, availabilityCacheInput, ttl);
    }

    @Override
    public AvailabilityCacheOutput getFlightSolutions(String searchCriteriaId, List<String> flightIds) {
        /*No fallback needed no usage as of now*/
        return fareCachingRepository.getAvailabilitySolutionFares(searchCriteriaId, flightIds);
    }

    @Override
    public AvailabilityCacheOutput getFareFamilySolution(String fareFamilyKey) {
        return fareCachingRepository.getAvailabilitySolutionFares(fareFamilyKey);
    }

    @Override
    public AvailabilityCacheOutput getFlightSolutionWithFare(String searchCriteriaId, String flightId, String comboFbc) {
        return fareCachingRepository.getAvailabilitySolutionFare(searchCriteriaId, flightId, comboFbc);
    }

    /*@Override
    public AvailabilityFareCacheData getCacheFlightSolutionWithFare(String searchCriteriaId) {
        return fareCachingRepository.getCacheAvailabilitySolutionFares(searchCriteriaId);
    }*/

    @Override
    public Boolean clearFlightSolutions(String searchCriteriaId) {
        return fareCachingRepository.clearAvailabilitySolutionFares(searchCriteriaId);
    }

    @Override
    public String getCachingString(String searchCriteriaId) {
        return searchCriteriaId;
    }

}
