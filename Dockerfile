FROM mecleartrip/javabasenew:alpine_jre11_0.4_v1
WORKDIR /opt/app
ENV NLS_LANG="AMERICAN_AMERICA.UTF8"
ENV LC_ALL="en_US.UTF-8"
COPY java_supervisord.conf /etc/supervisord.conf
COPY filebeat /etc/filebeat/
RUN chmod go-w /etc/filebeat/filebeat.yml
RUN apk add bash
COPY newrelic /opt/newrelic
COPY grpc-client-cli /opt/app
RUN mkdir -p /opt/app/v1/service/
COPY api/src/main/resources/proto/v1 /opt/app/v1
COPY api/src/main/resources/proto/cache /opt/app/cache
ADD  app.jar  /opt/app
RUN mkdir /var/log/supply-core
COPY search/src/main/resources/applications-prod-properties.json  search/src/main/resources/applications-prod-properties.json
COPY search/src/main/resources/applications-staging-properties.json  search/src/main/resources/applications-staging-properties.json
COPY credential/src/main/resources/applications-qa-properties.json  credential/src/main/resources/applications-qa-properties.json
COPY credential/src/main/resources/applications-prod-properties.json  credential/src/main/resources/applications-prod-properties.json
COPY credential/src/main/resources/applications-staging-properties.json  credential/src/main/resources/applications-staging-properties.json
COPY OnD/src/main/resources/applications-prod-properties.json  OnD/src/main/resources/applications-prod-properties.json
COPY OnD/src/main/resources/applications-staging-properties.json  OnD/src/main/resources/applications-staging-properties.json
COPY book/src/main/resources/application-qa-properties.json  book/src/main/resources/application-qa-properties.json
COPY book/src/main/resources/application-prod-properties.json  book/src/main/resources/application-prod-properties.json
COPY book/src/main/resources/application-staging-properties.json  book/src/main/resources/application-staging-properties.json

# #added these commands for chaos testing for book reconciliation flow
# RUN apk add py-pip
# RUN pip install flask
# RUN pip install schedule
# COPY book/src/main/resources/chaos-testing-script.py chaos-testing-script.py
# COPY book/src/main/resources/scheduler-reconciliation-script.py scheduler-reconciliation-script.py
# COPY book/src/main/resources/poll-book-scheduler-script.py poll-book-scheduler-script.py
# ###################################################
COPY Benefits/src/main/resources/openl/qa/fareRule_45ddfbb52754f895c8d09635ce630332.xlsx  common/src/main/resources/openl/qa/fareRule_45ddfbb52754f895c8d09635ce630332.xlsx

COPY Benefits/src/main/resources/openl/qa/fareRule_8607027e78cebea8c3d82c91de486b87.xlsx Benefits/src/main/resources/openl/qa/fareRule_8607027e78cebea8c3d82c91de486b87.xlsx
COPY miniRules/src/main/resources/openl/qa/fareRule_8607027e78cebea8c3d82c91de486b87.xlsx miniRules/src/main/resources/openl/qa/fareRule_8607027e78cebea8c3d82c91de486b87.xlsx
COPY Benefits/src/main/resources/openl/qa/fareBenefitRule_4d47ccc4162c8f0d3982818203e8d3d.xlsx Benefits/src/main/resources/openl/qa/fareBenefitRule_4d47ccc4162c8f0d3982818203e8d3d.xlsx
COPY search/src/main/resources/openl/qa/fareBenefitRule_4d47ccc4162c8f0d3982818203e8d3d.xlsx search/src/main/resources/openl/qa/fareBenefitRule_4d47ccc4162c8f0d3982818203e8d3d.xlsx
COPY Benefits/src/main/resources/openl/qa/baggageSourceAndConfigRules-prod_c4d565ca859c6d8ccd9f43f290852720.xlsx Benefits/src/main/resources/openl/qa/baggageSourceAndConfigRules-prod_c4d565ca859c6d8ccd9f43f290852720.xlsx
COPY search/src/main/resources/openl/qa/baggageSourceAndConfigRules-prod_c4d565ca859c6d8ccd9f43f290852720.xlsx search/src/main/resources/openl/qa/baggageSourceAndConfigRules-prod_c4d565ca859c6d8ccd9f43f290852720.xlsx
COPY ancillary/src/main/resources/openl/qa/ancillaryRules-prod_4758af7bd80b1dc2ee92ef41836d7255.xlsx ancillary/src/main/resources/openl/qa/ancillaryRules-prod_4758af7bd80b1dc2ee92ef41836d7255.xlsx
COPY book/src/main/resources/openl/SffCtcThresholdRules-prod_8e2798baeb249c3d0df3e858ac77bdb5.xlsx book/src/main/resources/openl/SffCtcThresholdRules-prod_8e2798baeb249c3d0df3e858ac77bdb5.xlsx
COPY book/src/main/resources/openl/optimiserEligibilityRules_b11223a73b8285fcf096a05190c958af.xlsx book/src/main/resources/openl/optimiserEligibilityRules_b11223a73b8285fcf096a05190c958af.xlsx
COPY search/src/main/resources/openl/qa/PromoCodeRules-prod_69059c95d505767953644512f01aba74.xlsx search/src/main/resources/openl/qa/PromoCodeRules-prod_69059c95d505767953644512f01aba74.xlsx
COPY search/src/main/resources/openl/qa/companyConfigRules-prod_5c710a3a29be513388563f1bc865d350.xlsx search/src/main/resources/openl/qa/companyConfigRules-prod_5c710a3a29be513388563f1bc865d350.xlsx
COPY book/src/main/resources/openl/companyConfigRules-prod_5c710a3a29be513388563f1bc865d350.xlsx book/src/main/resources/openl/companyConfigRules-prod_5c710a3a29be513388563f1bc865d350.xlsx
COPY search/src/main/resources/openl/qa/AmendSearchRuleEngine-prod_14f39180aab15acd0266119a10ab69ce.xlsx search/src/main/resources/openl/qa/AmendSearchRuleEngine-prod_14f39180aab15acd0266119a10ab69ce.xlsx
COPY search/src/main/resources/openl/qa/additionalSupplierRulesV2-prod_a7995d63aaad4506e70b51072789e721.xlsx search/src/main/resources/openl/qa/additionalSupplierRulesV2-prod_a7995d63aaad4506e70b51072789e721.xlsx
COPY search/src/main/resources/openl/qa/FareBrandProductClassUniverse-prod_fe200e8fdab6a870d4ceea2f85af533e.xlsx search/src/main/resources/openl/qa/FareBrandProductClassUniverse-prod_fe200e8fdab6a870d4ceea2f85af533e.xlsx
COPY search/src/main/resources/openl/qa/ctcConfigRuleEngineBeta-prod_4eede4d0ccaeac6df9018f490ebeed27.xlsx search/src/main/resources/openl/qa/ctcConfigRuleEngineBeta-prod_4eede4d0ccaeac6df9018f490ebeed27.xlsx
COPY search/src/main/resources/openl/qa/CtcConfigRules-prod_b0913417c7314e23dec3f8fb8728219c.xlsx search/src/main/resources/openl/qa/CtcConfigRules-prod_b0913417c7314e23dec3f8fb8728219c.xlsx
COPY search/src/main/resources/openl/qa/revenueConfigRules-prod_d714a6ca838a6b51528785d6ce7764ee.xlsx search/src/main/resources/openl/qa/revenueConfigRules-prod_d714a6ca838a6b51528785d6ce7764ee.xlsx
COPY search/src/main/resources/openl/qa/fareLoggingRules-prod_118620fa75304096f18ad7084bbdf1fc.xlsx search/src/main/resources/openl/qa/fareLoggingRules-prod_118620fa75304096f18ad7084bbdf1fc.xlsx
COPY search/src/main/resources/openl/qa/supplierEligibilityConfig-prod_d20d0042f87409db941841784898c1a8.xlsx search/src/main/resources/openl/qa/supplierEligibilityConfig-prod_d20d0042f87409db941841784898c1a8.xlsx
COPY search/src/main/resources/openl/qa/cacheConfig-prod_db65dcf8e96625fde1b655ea479a96b8.xlsx search/src/main/resources/openl/qa/cacheConfig-prod_db65dcf8e96625fde1b655ea479a96b8.xlsx
COPY search/src/main/resources/openl/qa/cacheExpiryEngineRules-prod_dd80697ea514d32c9e837e1ff96cf94a.xlsx search/src/main/resources/openl/qa/cacheExpiryEngineRules-prod_dd80697ea514d32c9e837e1ff96cf94a.xlsx
COPY search/src/main/resources/openl/qa/SffBookTaskIdentificationRules-prod_80e3e5d814442dbd6065390785e8e1ae.xlsx search/src/main/resources/openl/qa/SffBookTaskIdentificationRules-prod_80e3e5d814442dbd6065390785e8e1ae.xlsx
COPY search/src/main/resources/openl/qa/fareBrandRules-prod_bab3fcb6750d46b7a2e312c4c1b39b66.xlsx search/src/main/resources/openl/qa/fareBrandRules-prod_bab3fcb6750d46b7a2e312c4c1b39b66.xlsx
COPY search/src/main/resources/openl/qa/fareBrandDetailsRules-prod_f86f43caab270b8e79728385ef3d29a6.xlsx search/src/main/resources/openl/qa/fareBrandDetailsRules-prod_f86f43caab270b8e79728385ef3d29a6.xlsx
COPY search/src/main/resources/openl/qa/ApplicableFareBrandPromoCodeDetails-prod_6ce0a9f3f6a9e4b63940bbf50b091cff.xlsx search/src/main/resources/openl/qa/ApplicableFareBrandPromoCodeDetails-prod_6ce0a9f3f6a9e4b63940bbf50b091cff.xlsx
COPY search/src/main/resources/openl/qa/productClassCombinabilityRules-prod_3f02f9933e6c5e2aa40475d98a006a4d.xlsx search/src/main/resources/openl/qa/productClassCombinabilityRules-prod_3f02f9933e6c5e2aa40475d98a006a4d.xlsx
COPY search/src/main/resources/openl/qa/airlineSpecificConfigRules-prod_3062dea0ab3196202542f261052bf532.xlsx search/src/main/resources/openl/qa/airlineSpecificConfigRules-prod_3062dea0ab3196202542f261052bf532.xlsx
COPY search/src/main/resources/openl/qa/FlightPreviewStrategyRules-prod_fb03e122498f5c56aaee5611dfabc898.xlsx search/src/main/resources/openl/qa/FlightPreviewStrategyRules-prod_fb03e122498f5c56aaee5611dfabc898.xlsx
COPY Benefits/src/main/resources/openl/qa/fareRule_V3.xlsx Benefits/src/main/resources/openl/qa/fareRule_V3.xlsx
COPY miniRules/src/main/resources/openl/qa/fareRule_V3.xlsx miniRules/src/main/resources/openl/qa/fareRule_V3.xlsx
# COPY Benefits/src/main/resources/openl/qa/fareBenefitRule_V2.xlsx Benefits/src/main/resources/openl/qa/fareBenefitRule_V2.xlsx
# COPY search/src/main/resources/openl/qa/fareBenefitRule_V2.xlsx search/src/main/resources/openl/qa/fareBenefitRule_V2.xlsx
COPY Benefits/src/main/resources/openl/qa/fareBenefitRule_V3.xlsx Benefits/src/main/resources/openl/qa/fareBenefitRule_V3.xlsx
COPY search/src/main/resources/openl/qa/fareBenefitRule_V3.xlsx search/src/main/resources/openl/qa/fareBenefitRule_V3.xlsx

COPY Benefits/src/main/resources/openl/qa/fareRule_V4.xlsx Benefits/src/main/resources/openl/qa/fareRule_V4.xlsx
COPY miniRules/src/main/resources/openl/qa/fareRule_V4.xlsx miniRules/src/main/resources/openl/qa/fareRule_V4.xlsx

COPY Benefits/src/main/resources/openl/qa/fareRule_V5.xlsx Benefits/src/main/resources/openl/qa/fareRule_V5.xlsx
COPY miniRules/src/main/resources/openl/qa/fareRule_V5.xlsx miniRules/src/main/resources/openl/qa/fareRule_V5.xlsx
EXPOSE 9080
EXPOSE 8000
CMD ["/usr/bin/supervisord"]