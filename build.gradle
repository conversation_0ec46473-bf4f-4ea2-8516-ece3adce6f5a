def jacksonVersion = '2.15.3'
allprojects {
    apply plugin: 'java'
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11
    group = 'com.cleartrip.supplier'
    dependencies {
        implementation 'org.jetbrains:annotations:24.0.1'
        implementation group: 'org.cleartrip.utility', name: 'configuration-manager', version: '1.6-SNAPSHOT'
        testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
        testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
        testImplementation group: 'org.mockito', name: 'mockito-all', version: '1.10.19'

        implementation "com.fasterxml.jackson.module:jackson-module-scala_2.13:${jacksonVersion}"
        implementation "com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}"

        implementation "io.dropwizard:dropwizard-core:3.0.0",
                "io.dropwizard:dropwizard-assets:3.0.0",
                "io.dropwizard:dropwizard-migrations:3.0.0",
                "io.dropwizard:dropwizard-forms:3.0.0"
        implementation 'ru.vyarus:dropwizard-guicey:6.0.0'
        implementation group: 'io.dropwizard', name: 'dropwizard-hibernate', version: '3.0.0'

        implementation "com.squareup.retrofit2:retrofit:2.9.0"
        implementation "com.squareup.retrofit2:converter-jackson:2.9.0"
        implementation "com.squareup.retrofit2:converter-scalars:2.5.0"
        implementation "com.squareup.retrofit2:converter-protobuf:2.9.0"

        compileOnly("org.projectlombok:lombok:1.18.26")
        annotationProcessor("org.projectlombok:lombok:1.18.26")
        implementation group: 'com.mysql', name: 'mysql-connector-j', version: '8.0.33'

        implementation ("com.cleartrip.air.sms:api:${SMS_API_VERSION}") {
            force = true
            exclude group: 'com.cleartrip.air.config.management'
        }
    }

    repositories {
        mavenCentral()
        mavenLocal()
        maven {
            allowInsecureProtocol = true
            url "${ARTIFACTORY_URL}/${ARTIFACTORY_REPO}"
            credentials {
                username = "${ARTIFACTORY_USERNAME}"
                password = "${ARTIFACTORY_PASSWORD}"
            }
            name = "artifactory"
        }
        maven { url "https://repo.spring.io/snapshot" }
        maven { url "https://repo.spring.io/milestone" }
    }
    test {
        useJUnitPlatform()
    }
}





