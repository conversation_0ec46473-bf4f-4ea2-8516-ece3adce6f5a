logging:
  appenders:
    - type: file
      maxFileSize: 100mb
      threshold: ALL
      archivedLogFilenamePattern: "/var/log/tomcat7/app-%d{yyyy-MM-dd-HH}-%i.log.gz"
      logFormat: |-
        %highlight(%-5level) [%d{ISO8601}] [%X{reqId} %X{txnId}] [%cyan(%logger{0})]: %m
        %ex
      currentLogFilename: "/var/log/tomcat7/app.log"
      archivedFileCount: 15
    - type: console
      threshold: ALL
      logFormat: |-
        %highlight(%-5level) [%d{ISO8601}] [%X{reqId} %X{txnId}] [%cyan(%logger{0})]: %m
        %ex
  level: INFO
  loggers:
    org.hibernate.ejb.HibernatePersistence: ERROR
    com.cleartrip.supplier: DEBUG
server:
  maxThreads: 150
  requestLog:
    appenders:
      - logFormat: |-
          %-5p [%d{ISO8601}] [%X{reqId} %X{txnId}] %c{1.}: %m
          %ex
        archivedLogFilenamePattern: "/var/log/tomcat7/requests-%d.log.gz"
        type: file
        archivedFileCount: 5
        archive: true
        currentLogFilename: "/var/log/tomcat7/requests.log"
  adminConnectors:
    - type: http
      port: 9081
  idleThreadTimeout: 200s
  shutdownGracePeriod: 5s
  applicationConnectors:
    - port: 9080
      type: http
  gzip:
    enabled: true
  minThreads: 8

tripServiceConfiguration:
  name: "trip service"
  tripDetailsClientConfiguration:
    tripDetailsEndPoint: "http://trip-service-api.cltp.com:9001"
    connectTimeout: 4000
    readTimeout: 4000
    overallCallTimeout: 4000
    writeTimeout: 4000

  tripUpdateClientConfiguration:
    tripUpdateEndpoint: "http://trip-service-api.cltp.com:9001"
    connectTimeout: 4000
    readTimeout: 4000
    overallCallTimeout: 4000
    writeTimeout: 4000

  tripCloseTxnClientConfiguration:
    tripCloseTxnEndpoint: "http://trip-service-api.cltp.com:9001"
    connectTimeout: 4000
    readTimeout: 4000
    overallCallTimeout: 4000
    writeTimeout: 4000

bookConfiguration:
  name: "IBS"
  bookingThreadPoolConfiguration:
    corePoolSize: 50
    maxPoolSize: 100
    workerQueueSize: 200
  ticketingThreadPoolConfiguration:
    corePoolSize: 5
    maxPoolSize: 10
    workerQueueSize: 20
  smsBookClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
    asyncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 60000
      readTimeout: 60000
      overallCallTimeout: 60000
      writeTimeout: 60000
  smsExtendSessionClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
    asyncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
  vasClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://vasservice.cltp.com:9001"
      connectTimeout: 20000
      readTimeout: 20000
      overallCallTimeout: 20000
      writeTimeout: 20000
    asyncFlowClientConfiguration:
      endPoint: "http://vasservice.cltp.com:9001"
      connectTimeout: 20000
      readTimeout: 20000
      overallCallTimeout: 20000
      writeTimeout: 20000
  bookingClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
    asyncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
  credentialServiceClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://credentialservice.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
    asyncFlowClientConfiguration:
        endPoint: "http://credentialservice.cltp.com:9001"
        connectTimeout: 30000
        readTimeout: 30000
        overallCallTimeout: 30000
        writeTimeout: 30000
  bookingInfoClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
    asyncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
  ticketClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 120000
      readTimeout: 120000
      overallCallTimeout: 120000
      writeTimeout: 120000
    asyncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 120000
      readTimeout: 120000
      overallCallTimeout: 120000
      writeTimeout: 120000
  handlePendingTripClientConfiguration:
    handlePendingTripEndpoint: "http://supply-core-book.cltp.com:9001"
    connectTimeout: 4000
    readTimeout: 4000
    overallCallTimeout: 4000
    writeTimeout: 4000
  getBookingFromStateClientConfiguration:
    syncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
    asyncFlowClientConfiguration:
      endPoint: "http://air-sms.cltp.com:9001"
      connectTimeout: 30000
      readTimeout: 30000
      overallCallTimeout: 30000
      writeTimeout: 30000
  pollBookingLoopBackClientConfiguration:
    pollBookingLoopBackClientEndpoint: "http://supply-core-book.cltp.com:9001"
    connectTimeout: 20000
    readTimeout: 20000
    overallCallTimeout: 20000
    writeTimeout: 20000
  tripCloseTxnClientConfiguration:
    tripCloseTxnEndpoint: "http://distribution-core-hold.cltp.com:9001"
    connectTimeout: 4000
    readTimeout: 4000
    overallCallTimeout: 4000
    writeTimeout: 4000
  monitoringConfiguration:
    newRelic:
      tableName: IBS_METRICS_QA
      burnAmountTable: BurnAmountData
      sffMetricsTable: sssNewRelicMetric
      sffAirBookingInitJourneyTable: air_booking_init_journey_event_test
      bookOptimisationTable: ct_sms_book_optimiser
    stats:
      topicName: air
  configServiceSQLConfiguration:
    url: "jdbc:mysql://************:3306/ct_air_configmanager_schema?useSSL=false"
    userName: "config_manager"
    password: "7TH1QlW0xX$"
  database:
    # the name of your JDBC driver
    driverClass: com.mysql.cj.jdbc.Driver

    #  # the username
    user: kaizen_user
    #x
    #  # the password
    password: yP*25Z824w
    # the JDBC URL
    url: *****************************************************************************

    # any properties specific to your JDBC driver:
    properties:
      charSet: UTF-8
      hibernate.dialect: org.hibernate.dialect.MySQL8Dialect

    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s

    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyApplication Health Check */ SELECT 1"

    # the minimum number of connections to keep open
    minSize: 15

    # the maximum number of connections to keep open
    maxSize: 32

    # whether or not idle connections should be validated
    checkConnectionWhileIdle: false
    checkConnectionOnBorrow: true
  distributionCoreClientConfiguration:
    endPoint: "http://distribution-core-hold.cltp.com:9001"
    connectTimeout: 30000
    readTimeout: 30000
    overallCallTimeout: 30000
    writeTimeout: 30000
  themisClientConfig:
    themisUrl: "http://10.171.0.226:9001"
    themisGcsBucket: "me-qa-themis-resource-data"
  smsBookOptimisationClientConfiguration:
    endPoint: "http://air-sms.cltp.com:9001"
    connectTimeout: 120000
    readTimeout: 120000
    overallCallTimeout: 120000
    writeTimeout: 120000
  instrumentationConfiguration:
    bigQueryInstrumentationEnabled: true
    bigQueryBrokerList: kafka4data1.cltp.com:9092, kafka4data2.cltp.com:9092, kafka4data3.cltp.com:9092
    newRelicInstrumentationEnabled: true
    newRelicAccountId: 2352060
    newRelicAPIKey: f0fe7d4935efbfceef3b0305adfda2b2df8876ae
    newRelicDrainIntervalSeconds: 60
    sffInstrumentationDataTopic: sff-instrumentation-data
    sffSupplierRevenueDataTopic: sff-supplier-revenue
    supplierBookingEventDataTopic: air.supply-core.supplierbookingevent
    requestTimeout: 15000
    transactionTimeout: 5000
    messageTimeout: 5000
    socketTimeout: 5000
  modifyPnrClientConfiguration:
    endPoint: "http://air-sms.cltp.com:9001"
    connectTimeout: 120000
    readTimeout: 120000
    overallCallTimeout: 120000
    writeTimeout: 120000


searchConfiguration:
  searchConf: "search"
  ffEnabledSuppliers: ["FLY_DUBAI" ]
  companyResourceFilePath: "/openl/qa/companyConfig/companyConfigRules-qa.xlsx"
  commonRulesFilePath: "/openl/qa/common/rules-qa.xlsx"
  brandBenefitRulesFilePath: "/openl/qa/brandBenefitRule/brandBenefitRules-qa.xlsx"
  amendSearchRulesFilePath: "/openl/qa/amendSearch/amendSearchRules-qa.xlsx"
  mySqlConf:
    url: "***********************************************************************************"
    userName: "config_manager"
    password: "7TH1QlW0xX$"
  kafkaConf:
    brokerList: "qa2-kafka-platform.cltp.com:9092"
    requestTimeout: 15000
    transactionTimeout: 5000
    messageTimeout: 5000
    socketTimeout: 5000
    maxMessageSize: 10485880
  monitoringParam:
    newRelic:
      tableName: ctSupplyCoreTest
    stats:
      topicName: air
  baggageThreadPoolConfiguration:
    corePoolSize: 50
    maxPoolSize: 100
    workerQueueSize: 200
holdConfiguration:
  monitoringParam:
    newRelic:
      tableName: ctSupplyCoreTest
    stats:
      topicName: air
  mongoDBConnectionParam:
    uri: "mongodb://qa2-mongo-1.cltp.com,qa2-mongo-2.cltp.com"
    connectTimeout: 500
    serverSelectionTimeout: 500
    database: "supplyCoreDB"
    collectionName: "Journeys"
  smsDns: "http://air-sms.cltp.com:9001/"
  retrofitClientParam:
    poolConfiguration:
      keepAliveTimeInMillis: 2000
      maxIdleConnections: 2000
      maxRequests: 400
      maxRequestsPerHost: 200
    timeoutConfiguration:
      connectTimeoutInMillis: 50000
      overallCallTimeoutInMillis: 50000
      readTimeoutInMillis: 50000
      writeTimeoutInMillis: 50000
configManagerConfiguration:
  configManagerConf: "config manager"
commonConfiguration:
  monitoringConfig:
    newRelicAccountId: 2352060
    newRelicApiKey: f0fe7d4935efbfceef3b0305adfda2b2df8876ae
    statsBrokerList: kafka4data1.cltp.com:9092,kafka4data2.cltp.com:9092,kafka4data3.cltp.com:9092
    newRelicDrainIntervalSeconds: 60
  eventPublisherConfiguration:
    batchSize: 1000
    threadPoolSize: 10
    maxQueueSize: 10000
    maxRetryCount: 5
    batchIntervalTime: 2000
    connectionTimeoutMillis: 2000
    socketTimeoutMillis: 5000
    isNREnabled: true

miniRuleConfiguration:
  smsDns: "http://air-sms.cltp.com:9001/"
  retrofitClientParam:
    poolConfiguration:
      keepAliveTimeInMillis: 2000
      maxIdleConnections: 2000
      maxRequests: 400
      maxRequestsPerHost: 200
    timeoutConfiguration:
      connectTimeoutInMillis: 50000
      overallCallTimeoutInMillis: 50000
      readTimeoutInMillis: 50000
      writeTimeoutInMillis: 50000
  miniRuleCacheTTL: ********
  redisConnectionDetails:
    host: "redis-airsis.cltp.com"
    port: 6379
    redisPoolMaxIdleConnection: 20
    redisPoolMinIdleConnection: 10
    redisPoolMaxTotalConnection: 300
  credentialMap:
    GALILEO: "prod_staging_IN_minirule_galileo_5LK4_sms"
  monitoringDetails:
    newRelic:
      tableName: miniRule
    stats:
      topicName: air

benefitsConfiguration:
  sosDns: "http://supply-core.cltp.com:9090/"
  smsDns: "http://air-sms.cltp.com:9001/"
  mySqlConf:
    url: "***********************************************************************************"
    userName: "config_manager"
    password: "7TH1QlW0xX$"
  retrofitClientParam:
    poolConfiguration:
      keepAliveTimeInMillis: 2000
      maxIdleConnections: 2000
      maxRequests: 400
      maxRequestsPerHost: 200
    timeoutConfiguration:
      connectTimeoutInMillis: 50000
      overallCallTimeoutInMillis: 50000
      readTimeoutInMillis: 50000
      writeTimeoutInMillis: 50000
  miniRuleCacheTTL: ********
  redisConnectionDetails:
    host: "redis-airsis.cltp.com"
    port: 6379
    redisPoolMaxIdleConnection: 20
    redisPoolMinIdleConnection: 10
    redisPoolMaxTotalConnection: 300
  credentialMap:
    GALILEO: "prod_staging_IN_minirule_galileo_5LK4_sms"
  monitoringDetails:
    newRelic:
      tableName: miniRule
    stats:
      topicName: air
credentialConfiguration:
  configServiceSQLConfiguration:
    url: "***********************************************************************************"
    userName: "config_manager"
    password: "7TH1QlW0xX$"
  monitoringConfiguration:
    newRelic:
      tableName: BookCoreData
    stats:
      topicName: air



cancelAndRefundConfiguration:
  refundInfoClientConfiguration:
    endPoint: "http://air-sms.cltp.com:9001"
    connectTimeout: 4000
    readTimeout: 4000
    overallCallTimeout: 4000
    writeTimeout: 4000
  getBookingClientConfiguration:
    endPoint: "http://air-sms.cltp.com:9001"
    connectTimeout: 4000
    readTimeout: 4000
    overallCallTimeout: 4000
    writeTimeout: 4000
  monitoringDetails:
    stats:
      topicName: air
    newRelic:
      tableName: sisCancelAndRefund

ancillaryConfiguration:
  configServiceSQLConfiguration:
    url: "***********************************************************************************"
    userName: "config_manager"
    password: "7TH1QlW0xX$"
previewConfiguration:
  monitoringParam:
    newRelic:
      tableName: ScFlightPreviewTest
    stats:
      topicName: air